# 用户导入企业用户合作伙伴验证功能实现说明

## 功能概述

本次实现了用户导入功能中的企业用户合作伙伴验证逻辑，确保在导入企业用户时能够正确验证和设置合作伙伴相关信息。

## 需求背景

当导入用户时，如果账户类型选择了"企业用户"，则"所属合作伙伴名称"字段为必填项，并且需要验证该合作伙伴是否在数据库中存在。

## 实现内容

### 1. 合作伙伴API扩展

#### 1.1 PublicBizPartnerApi接口扩展
- **文件**: `bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/partner/PublicBizPartnerApi.java`
- **新增方法**: `getPartnerByName(String name)` - 根据合作伙伴名称查询合作伙伴信息

#### 1.2 PartnerRespDTO扩展
- **文件**: `bztmaster-module-publicbiz-api/src/main/java/cn/bztmaster/cnt/module/publicbiz/api/partner/dto/PartnerRespDTO.java`
- **新增字段**: 
  - `agencyId` - 关联机构ID
  - `agencyName` - 关联机构名称

#### 1.3 数据访问层扩展
- **文件**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/dal/mysql/partner/PartnerMapper.java`
- **新增方法**: `selectByName(String name)` - 根据名称查询合作伙伴

#### 1.4 服务层扩展
- **文件**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/service/partner/PartnerService.java`
- **新增方法**: `getPartnerByName(String name)` - 根据名称获取合作伙伴信息

- **文件**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/service/partner/impl/PartnerServiceImpl.java`
- **实现**: 根据名称查询合作伙伴的业务逻辑

#### 1.5 控制器层扩展
- **文件**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/admin/partner/PartnerController.java`
- **新增接口**: `GET /getByName` - 提供RPC调用接口

#### 1.6 转换器扩展
- **文件**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/convert/partner/PartnerConvert.java`
- **新增方法**: 各种DTO和VO之间的转换方法

### 2. 用户导入验证逻辑

#### 2.1 AdminUserServiceImpl扩展
- **文件**: `bztmaster-module-system-server/src/main/java/cn/bztmaster/cnt/module/system/service/user/AdminUserServiceImpl.java`

**新增方法**:
1. `validatePartnerForEnterpriseUser(UserImportExcelVO importUser)`
   - 验证企业用户的合作伙伴信息
   - 检查企业用户是否填写了合作伙伴名称
   - 验证合作伙伴名称是否在数据库中存在

2. `setPartnerFieldsForUser(AdminUserDO user, UserImportExcelVO importUser)`
   - 为用户设置合作伙伴相关字段
   - 从合作伙伴信息中获取partnerId、agencyId、agencyName等字段

**修改方法**:
- `importUserList()` - 在用户导入流程中集成合作伙伴验证逻辑

#### 2.2 错误码扩展
- **文件**: `bztmaster-module-system-api/src/main/java/cn/bztmaster/cnt/module/system/enums/ErrorCodeConstants.java`

**新增错误码**:
- `USER_PARTNER_NAME_REQUIRED` - 企业用户必须填写所属合作伙伴名称
- `USER_PARTNER_NOT_EXISTS` - 合作伙伴不存在
- `USER_PARTNER_QUERY_ERROR` - 查询合作伙伴信息失败

### 3. 数据结构支持

#### 3.1 用户数据对象
- **AdminUserDO**: 已包含合作伙伴相关字段（partnerId, partnerName, agencyId, agencyName）
- **UserImportExcelVO**: 已包含partnerName字段用于Excel导入

#### 3.2 账户类型枚举
- **AccountTypeEnum**: 已包含`isPartner(Integer type)`方法用于判断是否为企业用户

## 业务流程

### 用户导入验证流程

1. **基础验证**: 验证用户名、手机号、邮箱等基础信息
2. **合作伙伴验证**: 
   - 如果是企业用户（accountType = 2），检查是否填写了partnerName
   - 如果填写了partnerName，调用PublicBizPartnerApi验证合作伙伴是否存在
3. **数据设置**:
   - 如果合作伙伴验证通过，从合作伙伴信息中获取partnerId、agencyId、agencyName
   - 将这些信息设置到用户对象中
4. **数据保存**: 保存用户信息到数据库

### 验证规则

- **企业用户**: 必须填写合作伙伴名称，且合作伙伴必须在数据库中存在
- **内部员工**: 不需要填写合作伙伴信息，相关字段会被清空

## 错误处理

- 企业用户未填写合作伙伴名称 → 返回错误信息
- 合作伙伴名称不存在 → 返回错误信息
- RPC调用失败 → 记录日志并返回错误信息

## 测试

创建了单元测试文件 `AdminUserServiceImplPartnerTest.java`，包含以下测试场景：

1. 企业用户填写有效合作伙伴名称 - 导入成功
2. 企业用户未填写合作伙伴名称 - 导入失败
3. 企业用户填写无效合作伙伴名称 - 导入失败
4. 内部员工不填写合作伙伴信息 - 导入成功

## 使用说明

### Excel导入格式

在用户导入Excel中，需要包含以下列：
- 账户类型：1-内部员工，2-企业用户
- 所属合作伙伴名称：当账户类型为企业用户时必填

### API调用

新增的合作伙伴查询接口：
```
GET /admin-api/publicbiz/partner/getByName?name={合作伙伴名称}
```

## 注意事项

1. 合作伙伴名称查询时会自动去除前后空格
2. 如果RPC调用失败，会记录错误日志但不会中断整个导入流程
3. 内部员工的合作伙伴相关字段会被自动清空
4. 企业用户如果合作伙伴验证失败，该用户的导入会失败，但不影响其他用户的导入

## 相关文件清单

### 新增文件
- `AdminUserServiceImplPartnerTest.java` - 单元测试文件

### 修改文件
- `PublicBizPartnerApi.java` - 添加getPartnerByName方法
- `PartnerRespDTO.java` - 添加agencyId和agencyName字段
- `PartnerMapper.java` - 添加selectByName方法
- `PartnerService.java` - 添加getPartnerByName方法
- `PartnerServiceImpl.java` - 实现getPartnerByName方法
- `PartnerController.java` - 添加/getByName接口
- `PartnerConvert.java` - 添加转换方法
- `AdminUserServiceImpl.java` - 添加合作伙伴验证逻辑
- `ErrorCodeConstants.java` - 添加错误码常量
