# 机构管理模块

## 模块概述

本模块提供机构管理相关的功能，包括机构列表查询、审核详情查询、审核操作等功能。

## 数据库表结构

- **主表**：`publicbiz_agency` - 机构主表
- **关联表**：`publicbiz_agency_qualification` - 机构资质文件表

## 代码结构

### API模块 (bztmaster-module-publicbiz-api)

```
src/main/java/cn/bztmaster/cnt/module/publicbiz/api/employment/
├── AgencyApi.java                    # 机构管理API接口
├── dto/
│   ├── AgencyRespDTO.java           # 机构响应DTO
│   ├── AgencyPageReqDTO.java        # 机构分页查询请求DTO
│   ├── AgencyUpdateReqDTO.java      # 机构更新请求DTO
│   └── AgencyQualificationDTO.java  # 机构资质文件DTO
└── enums/
    ├── AgencyCooperationStatusEnum.java    # 机构合作状态枚举
    ├── AgencyReviewStatusEnum.java         # 机构审核状态枚举
    └── AgencyQualificationFileTypeEnum.java # 机构资质文件类型枚举
```

### Server模块 (bztmaster-module-publicbiz-server)

```
src/main/java/cn/bztmaster/cnt/module/publicbiz/
├── controller/admin/employment/
│   ├── AgencyController.java         # 机构管理Controller
│   └── vo/
│       ├── AgencyRespVO.java        # 机构响应VO
│       ├── AgencyPageReqVO.java     # 机构分页查询请求VO
│       ├── AgencyUpdateReqVO.java   # 机构更新请求VO
│       └── AgencyQualificationVO.java # 机构资质文件VO
├── dal/
│   ├── dataobject/employment/
│   │   ├── AgencyDO.java            # 机构数据对象
│   │   └── AgencyQualificationDO.java # 机构资质文件数据对象
│   └── mysql/employment/
│       ├── AgencyMapper.java        # 机构Mapper接口
│       └── AgencyQualificationMapper.java # 机构资质文件Mapper接口
├── service/employment/
│   ├── AgencyService.java           # 机构Service接口
│   └── impl/
│       └── AgencyServiceImpl.java   # 机构Service实现
├── convert/employment/
│   └── AgencyConvert.java          # 机构转换类
├── api/employment/
│   └── AgencyApiImpl.java          # 机构API实现
└── enums/
    └── ErrorCodeConstants.java      # 错误码常量
```

### 资源文件

```
src/main/resources/
└── mapper/
    ├── AgencyMapper.xml             # 机构Mapper XML映射
    └── AgencyQualificationMapper.xml # 机构资质文件Mapper XML映射
```

## 主要功能

### 1. 机构列表查询
- **接口**：`POST /publicbiz/agency/page`
- **功能**：分页查询机构列表，支持关键词搜索、状态筛选等
- **参数**：页码、每页条数、关键词、合作状态、审核状态、区县、机构编码

### 2. 机构详情查询
- **接口**：`GET /publicbiz/agency/get/{id}`
- **功能**：根据机构ID获取机构详细信息，包括资质文件列表
- **参数**：机构ID

### 3. 机构审核更新
- **接口**：`PUT /publicbiz/agency/update`
- **功能**：更新机构审核状态和审核备注
- **参数**：机构ID、审核状态、审核备注

## 数据字典

### 合作状态 (cooperation_status)
- cooperating：合作中
- suspended：已暂停
- terminated：已终止
- pending：待审核

### 审核状态 (review_status)
- pending：待审核
- approved：已通过
- rejected：已拒绝

### 文件类型 (file_type)
- business_license：营业执照
- qualification_cert：资质证书
- contract：合同文件
- human_resources：人力资源服务许可证
- opening_permit：开户许可证
- door_photo：门头照
- organizational_structure：组织机构代码证书
- id_card：法人身份证
- other：其他附件

## 使用说明

1. **启动服务**：确保数据库表已创建，启动publicbiz-server服务
2. **接口调用**：通过RESTful API调用相应的接口
3. **权限控制**：接口已配置权限控制，需要相应的权限才能访问
4. **参数校验**：所有接口都包含参数校验，确保数据正确性

## 注意事项

1. 机构编号和统一社会信用代码具有唯一性约束
2. 审核操作会记录审核人和审核时间
3. 资质文件按排序字段进行排序显示
4. 所有删除操作都是逻辑删除，不会物理删除数据

## 扩展说明

如需添加新功能，请按照以下步骤：

1. 在API模块添加新的DTO和接口方法
2. 在Server模块添加对应的VO、Service方法、Mapper方法
3. 在Controller中添加新的接口端点
4. 更新Convert类添加相应的转换方法
5. 添加相应的单元测试

## 错误码

- 1_001_001_000：机构不存在
- 1_001_001_001：机构编号已存在
- 1_001_001_002：统一社会信用代码已存在
- 1_001_001_003：审核状态错误 