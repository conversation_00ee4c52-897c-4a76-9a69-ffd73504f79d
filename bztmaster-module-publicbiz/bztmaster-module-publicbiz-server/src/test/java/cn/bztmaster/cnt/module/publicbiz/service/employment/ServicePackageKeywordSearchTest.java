package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackagePageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageRespVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 服务套餐关键字搜索功能测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class ServicePackageKeywordSearchTest {

    @Resource
    private ServicePackageService servicePackageService;

    /**
     * 测试纯数字关键字 - ID精确搜索
     */
    @Test
    public void testNumericKeywordSearch() {
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword("1"); // 纯数字

        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);
        
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        
        // 如果有结果，验证ID匹配
        if (!result.getList().isEmpty()) {
            for (ServicePackageRespVO item : result.getList()) {
                assertEquals(1L, item.getId(), "返回的套餐ID应该精确匹配");
            }
        }
    }

    /**
     * 测试非数字关键字 - 名称模糊搜索
     */
    @Test
    public void testNonNumericKeywordSearch() {
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword("保洁"); // 非数字

        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);
        
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        
        // 如果有结果，验证名称包含关键字
        if (!result.getList().isEmpty()) {
            for (ServicePackageRespVO item : result.getList()) {
                assertTrue(item.getName().contains("保洁"), 
                    "返回的套餐名称应包含关键词：" + item.getName());
            }
        }
    }

    /**
     * 测试混合字符关键字 - 名称模糊搜索
     */
    @Test
    public void testMixedKeywordSearch() {
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword("套餐123"); // 混合字符

        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);
        
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        
        // 如果有结果，验证名称包含关键字
        if (!result.getList().isEmpty()) {
            for (ServicePackageRespVO item : result.getList()) {
                assertTrue(item.getName().contains("套餐123"), 
                    "返回的套餐名称应包含关键词：" + item.getName());
            }
        }
    }

    /**
     * 测试空关键字
     */
    @Test
    public void testEmptyKeywordSearch() {
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword(""); // 空字符串

        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);
        
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        assertTrue(result.getTotal() >= 0, "总数应大于等于0");
    }

    /**
     * 测试null关键字
     */
    @Test
    public void testNullKeywordSearch() {
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword(null); // null值

        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);
        
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        assertTrue(result.getTotal() >= 0, "总数应大于等于0");
    }

    /**
     * 测试大数字关键字
     */
    @Test
    public void testLargeNumberKeywordSearch() {
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword("999999999999999999999"); // 超大数字

        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);
        
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        
        // 超大数字应该被当作名称模糊搜索处理
        if (!result.getList().isEmpty()) {
            for (ServicePackageRespVO item : result.getList()) {
                assertTrue(item.getName().contains("999999999999999999999"), 
                    "返回的套餐名称应包含关键词：" + item.getName());
            }
        }
    }

    /**
     * 测试前后有空格的数字关键字
     */
    @Test
    public void testTrimmedNumericKeywordSearch() {
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword("  123  "); // 前后有空格的数字

        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);
        
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        
        // 应该被当作ID精确搜索处理
        if (!result.getList().isEmpty()) {
            for (ServicePackageRespVO item : result.getList()) {
                assertEquals(123L, item.getId(), "返回的套餐ID应该精确匹配");
            }
        }
    }
}
