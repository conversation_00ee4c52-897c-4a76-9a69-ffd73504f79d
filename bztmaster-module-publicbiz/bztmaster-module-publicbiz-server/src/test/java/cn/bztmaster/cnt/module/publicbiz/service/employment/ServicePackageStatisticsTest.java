package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageStatisticsRespVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 服务套餐统计功能测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class ServicePackageStatisticsTest {

    @Resource
    private ServicePackageService servicePackageService;

    /**
     * 测试获取统计数据 - 不指定合作伙伴ID
     */
    @Test
    public void testGetStatisticsWithoutPartnerId() {
        // 执行统计查询
        ServicePackageStatisticsRespVO statistics = servicePackageService.getStatistics(null);

        // 验证结果
        assertNotNull(statistics, "统计结果不应为空");
        assertNotNull(statistics.getActiveCount(), "已上架数量不应为空");
        assertNotNull(statistics.getPendingCount(), "待上架数量不应为空");
        assertNotNull(statistics.getDeletedCount(), "回收站数量不应为空");
        assertNotNull(statistics.getTotalCount(), "总数量不应为空");
        assertNotNull(statistics.getStatisticsTime(), "统计时间不应为空");

        // 验证数量逻辑
        assertTrue(statistics.getActiveCount() >= 0, "已上架数量应大于等于0");
        assertTrue(statistics.getPendingCount() >= 0, "待上架数量应大于等于0");
        assertTrue(statistics.getDeletedCount() >= 0, "回收站数量应大于等于0");
        
        // 验证总数计算正确性
        Long expectedTotal = statistics.getActiveCount() + statistics.getPendingCount() + statistics.getDeletedCount();
        assertEquals(expectedTotal, statistics.getTotalCount(), "总数量应等于各状态数量之和");
    }

    /**
     * 测试获取统计数据 - 指定合作伙伴ID
     */
    @Test
    public void testGetStatisticsWithPartnerId() {
        // 使用一个示例合作伙伴ID
        Long partnerId = 1L;
        
        // 执行统计查询
        ServicePackageStatisticsRespVO statistics = servicePackageService.getStatistics(partnerId);

        // 验证结果
        assertNotNull(statistics, "统计结果不应为空");
        assertNotNull(statistics.getActiveCount(), "已上架数量不应为空");
        assertNotNull(statistics.getPendingCount(), "待上架数量不应为空");
        assertNotNull(statistics.getDeletedCount(), "回收站数量不应为空");
        assertNotNull(statistics.getTotalCount(), "总数量不应为空");
        assertNotNull(statistics.getStatisticsTime(), "统计时间不应为空");

        // 验证合作伙伴信息
        assertEquals(partnerId, statistics.getPartnerId(), "合作伙伴ID应匹配");

        // 验证数量逻辑
        assertTrue(statistics.getActiveCount() >= 0, "已上架数量应大于等于0");
        assertTrue(statistics.getPendingCount() >= 0, "待上架数量应大于等于0");
        assertTrue(statistics.getDeletedCount() >= 0, "回收站数量应大于等于0");
        
        // 验证总数计算正确性
        Long expectedTotal = statistics.getActiveCount() + statistics.getPendingCount() + statistics.getDeletedCount();
        assertEquals(expectedTotal, statistics.getTotalCount(), "总数量应等于各状态数量之和");
    }

    /**
     * 测试统计响应VO的构造方法
     */
    @Test
    public void testServicePackageStatisticsRespVO() {
        // 测试默认构造方法
        ServicePackageStatisticsRespVO vo1 = new ServicePackageStatisticsRespVO();
        assertNotNull(vo1.getStatisticsTime(), "统计时间应自动设置");

        // 测试带参数的构造方法
        ServicePackageStatisticsRespVO vo2 = new ServicePackageStatisticsRespVO(10L, 5L, 2L);
        assertEquals(10L, vo2.getActiveCount(), "已上架数量应正确设置");
        assertEquals(5L, vo2.getPendingCount(), "待上架数量应正确设置");
        assertEquals(2L, vo2.getDeletedCount(), "回收站数量应正确设置");
        assertEquals(17L, vo2.getTotalCount(), "总数量应自动计算");
        assertNotNull(vo2.getStatisticsTime(), "统计时间应自动设置");

        // 测试完整构造方法
        ServicePackageStatisticsRespVO vo3 = new ServicePackageStatisticsRespVO(
                15L, 8L, 3L, 1001L, "测试合作伙伴");
        assertEquals(15L, vo3.getActiveCount(), "已上架数量应正确设置");
        assertEquals(8L, vo3.getPendingCount(), "待上架数量应正确设置");
        assertEquals(3L, vo3.getDeletedCount(), "回收站数量应正确设置");
        assertEquals(26L, vo3.getTotalCount(), "总数量应自动计算");
        assertEquals(1001L, vo3.getPartnerId(), "合作伙伴ID应正确设置");
        assertEquals("测试合作伙伴", vo3.getPartnerName(), "合作伙伴名称应正确设置");
        assertNotNull(vo3.getStatisticsTime(), "统计时间应自动设置");
    }

    /**
     * 测试统计响应VO的setter方法
     */
    @Test
    public void testServicePackageStatisticsRespVOSetters() {
        ServicePackageStatisticsRespVO vo = new ServicePackageStatisticsRespVO();
        
        // 测试设置各个数量
        vo.setActiveCount(20L);
        vo.setPendingCount(10L);
        vo.setDeletedCount(5L);
        
        assertEquals(20L, vo.getActiveCount(), "已上架数量应正确设置");
        assertEquals(10L, vo.getPendingCount(), "待上架数量应正确设置");
        assertEquals(5L, vo.getDeletedCount(), "回收站数量应正确设置");
        assertEquals(35L, vo.getTotalCount(), "总数量应自动重新计算");
    }

    /**
     * 测试null值处理
     */
    @Test
    public void testNullValueHandling() {
        ServicePackageStatisticsRespVO vo = new ServicePackageStatisticsRespVO(null, null, null);
        
        assertEquals(0L, vo.getActiveCount(), "null值应转换为0");
        assertEquals(0L, vo.getPendingCount(), "null值应转换为0");
        assertEquals(0L, vo.getDeletedCount(), "null值应转换为0");
        assertEquals(0L, vo.getTotalCount(), "总数量应为0");
    }
}
