package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackagePageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.ServicePackageRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employment.impl.ServicePackageServiceImpl;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ServicePackageService 测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class ServicePackageServiceTest {

    @Resource
    private ServicePackageService servicePackageService;

    /**
     * 测试服务套餐分页查询 - categoryId过滤功能
     */
    @Test
    public void testGetServicePackagePageWithCategoryId() {
        // 准备测试数据
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setCategoryId(1L); // 设置分类ID过滤条件

        // 执行查询
        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        assertTrue(result.getTotal() >= 0, "总数应大于等于0");
        
        // 验证返回的数据都符合categoryId过滤条件
        for (ServicePackageRespVO item : result.getList()) {
            assertEquals(1L, item.getCategoryId(), "返回的套餐分类ID应该匹配过滤条件");
        }
    }

    /**
     * 测试服务套餐分页查询 - 不设置categoryId过滤
     */
    @Test
    public void testGetServicePackagePageWithoutCategoryId() {
        // 准备测试数据
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        // 不设置categoryId

        // 执行查询
        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        assertTrue(result.getTotal() >= 0, "总数应大于等于0");
    }

    /**
     * 测试服务套餐分页查询 - categoryId为null
     */
    @Test
    public void testGetServicePackagePageWithNullCategoryId() {
        // 准备测试数据
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setCategoryId(null); // 显式设置为null

        // 执行查询
        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        assertTrue(result.getTotal() >= 0, "总数应大于等于0");
    }

    /**
     * 测试服务套餐分页查询 - 套餐名称模糊搜索
     */
    @Test
    public void testGetServicePackagePageWithNameKeyword() {
        // 准备测试数据
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword("保洁"); // 设置非数字关键词

        // 执行查询
        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        assertTrue(result.getTotal() >= 0, "总数应大于等于0");

        // 验证返回的数据都符合名称模糊匹配条件
        for (ServicePackageRespVO item : result.getList()) {
            assertTrue(item.getName().contains("保洁"), "返回的套餐名称应包含关键词");
        }
    }

    /**
     * 测试服务套餐分页查询 - 套餐ID精确搜索
     */
    @Test
    public void testGetServicePackagePageWithIdKeyword() {
        // 准备测试数据
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword("123"); // 设置纯数字关键词

        // 执行查询
        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        assertTrue(result.getTotal() >= 0, "总数应大于等于0");

        // 验证返回的数据都符合ID精确匹配条件
        for (ServicePackageRespVO item : result.getList()) {
            assertEquals(123L, item.getId(), "返回的套餐ID应该精确匹配");
        }
    }

    /**
     * 测试服务套餐分页查询 - 混合字符关键词搜索
     */
    @Test
    public void testGetServicePackagePageWithMixedKeyword() {
        // 准备测试数据
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setKeyword("套餐123"); // 设置混合字符关键词

        // 执行查询
        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        assertTrue(result.getTotal() >= 0, "总数应大于等于0");

        // 验证返回的数据都符合名称模糊匹配条件（因为包含非数字字符）
        for (ServicePackageRespVO item : result.getList()) {
            assertTrue(item.getName().contains("套餐123"), "返回的套餐名称应包含关键词");
        }
    }

    /**
     * 测试服务套餐分页查询 - 组合条件查询（categoryId + keyword）
     */
    @Test
    public void testGetServicePackagePageWithCombinedFilters() {
        // 准备测试数据
        ServicePackagePageReqVO pageReqVO = new ServicePackagePageReqVO();
        pageReqVO.setPageNo(1);
        pageReqVO.setPageSize(10);
        pageReqVO.setCategoryId(1L);
        pageReqVO.setKeyword("保洁"); // 设置关键词

        // 执行查询
        PageResult<ServicePackageRespVO> result = servicePackageService.getServicePackagePage(pageReqVO);

        // 验证结果
        assertNotNull(result, "查询结果不应为空");
        assertNotNull(result.getList(), "查询结果列表不应为空");
        assertTrue(result.getTotal() >= 0, "总数应大于等于0");

        // 验证返回的数据都符合categoryId过滤条件
        for (ServicePackageRespVO item : result.getList()) {
            assertEquals(1L, item.getCategoryId(), "返回的套餐分类ID应该匹配过滤条件");
            assertTrue(item.getName().contains("保洁"), "返回的套餐名称应包含关键词");
        }
    }
}
