package cn.bztmaster.cnt.module.publicbiz.service.leads;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadInfoMapper;
import cn.bztmaster.cnt.module.publicbiz.service.leads.impl.LeadsServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 线索服务客户手机号查询功能测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class LeadsServiceCustomerPhoneTest {

    @Mock
    private LeadInfoMapper leadInfoMapper;

    @InjectMocks
    private LeadsServiceImpl leadsService;

    private LeadInfoDO mockLead1;
    private LeadInfoDO mockLead2;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        mockLead1 = new LeadInfoDO();
        mockLead1.setId(1L);
        mockLead1.setLeadId("XS202501150001");
        mockLead1.setCustomerName("张三");
        mockLead1.setCustomerPhone("13800138000");

        mockLead2 = new LeadInfoDO();
        mockLead2.setId(2L);
        mockLead2.setLeadId("XS202501150002");
        mockLead2.setCustomerName("李四");
        mockLead2.setCustomerPhone("13800138001");
    }

    @Test
    void testGetLeadsByCustomerPhone_Success() {
        // 准备参数
        String customerPhone = "13800138000";
        List<LeadInfoDO> expectedLeads = Arrays.asList(mockLead1);

        // Mock方法
        when(leadInfoMapper.selectListByCustomerPhone(customerPhone)).thenReturn(expectedLeads);

        // 调用
        List<LeadInfoDO> result = leadsService.getLeadsByCustomerPhone(customerPhone);

        // 验证
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockLead1.getId(), result.get(0).getId());
        assertEquals(mockLead1.getCustomerPhone(), result.get(0).getCustomerPhone());

        // 验证方法调用
        verify(leadInfoMapper).selectListByCustomerPhone(customerPhone);
    }

    @Test
    void testGetLeadsByCustomerPhone_EmptyResult() {
        // 准备参数
        String customerPhone = "13800138000";

        // Mock方法
        when(leadInfoMapper.selectListByCustomerPhone(customerPhone)).thenReturn(Collections.emptyList());

        // 调用
        List<LeadInfoDO> result = leadsService.getLeadsByCustomerPhone(customerPhone);

        // 验证
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // 验证方法调用
        verify(leadInfoMapper).selectListByCustomerPhone(customerPhone);
    }

    @Test
    void testGetLeadsByCustomerPhone_NullPhone() {
        // 准备参数
        String customerPhone = null;

        // 调用并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            leadsService.getLeadsByCustomerPhone(customerPhone);
        });

        assertEquals(400, exception.getCode());
        assertEquals("客户手机号不能为空", exception.getMessage());

        // 验证方法未被调用
        verify(leadInfoMapper, never()).selectListByCustomerPhone(anyString());
    }

    @Test
    void testGetLeadsByCustomerPhone_EmptyPhone() {
        // 准备参数
        String customerPhone = "";

        // 调用并验证异常
        ServiceException exception = assertThrows(ServiceException.class, () -> {
            leadsService.getLeadsByCustomerPhone(customerPhone);
        });

        assertEquals(400, exception.getCode());
        assertEquals("客户手机号不能为空", exception.getMessage());

        // 验证方法未被调用
        verify(leadInfoMapper, never()).selectListByCustomerPhone(anyString());
    }

    @Test
    void testGetLeadByCustomerPhone_Success() {
        // 准备参数
        String customerPhone = "13800138000";

        // Mock方法
        when(leadInfoMapper.selectByCustomerPhone(customerPhone)).thenReturn(mockLead1);

        // 调用
        LeadInfoDO result = leadsService.getLeadByCustomerPhone(customerPhone);

        // 验证
        assertNotNull(result);
        assertEquals(mockLead1.getId(), result.getId());
        assertEquals(mockLead1.getCustomerPhone(), result.getCustomerPhone());

        // 验证方法调用
        verify(leadInfoMapper).selectByCustomerPhone(customerPhone);
    }

    @Test
    void testGetLeadByCustomerPhone_NotFound() {
        // 准备参数
        String customerPhone = "13800138000";

        // Mock方法
        when(leadInfoMapper.selectByCustomerPhone(customerPhone)).thenReturn(null);

        // 调用
        LeadInfoDO result = leadsService.getLeadByCustomerPhone(customerPhone);

        // 验证
        assertNull(result);

        // 验证方法调用
        verify(leadInfoMapper).selectByCustomerPhone(customerPhone);
    }

    @Test
    void testExistsByCustomerPhone_Exists() {
        // 准备参数
        String customerPhone = "13800138000";

        // Mock方法
        when(leadInfoMapper.existsByCustomerPhone(customerPhone)).thenReturn(true);

        // 调用
        boolean result = leadsService.existsByCustomerPhone(customerPhone);

        // 验证
        assertTrue(result);

        // 验证方法调用
        verify(leadInfoMapper).existsByCustomerPhone(customerPhone);
    }

    @Test
    void testExistsByCustomerPhone_NotExists() {
        // 准备参数
        String customerPhone = "13800138000";

        // Mock方法
        when(leadInfoMapper.existsByCustomerPhone(customerPhone)).thenReturn(false);

        // 调用
        boolean result = leadsService.existsByCustomerPhone(customerPhone);

        // 验证
        assertFalse(result);

        // 验证方法调用
        verify(leadInfoMapper).existsByCustomerPhone(customerPhone);
    }

    @Test
    void testExistsByCustomerPhone_NullPhone() {
        // 准备参数
        String customerPhone = null;

        // 调用
        boolean result = leadsService.existsByCustomerPhone(customerPhone);

        // 验证
        assertFalse(result);

        // 验证方法未被调用
        verify(leadInfoMapper, never()).existsByCustomerPhone(anyString());
    }

    @Test
    void testExistsByCustomerPhone_EmptyPhone() {
        // 准备参数
        String customerPhone = "";

        // 调用
        boolean result = leadsService.existsByCustomerPhone(customerPhone);

        // 验证
        assertFalse(result);

        // 验证方法未被调用
        verify(leadInfoMapper, never()).existsByCustomerPhone(anyString());
    }
}
