<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyRecordMapper">

    <sql id="selectFields">
        id, tenant_id, record_id, agency_id, record_type, record_date, title, description,
        credit_impact, amount_impact, other_impact, related_info, status, follow_up_date,
        follow_up_item, remarks, effective_time, recorder_name, recorder_id, record_time,
        communication_type, communication_title, communication_content, participants,
        creator, create_time, updater, update_time, deleted
    </sql>

    <select id="selectPage" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyRecordDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_agency_record
        <where>
            deleted = 0
            <if test="recordType != null and recordType != ''">
                AND record_type = #{recordType}
            </if>
            <if test="startDate != null">
                AND record_date >= #{startDate}
            </if>
            <if test="endDate != null">
                AND record_date &lt;= #{endDate}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <select id="selectByRecordId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyRecordDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_agency_record
        WHERE record_id = #{recordId} AND deleted = 0
        LIMIT 1
    </select>

    <select id="selectByAgencyId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyRecordDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_agency_record
        WHERE agency_id = #{agencyId} AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据前缀查询当天最大记录编号，例如 prefix=REC20250101，则匹配 REC20250101% -->
    <select id="selectMaxRecordIdByPrefix" resultType="java.lang.String">
        SELECT record_id
        FROM publicbiz_agency_record
        WHERE deleted = 0
          AND record_id LIKE CONCAT(#{prefix}, '%')
        ORDER BY record_id DESC
        LIMIT 1
    </select>

</mapper> 