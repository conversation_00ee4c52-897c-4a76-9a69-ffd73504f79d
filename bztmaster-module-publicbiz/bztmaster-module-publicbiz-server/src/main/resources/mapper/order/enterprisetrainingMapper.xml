<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.enterprisetraining.EnterpriseTrainingOrderMapper">

    <sql id="selectFields">
        id, tenant_id, creator, create_time, updater, update_time, deleted,
        order_id, order_no, enterprise_name, enterprise_contact, enterprise_phone, enterprise_email, enterprise_address,
        training_project, training_description, participants_count, training_duration, training_location, training_type,
        per_person_fee, total_fee, material_fee, certification_fee, manager_id, manager_name, manager_phone, remark
    </sql>

    <select id="selectPage" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_training_order
        <where>
            <if test="query.tenantId != null">
                AND tenant_id = #{query.tenantId}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (enterprise_name LIKE CONCAT('%', #{query.keyword}, '%')
                OR training_project LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.trainingType != null and query.trainingType != ''">
                AND training_type = #{query.trainingType}
            </if>
            <if test="query.managerId != null">
                AND manager_id = #{query.managerId}
            </if>
            <if test="query.startDate != null and query.startDate != '' and query.endDate != null and query.endDate != ''">
                AND create_time BETWEEN CONCAT(#{query.startDate}, ' 00:00:00') AND CONCAT(#{query.endDate}, ' 23:59:59')
            </if>
            AND deleted = 0
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectByOrderNo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_training_order
        WHERE order_no = #{orderNo}
        AND deleted = 0
        LIMIT 1
    </select>

    <select id="selectByOrderId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_training_order
        WHERE order_id = #{orderId}
        AND deleted = 0
        LIMIT 1
    </select>

    <select id="selectByOrderIds" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_training_order
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        AND deleted = 0
    </select>

    <select id="selectByEnterpriseNameLike" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_training_order
        WHERE enterprise_name LIKE CONCAT('%', #{enterpriseName}, '%')
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <select id="selectByTrainingProjectLike" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_training_order
        WHERE training_project LIKE CONCAT('%', #{trainingProject}, '%')
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <select id="selectByTrainingType" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT
        <include refid="selectFields"/>
        FROM publicbiz_training_order
        WHERE training_type = #{trainingType}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID关联查询企业培训订单详情和公共订单信息 -->
    <select id="selectByIdWithOrderInfo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT 
            t.id, t.tenant_id, t.creator, t.create_time, t.updater, t.update_time, t.deleted,
            t.order_id, t.order_no, t.enterprise_name, t.enterprise_contact, t.enterprise_phone, 
            t.enterprise_email, t.enterprise_address, t.training_project, t.training_description, 
            t.participants_count, t.training_duration, t.training_location, t.training_type,
            t.per_person_fee, t.total_fee, t.material_fee, t.certification_fee,
            o.manager_id as managerId, o.manager_name as managerName, o.manager_phone as managerPhone, o.remark,
            o.order_type as orderType, o.business_line as businessLine, o.opportunity_id as opportunityId, o.lead_id as leadId, 
            o.project_name as projectName, o.project_description as projectDescription,
            o.start_date as startDate, o.end_date as endDate, o.total_amount as totalAmount, 
            o.paid_amount as paidAmount, o.refund_amount as refundAmount, o.payment_status as paymentStatus, o.order_status as orderStatus,
            o.contract_type as contractType, o.contract_file_url as contractFileUrl, o.contract_status as contractStatus, 
            o.settlement_status as settlementStatus, o.settlement_time as settlementTime, o.settlement_method as settlementMethod
        FROM publicbiz_training_order t
        LEFT JOIN publicbiz_order o ON t.order_id = o.id
        WHERE t.id = #{id} 
        AND t.deleted = 0
        LIMIT 1
    </select>

    <!-- 根据订单号关联查询企业培训订单详情和公共订单信息 -->
    <select id="selectByOrderNoWithOrderInfo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT 
            t.id, t.tenant_id, t.creator, t.create_time, t.updater, t.update_time, t.deleted,
            t.order_id, t.order_no, t.enterprise_name, t.enterprise_contact, t.enterprise_phone, 
            t.enterprise_email, t.enterprise_address, t.training_project, t.training_description, 
            t.participants_count, t.training_duration, t.training_location, t.training_type,
            t.per_person_fee, t.total_fee, t.material_fee, t.certification_fee,
            o.manager_id as managerId, o.manager_name as managerName, o.manager_phone as managerPhone, o.remark,
            o.order_type as orderType, o.business_line as businessLine, o.opportunity_id as opportunityId, o.lead_id as leadId, 
            o.project_name as projectName, o.project_description as projectDescription,
            o.start_date as startDate, o.end_date as endDate, o.total_amount as totalAmount, 
            o.paid_amount as paidAmount, o.refund_amount as refundAmount, o.payment_status as paymentStatus, o.order_status as orderStatus,
            o.contract_type as contractType, o.contract_file_url as contractFileUrl, o.contract_status as contractStatus, 
            o.settlement_status as settlementStatus, o.settlement_time as settlementTime, o.settlement_method as settlementMethod
        FROM publicbiz_training_order t
        LEFT JOIN publicbiz_order o ON t.order_id = o.id
        WHERE t.order_no = #{orderNo} 
        AND t.deleted = 0
        LIMIT 1
    </select>

    <!-- 根据订单ID列表关联查询企业培训订单详情和公共订单信息 -->
    <select id="selectByOrderIdsWithOrderInfo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT 
            t.id, t.tenant_id, t.creator, t.create_time, t.updater, t.update_time, t.deleted,
            t.order_id, t.order_no, t.enterprise_name, t.enterprise_contact, t.enterprise_phone, 
            t.enterprise_email, t.enterprise_address, t.training_project, t.training_description, 
            t.participants_count, t.training_duration, t.training_location, t.training_type,
            t.per_person_fee, t.total_fee, t.material_fee, t.certification_fee,
            o.manager_id as managerId, o.manager_name as managerName, o.manager_phone as managerPhone, o.remark,
            o.order_type as orderType, o.business_line as businessLine, o.opportunity_id as opportunityId, o.lead_id as leadId, 
            o.project_name as projectName, o.project_description as projectDescription,
            o.start_date as startDate, o.end_date as endDate, o.total_amount as totalAmount, 
            o.paid_amount as paidAmount, o.refund_amount as refundAmount, o.payment_status as paymentStatus, o.order_status as orderStatus,
            o.contract_type as contractType, o.contract_file_url as contractFileUrl, o.contract_status as contractStatus, 
            o.settlement_status as settlementStatus, o.settlement_time as settlementTime, o.settlement_method as settlementMethod
        FROM publicbiz_training_order t
        LEFT JOIN publicbiz_order o ON t.order_id = o.id
        WHERE t.order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        AND t.deleted = 0
        ORDER BY t.create_time DESC
    </select>

    <!-- 分页查询企业培训订单详情和公共订单信息 -->
    <select id="selectPageWithOrderInfo" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO">
        SELECT 
            t.id, t.tenant_id, t.creator, t.create_time, t.updater, t.update_time, t.deleted,
            t.order_id, t.order_no, t.enterprise_name, t.enterprise_contact, t.enterprise_phone, 
            t.enterprise_email, t.enterprise_address, t.training_project, t.training_description, 
            t.participants_count, t.training_duration, t.training_location, t.training_type,
            t.per_person_fee, t.total_fee, t.material_fee, t.certification_fee,
            o.manager_id as managerId, o.manager_name as managerName, o.manager_phone as managerPhone, o.remark,
            o.order_type as orderType, o.business_line as businessLine, o.opportunity_id as opportunityId, o.lead_id as leadId, 
            o.project_name as projectName, o.project_description as projectDescription,
            o.start_date as startDate, o.end_date as endDate, o.total_amount as totalAmount, 
            o.paid_amount as paidAmount, o.refund_amount as refundAmount, o.payment_status as paymentStatus, o.order_status as orderStatus,
            o.contract_type as contractType, o.contract_file_url as contractFileUrl, o.contract_status as contractStatus, 
            o.settlement_status as settlementStatus, o.settlement_time as settlementTime, o.settlement_method as settlementMethod
        FROM publicbiz_training_order t
        LEFT JOIN publicbiz_order o ON t.order_id = o.id
        <where>
            <if test="query.tenantId != null">
                AND t.tenant_id = #{query.tenantId}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (t.enterprise_name LIKE CONCAT('%', #{query.keyword}, '%')
                OR t.training_project LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.trainingType != null and query.trainingType != ''">
                AND t.training_type = #{query.trainingType}
            </if>
            <if test="query.managerId != null">
                AND o.manager_id = #{query.managerId}
            </if>
            <if test="query.startDate != null and query.startDate != '' and query.endDate != null and query.endDate != ''">
                AND t.create_time BETWEEN CONCAT(#{query.startDate}, ' 00:00:00') AND CONCAT(#{query.endDate}, ' 23:59:59')
            </if>
            AND t.deleted = 0
        </where>
        ORDER BY t.create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 统计分页查询总数 -->
    <select id="selectCountWithOrderInfo" resultType="long">
        SELECT COUNT(*)
        FROM publicbiz_training_order t
        LEFT JOIN publicbiz_order o ON t.order_id = o.id
        <where>
            <if test="query.tenantId != null">
                AND t.tenant_id = #{query.tenantId}
            </if>
            <if test="query.keyword != null and query.keyword != ''">
                AND (t.enterprise_name LIKE CONCAT('%', #{query.keyword}, '%')
                OR t.training_project LIKE CONCAT('%', #{query.keyword}, '%'))
            </if>
            <if test="query.trainingType != null and query.trainingType != ''">
                AND t.training_type = #{query.trainingType}
            </if>
            <if test="query.managerId != null">
                AND o.manager_id = #{query.managerId}
            </if>
            <if test="query.startDate != null and query.startDate != '' and query.endDate != null and query.endDate != ''">
                AND t.create_time BETWEEN CONCAT(#{query.startDate}, ' 00:00:00') AND CONCAT(#{query.endDate}, ' 23:59:59')
            </if>
            AND t.deleted = 0
        </where>
    </select>

    <!-- 调试查询：检查关联关系 -->
    <select id="debugOrderRelation" resultType="map">
        SELECT 
            t.id as training_id,
            t.order_id,
            t.order_no as training_order_no,
            o.id as order_id,
            o.order_no as order_order_no,
            o.order_type,
            o.business_line,
            o.opportunity_id,
            o.lead_id,
            o.total_amount,
            o.payment_status
        FROM publicbiz_training_order t
        LEFT JOIN publicbiz_order o ON t.order_id = o.id
        WHERE t.id = #{id}
        AND t.deleted = 0
        LIMIT 1
    </select>

</mapper>
