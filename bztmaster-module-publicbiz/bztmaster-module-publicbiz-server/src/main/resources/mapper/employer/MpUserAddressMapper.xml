<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserAddressMapper">

    <sql id="selectFields">
        id, user_id, label, receiver_name, receiver_phone, province_code, province_name, 
        city_code, city_name, district_code, district_name, region, address, full_address, 
        longitude, latitude, is_default, status, sort, remark, creator, create_time, 
        updater, update_time, deleted, tenant_id
    </sql>

    <!-- 根据用户ID查询地址列表 -->
    <select id="selectByUserId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserAddressDO">
        SELECT
        <include refid="selectFields"/>
        FROM mp_user_address
        WHERE user_id = #{userId}
        AND deleted = 0
        ORDER BY is_default DESC, sort ASC, create_time DESC
    </select>

    <!-- 根据用户ID查询默认地址 -->
    <select id="selectDefaultByUserId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserAddressDO">
        SELECT
        <include refid="selectFields"/>
        FROM mp_user_address
        WHERE user_id = #{userId}
        AND is_default = 1
        AND status = 1
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 根据用户ID查询启用的地址列表 -->
    <select id="selectEnabledByUserId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserAddressDO">
        SELECT
        <include refid="selectFields"/>
        FROM mp_user_address
        WHERE user_id = #{userId}
        AND status = 1
        AND deleted = 0
        ORDER BY is_default DESC, sort ASC, create_time DESC
    </select>

    <!-- 根据用户ID和地址ID查询地址详情 -->
    <select id="selectByUserIdAndAddressId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserAddressDO">
        SELECT
        <include refid="selectFields"/>
        FROM mp_user_address
        WHERE user_id = #{userId}
        AND id = #{addressId}
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 设置用户的所有地址为非默认 -->
    <update id="clearDefaultByUserId">
        UPDATE mp_user_address
        SET is_default = 0,
            updater = #{updater},
            update_time = NOW()
        WHERE user_id = #{userId}
        AND deleted = 0
    </update>

    <!-- 设置指定地址为默认地址 -->
    <update id="setDefaultByUserIdAndAddressId">
        UPDATE mp_user_address
        SET is_default = 1,
            updater = #{updater},
            update_time = NOW()
        WHERE user_id = #{userId}
        AND id = #{addressId}
        AND deleted = 0
    </update>

    <!-- 根据地址ID查询地址详情 -->
    <select id="selectById" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserAddressDO">
        SELECT
        <include refid="selectFields"/>
        FROM mp_user_address
        WHERE id = #{addressId}
        AND deleted = 0
        LIMIT 1
    </select>

</mapper>
