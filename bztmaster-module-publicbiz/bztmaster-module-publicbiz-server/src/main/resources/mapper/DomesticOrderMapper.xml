<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper">

    <select id="selectCountGroupByCreateMonth" resultType="java.util.HashMap">
        SELECT DATE_FORMAT(create_time, '%Y-%m') AS month,
               COUNT(1)                          AS count
        FROM publicbiz_domestic_order
        WHERE deleted = FALSE
          AND agency_id = #{agencyId}
          AND create_time BETWEEN #{beginTime} AND #{endTime}
        GROUP BY month
        ORDER BY month ASC
    </select>

</mapper>

