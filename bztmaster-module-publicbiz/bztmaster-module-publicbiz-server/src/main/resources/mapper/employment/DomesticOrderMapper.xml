<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper">

    <sql id="selectFields">
        id, order_id, order_no, customer_oneid, customer_name, customer_phone, customer_address, customer_remark,
        service_category_id, service_category_name, service_package_id, service_package_name, service_start_date,
        service_end_date, service_duration, service_frequency, service_package_thumbnail, service_package_price,
        service_package_original_price, service_package_unit, service_package_duration, service_package_type,
        service_description, service_details, service_process, purchase_notice, service_times, unit_price,
        total_amount, discount_amount, actual_amount, service_address, service_address_detail, service_latitude,
        service_longitude, service_schedule, practitioner_oneid, practitioner_name, practitioner_phone,
        agency_id, agency_name, task_count, completed_task_count, task_progress, service_fee, agency_fee,
        platform_fee, create_time, update_time, creator, updater, deleted, tenant_id
    </sql>

    <!-- 根据阿姨OneID查询订单列表 -->
    <select id="selectByPractitionerOneid" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_domestic_order
        WHERE practitioner_oneid = #{practitionerOneid}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据订单ID查询订单详情 -->
    <select id="selectByOrderId" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_domestic_order
        WHERE order_id = #{orderId}
        AND deleted = 0
    </select>

    <!-- 根据客户OneID查询订单列表 -->
    <select id="selectByCustomerOneid" resultType="cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO">
        SELECT <include refid="selectFields"/>
        FROM publicbiz_domestic_order
        WHERE customer_oneid = #{customerOneid}
        AND deleted = 0
        ORDER BY create_time DESC
    </select>

</mapper> 