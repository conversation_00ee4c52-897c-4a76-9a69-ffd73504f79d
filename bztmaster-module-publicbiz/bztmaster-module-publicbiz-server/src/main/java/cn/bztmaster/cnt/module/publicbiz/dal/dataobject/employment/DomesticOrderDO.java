package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("publicbiz_domestic_order")
@Schema(description = "家政服务订单详情表 DO")
public class DomesticOrderDO {
    @TableId
    private Long id;
    private Long orderId;
    private String orderNo;
    private String customerOneid;
    private String customerName;
    private String customerPhone;
    private String customerAddress;
    private String customerRemark;
    private Long serviceCategoryId;
    private String serviceCategoryName;
    private Long servicePackageId;
    private String servicePackageName;
    private Date serviceStartDate;
    private Date serviceEndDate;
    private String serviceDuration;
    private String serviceFrequency;
    private String servicePackageThumbnail;
    private BigDecimal servicePackagePrice;
    private BigDecimal servicePackageOriginalPrice;
    private String servicePackageUnit;
    private String servicePackageDuration;
    private String servicePackageType;
    private String serviceDescription;
    private String serviceDetails;
    private String serviceProcess;
    private String purchaseNotice;
    private Integer serviceTimes;
    private BigDecimal unitPrice;
    private BigDecimal totalAmount;
    private BigDecimal discountAmount;
    private BigDecimal actualAmount;
    private String serviceAddress;
    private String serviceAddressDetail;
    private BigDecimal serviceLatitude;
    private BigDecimal serviceLongitude;
    private String serviceSchedule;
    private String practitionerOneid;
    private String practitionerName;
    private String practitionerPhone;
    private Long agencyId;
    private String agencyName;
    private Integer taskCount;
    private Integer completedTaskCount;
    private BigDecimal taskProgress;
    private BigDecimal serviceFee;
    private BigDecimal agencyFee;
    private BigDecimal platformFee;
    private Date createTime;
    private Date updateTime;
    private String creator;
    private String updater;
    private Boolean deleted;
    private Long tenantId;
}