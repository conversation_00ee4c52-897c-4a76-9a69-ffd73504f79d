package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 机构记录附件 Response VO")
@Data
public class AgencyRecordAttachmentRespVO {

    @Schema(description = "附件ID", example = "1")
    private Long id;

    @Schema(description = "文件名", example = "客户表扬录音.mp3")
    private String fileName;

    @Schema(description = "文件类型", example = "mp3")
    private String fileType;

    @Schema(description = "文件大小（字节）", example = "1536000")
    private Long fileSize;

    @Schema(description = "文件访问URL", example = "https://example.com/files/客户表扬录音.mp3")
    private String fileUrl;

    @Schema(description = "上传时间")
    private LocalDateTime uploadTime;
} 