package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 阿姨评价列表响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 阿姨评价列表响应 VO")
@Data
public class AuntReviewListRespVO {

    @Schema(description = "总记录数", example = "25")
    private Long total;

    @Schema(description = "总页数", example = "3")
    private Long pages;

    @Schema(description = "当前页码", example = "1")
    private Long current;

    @Schema(description = "每页数量", example = "10")
    private Long size;

    @Schema(description = "评价记录列表")
    private List<ReviewRecord> records;

    @Schema(description = "评价记录")
    @Data
    public static class ReviewRecord {
        @Schema(description = "评价ID", example = "1")
        private Long id;

        @Schema(description = "评价人姓名", example = "李女士")
        private String reviewerName;

        @Schema(description = "评价人头像URL", example = "https://example.com/user1.jpg")
        private String reviewerAvatar;

        @Schema(description = "评分", example = "5.0")
        private BigDecimal rating;

        @Schema(description = "评价日期", example = "2024-05-15")
        private String date;

        @Schema(description = "评价标签", example = "[\"专业\",\"细心\",\"守时\"]")
        private List<String> tags;

        @Schema(description = "评价内容", example = "王阿姨非常专业，照顾宝宝很细心，服务态度也很好，非常满意！")
        private String content;

        @Schema(description = "评价图片URL列表", example = "[]")
        private List<String> images;

        @Schema(description = "是否匿名评价", example = "false")
        private Boolean isAnonymous;

        @Schema(description = "点赞数", example = "3")
        private Integer likeCount;
    }
}
