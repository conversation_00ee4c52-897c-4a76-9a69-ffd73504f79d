package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 机构记录分页 Request VO")
@Data
public class AgencyRecordPageReqVO {

    @Schema(description = "页码，从1开始", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码最小为1")
    private Integer pageNum;

    @Schema(description = "每页记录数，最大100", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    @NotNull(message = "每页记录数不能为空")
    @Min(value = 1, message = "每页记录数最小为1")
    @Max(value = 100, message = "每页记录数最大为100")
    private Integer pageSize;

    @Schema(description = "记录类型：incentive-激励记录，penalty-处罚记录，communication-沟通日志", example = "incentive")
    private String recordType;

    @Schema(description = "开始日期", example = "2024-01-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-01-31")
    private LocalDate endDate;

    @Schema(description = "机构ID", example = "1")
    private Long agencyId;
} 