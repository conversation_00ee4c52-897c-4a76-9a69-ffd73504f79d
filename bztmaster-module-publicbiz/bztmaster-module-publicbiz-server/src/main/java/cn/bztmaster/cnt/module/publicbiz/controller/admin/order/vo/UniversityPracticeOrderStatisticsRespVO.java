package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 高校实践订单统计概览响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单统计概览响应")
@Data
public class UniversityPracticeOrderStatisticsRespVO {

    @Schema(description = "总订单数", example = "150")
    private Long totalOrders;

    @Schema(description = "本月新增订单数", example = "25")
    private Long monthlyNewOrders;

    @Schema(description = "总订单金额", example = "8500000.00")
    private BigDecimal totalAmount;

    @Schema(description = "本月新增订单金额", example = "1200000.00")
    private BigDecimal monthlyNewAmount;

    @Schema(description = "已完成订单数", example = "120")
    private Long completedOrders;

    @Schema(description = "进行中订单数", example = "20")
    private Long inProgressOrders;

    @Schema(description = "待审批订单数", example = "10")
    private Long pendingApprovalOrders;

    @Schema(description = "各状态订单数量统计")
    private List<OrderStatusCount> statusCounts;

    @Schema(description = "各月份订单数量趋势")
    private List<MonthlyOrderTrend> monthlyTrends;

    @Schema(description = "统计时间", example = "2024-06-21 10:30:00")
    private String statisticsTime;

    /**
     * 订单状态数量统计
     */
    @Schema(description = "订单状态数量统计")
    @Data
    public static class OrderStatusCount {
        @Schema(description = "状态", example = "draft")
        private String status;

        @Schema(description = "状态名称", example = "草稿")
        private String statusName;

        @Schema(description = "数量", example = "15")
        private Long count;

        @Schema(description = "占比", example = "10.0")
        private Double percentage;
    }

    /**
     * 月度订单趋势
     */
    @Schema(description = "月度订单趋势")
    @Data
    public static class MonthlyOrderTrend {
        @Schema(description = "月份", example = "2024-06")
        private String month;

        @Schema(description = "订单数量", example = "25")
        private Long orderCount;

        @Schema(description = "订单金额", example = "1200000.00")
        private BigDecimal orderAmount;
    }

}
