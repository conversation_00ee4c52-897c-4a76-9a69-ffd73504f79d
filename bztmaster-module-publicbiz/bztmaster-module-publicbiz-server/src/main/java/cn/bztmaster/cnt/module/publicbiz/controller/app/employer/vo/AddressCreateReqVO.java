package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;

/**
 * 地址创建请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 地址创建请求 VO")
@Data
public class AddressCreateReqVO {

    @Schema(description = "地址标签", requiredMode = Schema.RequiredMode.REQUIRED, example = "家庭地址")
    @NotBlank(message = "地址标签不能为空")
    private String label;

    @Schema(description = "收货人姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @NotBlank(message = "收货人姓名不能为空")
    private String receiverName;

    @Schema(description = "收货人手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13900139001")
    @NotBlank(message = "收货人手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String receiverPhone;

    @Schema(description = "省份编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "510000")
    @NotBlank(message = "省份编码不能为空")
    private String provinceCode;

    @Schema(description = "省份名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "四川省")
    @NotBlank(message = "省份名称不能为空")
    private String provinceName;

    @Schema(description = "城市编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "510100")
    @NotBlank(message = "城市编码不能为空")
    private String cityCode;

    @Schema(description = "城市名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "成都市")
    @NotBlank(message = "城市名称不能为空")
    private String cityName;

    @Schema(description = "区县编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "510104")
    @NotBlank(message = "区县编码不能为空")
    private String districtCode;

    @Schema(description = "区县名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "锦江区")
    @NotBlank(message = "区县名称不能为空")
    private String districtName;

    @Schema(description = "省市区组合", requiredMode = Schema.RequiredMode.REQUIRED, example = "四川省成都市锦江区")
    @NotBlank(message = "省市区组合不能为空")
    private String region;

    @Schema(description = "详细地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "春熙路1号太古里购物中心2楼201室")
    @NotBlank(message = "详细地址不能为空")
    private String address;

    @Schema(description = "经度", example = "104.0800000")
    private BigDecimal longitude;

    @Schema(description = "纬度", example = "30.6500000")
    private BigDecimal latitude;

    @Schema(description = "是否默认地址", example = "false")
    private Boolean isDefault = false;

    @Schema(description = "排序", example = "1")
    private Integer sort = 0;

    @Schema(description = "备注")
    private String remark;
}
