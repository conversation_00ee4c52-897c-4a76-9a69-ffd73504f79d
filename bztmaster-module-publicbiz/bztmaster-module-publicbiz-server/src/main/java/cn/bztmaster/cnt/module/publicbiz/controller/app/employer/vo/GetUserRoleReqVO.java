package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 获取用户角色请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "获取用户角色请求 VO")
@Data
public class GetUserRoleReqVO {

    @Schema(description = "微信登录临时code", requiredMode = Schema.RequiredMode.REQUIRED, example = "wx_login_code_123456")
    @NotBlank(message = "code参数不能为空")
    private String code;

}