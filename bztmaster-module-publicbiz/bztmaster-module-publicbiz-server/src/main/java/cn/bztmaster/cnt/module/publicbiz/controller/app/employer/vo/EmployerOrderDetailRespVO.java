package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 雇主端订单详情 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端订单详情 Response VO")
@Data
public class EmployerOrderDetailRespVO {

    @Schema(description = "订单ID", example = "123")
    private Long orderId;

    @Schema(description = "订单编号", example = "DO202412010001")
    private String orderNo;

    @Schema(description = "订单类型", example = "domestic")
    private String orderType;

    @Schema(description = "业务线", example = "家政服务")
    private String businessLine;

    @Schema(description = "订单状态", example = "executing")
    private String orderStatus;

    @Schema(description = "支付状态", example = "paid")
    private String paymentStatus;

    @Schema(description = "订单总金额", example = "150.00")
    private String totalAmount;

    @Schema(description = "已支付金额", example = "140.00")
    private String paidAmount;

    @Schema(description = "退款金额", example = "0.00")
    private String refundAmount;

    @Schema(description = "创建时间", example = "2024-12-19 14:00:09")
    private String createTime;

    @Schema(description = "更新时间", example = "2024-12-19 14:00:09")
    private String updateTime;

    @Schema(description = "备注信息", example = "家里有狗")
    private String remarks;

    @Schema(description = "服务地址", example = "杨浦区莲花小区")
    private String serviceAddress;

    @Schema(description = "详细地址", example = "1号楼1单元101室")
    private String serviceAddressDetail;

    @Schema(description = "家政订单详情")
    private DomesticOrderDetail domesticOrder;

    @Schema(description = "进度步骤")
    private List<ProgressStep> progressSteps;

    @Schema(description = "家政订单详情")
    @Data
    public static class DomesticOrderDetail {

        @Schema(description = "订单ID", example = "1")
        private Long id;

        @Schema(description = "客户姓名", example = "张女士")
        private String customerName;

        @Schema(description = "客户手机号", example = "138****8888")
        private String customerPhone;

        @Schema(description = "服务分类名称", example = "日常保洁")
        private String serviceCategoryName;

        @Schema(description = "服务套餐名称", example = "3次日常保洁 (3小时)")
        private String servicePackageName;

        @Schema(description = "服务套餐缩略图", example = "https://example.com/image1.jpg")
        private String servicePackageThumbnail;

        @Schema(description = "服务套餐价格", example = "150.00")
        private String servicePackagePrice;

        @Schema(description = "服务套餐原价", example = "160.00")
        private String servicePackageOriginalPrice;

        @Schema(description = "服务套餐单位", example = "次")
        private String servicePackageUnit;

        @Schema(description = "服务套餐时长", example = "3小时")
        private String servicePackageDuration;

        @Schema(description = "服务套餐类型", example = "count-card")
        private String servicePackageType;

        @Schema(description = "服务次数", example = "3")
        private Integer serviceTimes;

        @Schema(description = "单价", example = "50.00")
        private String unitPrice;

        @Schema(description = "实际金额", example = "140.00")
        private String actualAmount;

        @Schema(description = "服务人员姓名", example = "李阿姨")
        private String practitionerName;

        @Schema(description = "服务人员手机号", example = "139****9999")
        private String practitionerPhone;

        @Schema(description = "服务人员头像", example = "https://example.com/avatar.jpg")
        private String practitionerAvatar;

        @Schema(description = "服务人员标签", example = "金牌阿姨")
        private String practitionerBadge;

        @Schema(description = "服务人员经验", example = "5年经验")
        private String practitionerExperience;

        @Schema(description = "服务家庭数", example = "服务15个家庭")
        private String practitionerFamilies;

        @Schema(description = "机构名称", example = "金牌家政")
        private String agencyName;

        @Schema(description = "任务总数", example = "3")
        private Integer taskCount;

        @Schema(description = "已完成任务数", example = "1")
        private Integer completedTaskCount;

        @Schema(description = "任务进度", example = "33.33")
        private String taskProgress;

        @Schema(description = "服务开始日期", example = "2024-12-19")
        private String serviceStartDate;

        @Schema(description = "服务结束日期", example = "2024-12-31")
        private String serviceEndDate;

        @Schema(description = "服务安排")
        private List<ServiceSchedule> serviceSchedule;
    }

    @Schema(description = "服务安排")
    @Data
    public static class ServiceSchedule {

        @Schema(description = "任务序号", example = "1")
        private Integer taskIndex;

        @Schema(description = "任务标签", example = "第1次")
        private String taskLabel;

        @Schema(description = "计划服务时间", example = "2024-12-19 14:00-17:00")
        private String scheduledTime;

        @Schema(description = "实际服务时间", example = "2024-12-19 14:00-17:00")
        private String actualTime;

        @Schema(description = "任务状态", example = "completed")
        private String status;

        @Schema(description = "服务人员姓名", example = "李阿姨")
        private String practitionerName;

        @Schema(description = "服务人员手机号", example = "139****9999")
        private String practitionerPhone;
    }

    @Schema(description = "进度步骤")
    @Data
    public static class ProgressStep {

        @Schema(description = "步骤标识", example = "payment")
        private String step;

        @Schema(description = "步骤名称", example = "已支付")
        private String stepName;

        @Schema(description = "步骤状态", example = "completed")
        private String status;

        @Schema(description = "完成时间", example = "2024-12-19 14:00:09")
        private String completedTime;

        @Schema(description = "开始时间", example = "2024-12-19 14:00:00")
        private String startedTime;
    }
}
