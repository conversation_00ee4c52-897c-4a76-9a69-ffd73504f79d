package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 高校实践订单合同上传响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单合同上传响应")
@Data
public class UniversityPracticeOrderContractUploadRespVO {

    @Schema(description = "上传是否成功", example = "true")
    private Boolean success;

    @Schema(description = "文件URL", example = "https://example.com/contracts/HT-202406001.pdf")
    private String fileUrl;

    @Schema(description = "文件名", example = "合同_XX大学暑期实践项目.pdf")
    private String fileName;

    @Schema(description = "文件大小（字节）", example = "2048576")
    private Long fileSize;

    @Schema(description = "合同状态", example = "unsigned")
    private String contractStatus;

    @Schema(description = "上传时间", example = "2024-06-21 10:30:00")
    private String uploadTime;

}
