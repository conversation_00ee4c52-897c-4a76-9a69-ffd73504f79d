package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 轮播图 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "轮播图 Response VO")
@Data
public class CarouselRespVO {

    @Schema(description = "轮播图ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "轮播图标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "限时优惠活动")
    private String carouselTitle;

    @Schema(description = "轮播图片URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/banner1.jpg")
    private String carouselImageUrl;

    @Schema(description = "跳转链接URL", example = "/pages/activity/index")
    private String carouselLinkUrl;

    @Schema(description = "排序值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer sortOrder;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    private LocalDateTime startTime;

    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    private LocalDateTime endTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}