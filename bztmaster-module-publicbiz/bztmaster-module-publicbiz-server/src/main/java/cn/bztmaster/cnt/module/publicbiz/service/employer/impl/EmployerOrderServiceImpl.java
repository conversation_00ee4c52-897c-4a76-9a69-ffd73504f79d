package cn.bztmaster.cnt.module.publicbiz.service.employer.impl;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderCreateRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.EmployerOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadInfoDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderLogDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.MpUserAddressDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.leads.LeadInfoMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderLogMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.MpUserAddressMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.BusinessModuleEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.CreateMethodEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadSourceEnum;
import cn.bztmaster.cnt.module.publicbiz.enums.leads.LeadStatusEnum;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerOrderService;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 雇主端订单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class EmployerOrderServiceImpl implements EmployerOrderService {

    @Resource
    private PublicbizOrderMapper orderMapper;

    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    @Resource
    private LeadInfoMapper leadInfoMapper;

    @Resource
    private PublicbizOrderLogMapper orderLogMapper;

    @Resource
    private MpUserMapper mpUserMapper;

    @Resource
    private MpUserAddressMapper mpUserAddressMapper;

    @Resource
    private ServicePackageMapper servicePackageMapper;

    @Resource
    private PractitionerMapper practitionerMapper;

    @Override
    public EmployerOrderListRespVO getOrderList(String customerOpenId, String status, Integer page, Integer size) {
        log.info("获取雇主订单列表 - customerOpenId: {}, status: {}, page: {}, size: {}",
                customerOpenId, status, page, size);

        try {
            // 1. 参数校验
            if (StrUtil.isEmpty(customerOpenId)) {
                throw new ServiceException(400, "客户OpenId不能为空");
            }

            // 2. 根据openId获取用户oneid
            String userOneId = getOneIdByOpenId(customerOpenId);
            if (StrUtil.isEmpty(userOneId)) {
                throw new ServiceException(404, "客户信息不存在");
            }

            // 3. 设置默认分页参数
            page = page == null || page <= 0 ? 1 : page;
            size = size == null || size <= 0 ? 10 : Math.min(size, 50);

            // 4. 查询订单列表
            List<PublicbizOrderDO> orderList;
            if ("all".equals(status) || StrUtil.isEmpty(status)) {
                // 查询所有订单
                orderList = orderMapper.selectListByCreator(userOneId);
            } else {
                // 根据状态查询订单
                orderList = orderMapper.selectListByCreatorAndStatus(userOneId, status);
            }

            // 5. 过滤家政服务订单
            List<PublicbizOrderDO> domesticOrders = orderList.stream()
                    .filter(order -> "domestic".equals(order.getOrderType()))
                    .collect(Collectors.toList());

            // 6. 分页处理
            int total = domesticOrders.size();
            int startIndex = (page - 1) * size;
            int endIndex = Math.min(startIndex + size, total);
            List<PublicbizOrderDO> pageOrders = startIndex < total ? domesticOrders.subList(startIndex, endIndex)
                    : new ArrayList<>();

            // 7. 获取订单详情
            List<EmployerOrderListRespVO.OrderInfo> orderInfoList = new ArrayList<>();
            for (PublicbizOrderDO order : pageOrders) {
                DomesticOrderDO domesticOrder = domesticOrderMapper.selectByOrderId(order.getId());
                if (domesticOrder != null) {
                    EmployerOrderListRespVO.OrderInfo orderInfo = buildOrderInfo(order, domesticOrder);
                    orderInfoList.add(orderInfo);
                }
            }

            // 8. 统计各状态订单数量
            Map<String, Long> statusCounts = getStatusCounts(userOneId);

            // 9. 构建响应结果
            EmployerOrderListRespVO result = new EmployerOrderListRespVO();
            result.setTotal((long) total);
            result.setPages((long) ((total + size - 1) / size));
            result.setCurrent((long) page);
            result.setSize((long) size);
            result.setRecords(orderInfoList);
            result.setStatusCounts(statusCounts);

            log.info("获取雇主订单列表成功 - 总数: {}, 当前页: {}, 每页数量: {}", total, page, size);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取雇主订单列表失败 - customerOpenId: {}, 错误: {}", customerOpenId, e.getMessage(), e);
            throw new ServiceException(500, "获取订单列表失败，请稍后重试");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public EmployerOrderCreateRespVO createOrder(@Valid EmployerOrderCreateReqVO createReqVO) {
        log.info("创建服务套餐订单 - createReqVO: {}", createReqVO);

        try {
            // 1. 参数校验（@Valid注解已处理）

            // 根据openId获取用户oneid作为创建人和更新人
            String userOneId = getOneIdByOpenId(createReqVO.getOpenId());

            // 2. 获取并校验服务套餐信息
            ServicePackageDO servicePackage = validateAndGetServicePackage(createReqVO);

            // 3. 业务规则校验
            validateBusinessRules(createReqVO);

            // 4. 线索匹配或创建
            String leadId = handleLeadMatching(createReqVO);

            // 5. 生成订单号
            String orderNo = generateOrderNo();

            // 6. 创建订单主表记录
            PublicbizOrderDO orderDO = createMainOrder(createReqVO, orderNo, leadId, userOneId, servicePackage);

            // 7. 创建家政服务订单详情记录
            DomesticOrderDO domesticOrderDO = createDomesticOrder(createReqVO, orderDO, userOneId, servicePackage);

            // 8. 创建订单日志
            createOrderLog(orderDO, "订单创建", "雇主通过小程序创建订单", "draft", "pending_payment", userOneId);

            // 9. 构建响应结果
            EmployerOrderCreateRespVO result = buildCreateResponse(orderDO, domesticOrderDO);

            log.info("订单创建成功 - 订单号: {}, 订单ID: {}", orderNo, orderDO.getId());
            return result;

        } catch (Exception e) {
            log.error("创建订单失败 - createReqVO: {}, 错误: {}", createReqVO, e.getMessage(), e);
            throw new ServiceException(500, "创建订单失败，请稍后重试");
        }
    }

    /**
     * 获取并校验服务套餐信息
     */
    private ServicePackageDO validateAndGetServicePackage(EmployerOrderCreateReqVO createReqVO) {
        Long serviceId = createReqVO.getServiceId();
        if (serviceId == null) {
            throw new ServiceException(400, "服务套餐ID不能为空");
        }

        // 查询数据库中的最新套餐信息
        ServicePackageDO servicePackage = servicePackageMapper.selectById(serviceId);
        if (servicePackage == null) {
            throw new ServiceException(400, "服务套餐不存在");
        }

        // 检查套餐状态
        if (!"active".equals(servicePackage.getStatus())) {
            throw new ServiceException(400, "服务套餐已下架，请选择其他套餐");
        }

        // 检查审核状态
        if (!"approved".equals(servicePackage.getAuditStatus())) {
            throw new ServiceException(400, "服务套餐审核未通过，请选择其他套餐");
        }

        // 校验价格是否一致
        BigDecimal requestPrice = createReqVO.getServiceInfo().getPrice();
        BigDecimal dbPrice = servicePackage.getPrice();

        if (requestPrice.compareTo(dbPrice) != 0) {
            log.warn("套餐价格不一致 - 请求价格: {}, 数据库价格: {}, 套餐ID: {}",
                    requestPrice, dbPrice, serviceId);
            throw new ServiceException(400, "套餐信息已变更，请刷新套餐信息后提交订单");
        }

        // 校验原价是否一致
        BigDecimal requestOriginalPrice = createReqVO.getServiceInfo().getOriginalPrice();
        BigDecimal dbOriginalPrice = servicePackage.getOriginalPrice();

        if (requestOriginalPrice != null && dbOriginalPrice != null &&
                requestOriginalPrice.compareTo(dbOriginalPrice) != 0) {
            log.warn("套餐原价不一致 - 请求原价: {}, 数据库原价: {}, 套餐ID: {}",
                    requestOriginalPrice, dbOriginalPrice, serviceId);
            throw new ServiceException(400, "套餐信息已变更，请刷新套餐信息后提交订单");
        }

        log.info("套餐信息校验通过 - 套餐ID: {}, 套餐名称: {}, 价格: {}",
                serviceId, servicePackage.getName(), dbPrice);

        return servicePackage;
    }

    /**
     * 业务规则校验
     */
    private void validateBusinessRules(EmployerOrderCreateReqVO createReqVO) {
        // 校验套餐类型和参数的匹配性
        if ("long-term".equals(createReqVO.getPackageType())) {
            if (StrUtil.isEmpty(createReqVO.getStartDate()) || createReqVO.getServiceDays() == null) {
                throw new ServiceException(400, "长周期套餐必须提供开始日期和服务天数");
            }
        } else if ("count-card".equals(createReqVO.getPackageType())) {
            if (createReqVO.getServiceTimes() == null || createReqVO.getServiceTimes().isEmpty()) {
                throw new ServiceException(400, "次卡套餐必须提供服务时间列表");
            }
        }

        // 校验金额
        if (createReqVO.getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException(400, "订单金额必须大于0");
        }
    }

    /**
     * 处理线索匹配
     */
    private String handleLeadMatching(EmployerOrderCreateReqVO createReqVO) {
        String customerPhone = createReqVO.getUserPhone();

        // 查询是否存在该手机号的线索
        List<LeadInfoDO> existingLeads = leadInfoMapper.selectListByCustomerPhone(customerPhone);

        if (!existingLeads.isEmpty()) {
            // 存在历史线索，更新状态为已转化，并更新客户姓名为真实昵称
            LeadInfoDO existingLead = existingLeads.get(0); // 取最新的线索
            String customerName = getCustomerNameByOpenId(createReqVO.getOpenId(), createReqVO.getUserPhone());
            existingLead.setCustomerName(customerName);
            existingLead.setLeadStatus(LeadStatusEnum.CONVERTED.getType());
            existingLead.setUpdateTime(LocalDateTime.now());
            leadInfoMapper.updateById(existingLead);

            log.info("找到历史线索，更新状态为已转化 - 线索ID: {}, 客户手机号: {}, 客户姓名: {}",
                    existingLead.getLeadId(), customerPhone, customerName);

            return existingLead.getLeadId();
        } else {
            // 不存在线索，创建新线索并标记为已转化
            String leadId = generateLeadId();
            LeadInfoDO newLead = createNewLead(createReqVO, leadId);
            leadInfoMapper.insert(newLead);

            log.info("创建新线索并标记为已转化 - 线索ID: {}, 客户手机号: {}", leadId, customerPhone);

            return leadId;
        }
    }

    /**
     * 创建新线索
     */
    private LeadInfoDO createNewLead(EmployerOrderCreateReqVO createReqVO, String leadId) {
        // 根据openId查询用户昵称
        String customerName = getCustomerNameByOpenId(createReqVO.getOpenId(), createReqVO.getUserPhone());

        LeadInfoDO leadDO = new LeadInfoDO();
        leadDO.setLeadId(leadId);
        leadDO.setCustomerName(customerName);
        leadDO.setCustomerPhone(createReqVO.getUserPhone());
        leadDO.setLeadSource(LeadSourceEnum.OTHER.getType()); // 其它
        leadDO.setBusinessModule(BusinessModuleEnum.DOMESTIC_SERVICE.getType()); // 家政业务
        leadDO.setLeadStatus(LeadStatusEnum.CONVERTED.getType()); // 已转化
        leadDO.setCreateMethod(CreateMethodEnum.API_INTEGRATION.getType());
        leadDO.setRemark("雇主通过小程序创建订单自动生成线索");
        leadDO.setCreateTime(LocalDateTime.now());
        leadDO.setUpdateTime(LocalDateTime.now());
        leadDO.setTenantId(1L);
        return leadDO;
    }

    /**
     * 根据openId获取用户昵称
     */
    private String getCustomerNameByOpenId(String openId, String userPhone) {
        if (StrUtil.isEmpty(openId)) {
            log.warn("openId为空，使用雇主手机号作为客户姓名");
            return userPhone;
        }

        try {
            MpUserDO mpUser = mpUserMapper.selectByOpenid(openId);
            if (mpUser != null && StrUtil.isNotEmpty(mpUser.getNickname())) {
                log.info("根据openId查询到用户昵称 - openId: {}, nickname: {}", openId, mpUser.getNickname());
                return mpUser.getNickname();
            } else {
                log.warn("根据openId未查询到用户信息或昵称为空 - openId: {}", openId);
                return userPhone;
            }
        } catch (Exception e) {
            log.error("根据openId查询用户信息失败 - openId: {}, 错误: {}", openId, e.getMessage(), e);
            return userPhone;
        }
    }

    /**
     * 根据openId获取用户oneid
     */
    private String getOneIdByOpenId(String openId) {
        if (StrUtil.isEmpty(openId)) {
            log.warn("openId为空，无法获取用户oneid");
            return null;
        }

        try {
            MpUserDO mpUser = mpUserMapper.selectByOpenid(openId);
            if (mpUser != null && StrUtil.isNotEmpty(mpUser.getOneid())) {
                log.info("根据openId查询到用户oneid - openId: {}, oneid: {}", openId, mpUser.getOneid());
                return mpUser.getOneid();
            } else {
                log.warn("根据openId未查询到用户信息或oneid为空 - openId: {}", openId);
                return null;
            }
        } catch (Exception e) {
            log.error("根据openId查询用户信息失败 - openId: {}, 错误: {}", openId, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据addressId获取地址信息，处理省市区重复去重
     */
    private String getAddressByAddressId(Long addressId) {
        if (addressId == null) {
            log.warn("addressId为空，返回空地址");
            return "";
        }

        try {
            MpUserAddressDO addressDO = mpUserAddressMapper.selectById(addressId);
            if (addressDO == null) {
                log.warn("根据addressId未查询到地址信息 - addressId: {}", addressId);
                return "";
            }

            // 获取region和address
            String region = addressDO.getRegion();
            String address = addressDO.getAddress();

            if (StrUtil.isEmpty(region) && StrUtil.isEmpty(address)) {
                log.warn("地址信息为空 - addressId: {}", addressId);
                return "";
            }

            // 处理省市区重复去重
            String fullAddress = buildFullAddress(region, address);
            log.info("根据addressId查询到地址信息 - addressId: {}, fullAddress: {}", addressId, fullAddress);
            return fullAddress;

        } catch (Exception e) {
            log.error("根据addressId查询地址信息失败 - addressId: {}, 错误: {}", addressId, e.getMessage(), e);
            return "";
        }
    }

    /**
     * 构建完整地址，处理省市区重复去重
     */
    private String buildFullAddress(String region, String address) {
        if (StrUtil.isEmpty(region) && StrUtil.isEmpty(address)) {
            return "";
        }

        if (StrUtil.isEmpty(region)) {
            return address;
        }

        if (StrUtil.isEmpty(address)) {
            return region;
        }

        // 检查address是否已经包含了region中的省市区信息
        // 先检查完整的region是否包含在address中
        if (address.contains(region)) {
            log.info("详细地址已包含完整省市区信息，去重处理 - region: {}, address: {}", region, address);
            return address;
        }

        // 再检查region中的各个部分是否包含在address中
        String[] regionParts = region.split("(?<=省|市|区|县)");
        boolean hasDuplicate = false;
        for (String part : regionParts) {
            if (StrUtil.isNotEmpty(part) && address.contains(part)) {
                hasDuplicate = true;
                break;
            }
        }

        if (hasDuplicate) {
            // 如果详细地址中已经包含了省市区信息，则只返回详细地址
            log.info("详细地址已包含省市区信息，去重处理 - region: {}, address: {}", region, address);
            return address;
        }

        // 如果没有重复，则拼接region和address
        return region + address;
    }

    /**
     * 生成订单号
     */
    private String generateOrderNo() {
        String dateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        long snowflakeId = IdUtil.getSnowflake().nextId();
        String suffix = String.format("%03d", snowflakeId % 1000);
        return "HM" + dateTimeStr + suffix;
    }

    /**
     * 生成线索ID
     */
    private String generateLeadId() {
        String dateTimeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS"));
        long snowflakeId = IdUtil.getSnowflake().nextId();
        String suffix = String.format("%03d", snowflakeId % 1000);
        return "XS" + dateTimeStr + suffix;
    }

    /**
     * 创建订单主表记录
     */
    private PublicbizOrderDO createMainOrder(EmployerOrderCreateReqVO createReqVO, String orderNo, String leadId,
            String userOneId, ServicePackageDO servicePackage) {

        PublicbizOrderDO orderDO = PublicbizOrderDO.builder()
                .orderNo(orderNo)
                .orderType("domestic") // 家政服务
                .businessLine("家政服务")
                .leadId(leadId)
                .projectName(servicePackage.getName()) // 使用数据库中的套餐名称
                .projectDescription(servicePackage.getServiceDescription()) // 使用数据库中的套餐描述
                .startDate(StrUtil.isNotEmpty(createReqVO.getStartDate()) ? LocalDate.parse(createReqVO.getStartDate())
                        : null)
                .totalAmount(createReqVO.getTotalAmount())
                .paidAmount(BigDecimal.ZERO)
                .refundAmount(BigDecimal.ZERO)
                .paymentStatus("pending")
                .orderStatus("pending_payment")
                .remark(createReqVO.getRemark())
                .settlementStatus("pending")
                .isSelectedForReconciliation(0)
                .build();

        // 设置创建人和更新人为用户的oneid
        orderDO.setCreator(userOneId);
        orderDO.setUpdater(userOneId);
        orderDO.setTenantId(1L);

        orderMapper.insert(orderDO);
        return orderDO;
    }

    /**
     * 创建家政服务订单详情记录
     */
    private DomesticOrderDO createDomesticOrder(EmployerOrderCreateReqVO createReqVO, PublicbizOrderDO orderDO,
            String userOneId, ServicePackageDO servicePackage) {
        String orderItemNo = generateOrderNo();

        // 根据addressId获取地址信息
        String customerAddress = getAddressByAddressId(createReqVO.getAddressId());
        String serviceAddress = getAddressByAddressId(createReqVO.getAddressId());

        DomesticOrderDO domesticOrderDO = new DomesticOrderDO();
        domesticOrderDO.setOrderId(orderDO.getId());
        domesticOrderDO.setOrderNo(orderItemNo);
        domesticOrderDO.setCustomerOneid(userOneId); // 使用雇主的openId关联mpUser表获取的oneid
        domesticOrderDO.setCustomerName(servicePackage.getAgencyName()); // 使用数据库中的机构名称
        domesticOrderDO.setCustomerPhone(createReqVO.getUserPhone());
        domesticOrderDO.setCustomerAddress(customerAddress); // 根据addressId查询mp_user_address表获取地址
        domesticOrderDO.setCustomerRemark(createReqVO.getRemark());
        domesticOrderDO.setServiceCategoryId(servicePackage.getCategoryId()); // 使用数据库中的分类ID
        domesticOrderDO.setServiceCategoryName(servicePackage.getCategory()); // 使用数据库中的分类名称
        domesticOrderDO.setServicePackageId(servicePackage.getId()); // 使用数据库中的套餐ID
        domesticOrderDO.setServicePackageName(servicePackage.getName()); // 使用数据库中的套餐名称
        domesticOrderDO.setServiceStartDate(
                StrUtil.isNotEmpty(createReqVO.getStartDate()) ? DateUtil.parseDate(createReqVO.getStartDate()) : null);
        domesticOrderDO.setServicePackageThumbnail(servicePackage.getThumbnail()); // 使用数据库中的套餐主图
        domesticOrderDO.setServicePackagePrice(servicePackage.getPrice()); // 使用数据库中的套餐价格
        domesticOrderDO.setServicePackageOriginalPrice(servicePackage.getOriginalPrice()); // 使用数据库中的套餐原价
        domesticOrderDO.setServicePackageUnit(servicePackage.getUnit()); // 使用数据库中的价格单位
        domesticOrderDO.setServicePackageType(servicePackage.getPackageType()); // 使用数据库中的套餐类型
        domesticOrderDO.setServiceDescription(servicePackage.getServiceDescription()); // 使用数据库中的服务描述
        domesticOrderDO.setServiceTimes(createReqVO.getServiceCount() != null ? createReqVO.getServiceCount() : 1);
        domesticOrderDO.setUnitPrice(servicePackage.getPrice()); // 使用数据库中的套餐价格作为单价
        domesticOrderDO.setTotalAmount(createReqVO.getTotalAmount());
        domesticOrderDO.setDiscountAmount(BigDecimal.ZERO);
        domesticOrderDO.setActualAmount(createReqVO.getTotalAmount());
        domesticOrderDO.setServiceAddress(serviceAddress); // 根据addressId查询mp_user_address表获取地址
        domesticOrderDO.setAgencyId(servicePackage.getAgencyId()); // 使用数据库中的机构ID
        domesticOrderDO.setAgencyName(servicePackage.getAgencyName()); // 使用数据库中的机构名称
        domesticOrderDO.setTaskCount(0);
        domesticOrderDO.setCompletedTaskCount(0);
        domesticOrderDO.setTaskProgress(BigDecimal.ZERO);
        domesticOrderDO.setCreateTime(new Date());
        domesticOrderDO.setUpdateTime(new Date());
        domesticOrderDO.setCreator(userOneId); // 设置创建人为雇主的oneid
        domesticOrderDO.setUpdater(userOneId); // 设置更新人为雇主的oneid
        domesticOrderDO.setTenantId(1L);
        domesticOrderDO.setServiceDuration(servicePackage.getSingleDurationHours() + "小时");
        domesticOrderDO.setServiceFrequency(buildServiceFrequency(servicePackage.getServiceIntervalType(),
                servicePackage.getServiceIntervalValue()));

        String serviceSchedule = buildServiceSchedule(createReqVO, servicePackage);
        log.info("设置服务时间安排 - serviceSchedule: {}", serviceSchedule);
        domesticOrderDO.setServiceSchedule(serviceSchedule);

        domesticOrderMapper.insert(domesticOrderDO);
        return domesticOrderDO;
    }

    /**
     * 创建订单日志
     */
    private void createOrderLog(PublicbizOrderDO orderDO, String logTitle, String logContent,
            String oldStatus, String newStatus, String userOneId) {
        // 构建JSON格式的日志内容
        String jsonLogContent = buildJsonLogContent(logTitle, logContent, orderDO.getProjectName());

        PublicbizOrderLogDO logDO = PublicbizOrderLogDO.builder()
                .orderNo(orderDO.getOrderNo())
                .logType("订单创建")
                .logTitle(logTitle)
                .logContent(jsonLogContent)
                .oldStatus(oldStatus)
                .newStatus(newStatus)
                .operatorId(null) // 系统操作
                .operatorName(userOneId)
                .operatorRole("雇主")
                .relatedPartyType("雇主")
                .relatedPartyName(orderDO.getProjectName())
                .build();

        orderLogMapper.insert(logDO);
    }

    /**
     * 构建JSON格式的日志内容
     */
    private String buildJsonLogContent(String action, String description, String projectName) {
        try {
            // 构建日志详情Map
            Map<String, Object> logDetails = new HashMap<>();
            logDetails.put("action", action);
            logDetails.put("details", new HashMap<>());
            logDetails.put("description", description + "，项目：" + projectName);

            return JSONUtil.toJsonStr(logDetails);
        } catch (Exception e) {
            log.error("构建JSON日志内容失败", e);
            // 如果JSON构建失败，返回简单的文本格式
            return "{\"action\":\"" + action + "\",\"details\":{},\"description\":\"" + description + "，项目："
                    + projectName + "\"}";
        }
    }

    /**
     * 构建创建响应
     */
    private EmployerOrderCreateRespVO buildCreateResponse(PublicbizOrderDO orderDO, DomesticOrderDO domesticOrderDO) {
        EmployerOrderCreateRespVO result = new EmployerOrderCreateRespVO();
        result.setOrderId(String.valueOf(orderDO.getId()));
        result.setOrderNo(orderDO.getOrderNo());
        result.setStatus(orderDO.getOrderStatus());
        result.setCreateTime(orderDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.setPayUrl(""); // 支付链接，后续集成支付系统时设置

        return result;
    }

    /**
     * 构建订单信息
     */
    private EmployerOrderListRespVO.OrderInfo buildOrderInfo(PublicbizOrderDO order, DomesticOrderDO domesticOrder) {
        EmployerOrderListRespVO.OrderInfo orderInfo = new EmployerOrderListRespVO.OrderInfo();
        orderInfo.setId(order.getId());
        orderInfo.setOrderNo(order.getOrderNo());
        orderInfo.setOrderType(order.getOrderType());
        orderInfo.setBusinessLine(order.getBusinessLine());
        orderInfo.setTotalAmount(order.getTotalAmount() != null ? order.getTotalAmount().toString() : "0.00");
        orderInfo.setPaidAmount(order.getPaidAmount() != null ? order.getPaidAmount().toString() : "0.00");
        orderInfo.setRefundAmount(order.getRefundAmount() != null ? order.getRefundAmount().toString() : "0.00");
        orderInfo.setPaymentStatus(order.getPaymentStatus());
        orderInfo.setOrderStatus(order.getOrderStatus());
        orderInfo.setCreateTime(order.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        orderInfo.setUpdateTime(order.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        // 构建家政订单详情
        EmployerOrderListRespVO.DomesticOrderInfo domesticOrderInfo = buildDomesticOrderInfo(domesticOrder);
        orderInfo.setDomesticOrder(domesticOrderInfo);

        return orderInfo;
    }

    /**
     * 构建家政订单详情信息
     */
    private EmployerOrderListRespVO.DomesticOrderInfo buildDomesticOrderInfo(DomesticOrderDO domesticOrder) {
        EmployerOrderListRespVO.DomesticOrderInfo domesticOrderInfo = new EmployerOrderListRespVO.DomesticOrderInfo();
        domesticOrderInfo.setId(domesticOrder.getId());
        domesticOrderInfo.setCustomerName(domesticOrder.getCustomerName());
        domesticOrderInfo.setCustomerPhone(maskPhoneNumber(domesticOrder.getCustomerPhone()));
        domesticOrderInfo.setCustomerAddress(domesticOrder.getCustomerAddress());
        domesticOrderInfo.setServiceCategoryName(domesticOrder.getServiceCategoryName());
        domesticOrderInfo.setServicePackageName(domesticOrder.getServicePackageName());
        domesticOrderInfo.setServicePackageThumbnail(domesticOrder.getServicePackageThumbnail());
        domesticOrderInfo.setServicePackagePrice(
                domesticOrder.getServicePackagePrice() != null ? domesticOrder.getServicePackagePrice().toString()
                        : "0.00");
        domesticOrderInfo.setServicePackageOriginalPrice(domesticOrder.getServicePackageOriginalPrice() != null
                ? domesticOrder.getServicePackageOriginalPrice().toString()
                : "0.00");
        domesticOrderInfo.setServicePackageUnit(domesticOrder.getServicePackageUnit());
        domesticOrderInfo.setServicePackageDuration(domesticOrder.getServicePackageDuration());
        domesticOrderInfo.setServicePackageType(domesticOrder.getServicePackageType());
        domesticOrderInfo.setServiceTimes(domesticOrder.getServiceTimes());
        domesticOrderInfo
                .setUnitPrice(domesticOrder.getUnitPrice() != null ? domesticOrder.getUnitPrice().toString() : "0.00");
        domesticOrderInfo.setActualAmount(
                domesticOrder.getActualAmount() != null ? domesticOrder.getActualAmount().toString() : "0.00");
        domesticOrderInfo.setServiceAddress(domesticOrder.getServiceAddress());
        domesticOrderInfo.setServiceAddressDetail(domesticOrder.getServiceAddressDetail());
        domesticOrderInfo.setPractitionerName(domesticOrder.getPractitionerName());
        domesticOrderInfo.setPractitionerPhone(maskPhoneNumber(domesticOrder.getPractitionerPhone()));
        domesticOrderInfo.setAgencyName(domesticOrder.getAgencyName());
        domesticOrderInfo.setTaskCount(domesticOrder.getTaskCount());
        domesticOrderInfo.setCompletedTaskCount(domesticOrder.getCompletedTaskCount());
        domesticOrderInfo.setTaskProgress(
                domesticOrder.getTaskProgress() != null ? domesticOrder.getTaskProgress().toString() : "0.00");

        // 设置服务开始和结束日期
        if (domesticOrder.getServiceStartDate() != null) {
            domesticOrderInfo.setServiceStartDate(DateUtil.formatDate(domesticOrder.getServiceStartDate()));
        }
        if (domesticOrder.getServiceEndDate() != null) {
            domesticOrderInfo.setServiceEndDate(DateUtil.formatDate(domesticOrder.getServiceEndDate()));
        }

        return domesticOrderInfo;
    }

    /**
     * 手机号脱敏处理
     */
    private String maskPhoneNumber(String phone) {
        if (StrUtil.isEmpty(phone) || phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    /**
     * 获取各状态订单数量统计
     */
    private Map<String, Long> getStatusCounts(String userOneId) {
        Map<String, Long> statusCounts = new HashMap<>();

        try {
            // 查询所有家政服务订单
            List<PublicbizOrderDO> allOrders = orderMapper.selectListByCreator(userOneId);
            List<PublicbizOrderDO> domesticOrders = allOrders.stream()
                    .filter(order -> "domestic".equals(order.getOrderType()))
                    .collect(Collectors.toList());

            // 统计各状态数量
            long allCount = domesticOrders.size();
            long pendingPaymentCount = domesticOrders.stream()
                    .filter(order -> "pending_payment".equals(order.getOrderStatus()))
                    .count();
            long executingCount = domesticOrders.stream()
                    .filter(order -> "executing".equals(order.getOrderStatus()))
                    .count();
            long completedCount = domesticOrders.stream()
                    .filter(order -> "completed".equals(order.getOrderStatus()))
                    .count();
            long cancelledCount = domesticOrders.stream()
                    .filter(order -> "cancelled".equals(order.getOrderStatus()))
                    .count();

            statusCounts.put("all", allCount);
            statusCounts.put("pending_payment", pendingPaymentCount);
            statusCounts.put("executing", executingCount);
            statusCounts.put("completed", completedCount);
            statusCounts.put("cancelled", cancelledCount);

        } catch (Exception e) {
            log.error("统计订单状态数量失败 - userOneId: {}, 错误: {}", userOneId, e.getMessage(), e);
            // 设置默认值
            statusCounts.put("all", 0L);
            statusCounts.put("pending_payment", 0L);
            statusCounts.put("executing", 0L);
            statusCounts.put("completed", 0L);
            statusCounts.put("cancelled", 0L);
        }

        return statusCounts;
    }

    @Override
    public EmployerOrderDetailRespVO getOrderDetail(String orderId) {
        log.info("获取雇主订单详情 - orderId: {}", orderId);

        try {
            // 1. 参数校验
            if (StrUtil.isEmpty(orderId)) {
                throw new ServiceException(400, "订单ID不能为空");
            }

            // 2. 查询订单主表信息
            PublicbizOrderDO orderDO = orderMapper.selectById(Long.valueOf(orderId));
            if (orderDO == null) {
                throw new ServiceException(404, "订单不存在");
            }

            // 3. 验证订单类型
            if (!"domestic".equals(orderDO.getOrderType())) {
                throw new ServiceException(400, "订单类型不正确");
            }

            // 4. 查询家政订单详情
            DomesticOrderDO domesticOrderDO = domesticOrderMapper.selectByOrderId(orderDO.getId());
            if (domesticOrderDO == null) {
                throw new ServiceException(404, "订单详情不存在");
            }

            // 5. 构建响应结果
            EmployerOrderDetailRespVO result = buildOrderDetailResponse(orderDO, domesticOrderDO);

            log.info("获取雇主订单详情成功 - orderId: {}", orderId);
            return result;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取雇主订单详情失败 - orderId: {}, 错误: {}",
                    orderId, e.getMessage(), e);
            throw new ServiceException(500, "获取订单详情失败，请稍后重试");
        }
    }

    /**
     * 构建订单详情响应
     */
    private EmployerOrderDetailRespVO buildOrderDetailResponse(PublicbizOrderDO orderDO,
            DomesticOrderDO domesticOrderDO) {
        EmployerOrderDetailRespVO result = new EmployerOrderDetailRespVO();

        // 设置基础订单信息
        result.setOrderId(orderDO.getId());
        result.setOrderNo(orderDO.getOrderNo());
        result.setOrderType(orderDO.getOrderType());
        result.setBusinessLine(orderDO.getBusinessLine());
        result.setOrderStatus(orderDO.getOrderStatus());
        result.setPaymentStatus(orderDO.getPaymentStatus());
        result.setTotalAmount(orderDO.getTotalAmount() != null ? orderDO.getTotalAmount().toString() : "0.00");
        result.setPaidAmount(orderDO.getPaidAmount() != null ? orderDO.getPaidAmount().toString() : "0.00");
        result.setRefundAmount(orderDO.getRefundAmount() != null ? orderDO.getRefundAmount().toString() : "0.00");
        result.setCreateTime(orderDO.getCreateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.setUpdateTime(orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        result.setRemarks(orderDO.getRemark());

        // 构建家政订单详情
        EmployerOrderDetailRespVO.DomesticOrderDetail domesticOrderDetail = buildDomesticOrderDetail(domesticOrderDO);
        result.setDomesticOrder(domesticOrderDetail);

        // 设置服务地址信息
        result.setServiceAddress(domesticOrderDO.getServiceAddress());
        result.setServiceAddressDetail(domesticOrderDO.getServiceAddressDetail());

        // 构建进度步骤
        List<EmployerOrderDetailRespVO.ProgressStep> progressSteps = buildProgressSteps(orderDO);
        result.setProgressSteps(progressSteps);

        return result;
    }

    /**
     * 构建家政订单详情
     */
    private EmployerOrderDetailRespVO.DomesticOrderDetail buildDomesticOrderDetail(DomesticOrderDO domesticOrderDO) {
        EmployerOrderDetailRespVO.DomesticOrderDetail detail = new EmployerOrderDetailRespVO.DomesticOrderDetail();

        detail.setId(domesticOrderDO.getId());
        detail.setCustomerName(domesticOrderDO.getCustomerName());
        detail.setCustomerPhone(maskPhoneNumber(domesticOrderDO.getCustomerPhone()));
        detail.setServiceCategoryName(domesticOrderDO.getServiceCategoryName());
        detail.setServicePackageName(domesticOrderDO.getServicePackageName());
        detail.setServicePackageThumbnail(domesticOrderDO.getServicePackageThumbnail());
        detail.setServicePackagePrice(
                domesticOrderDO.getServicePackagePrice() != null ? domesticOrderDO.getServicePackagePrice().toString()
                        : "0.00");
        detail.setServicePackageOriginalPrice(domesticOrderDO.getServicePackageOriginalPrice() != null
                ? domesticOrderDO.getServicePackageOriginalPrice().toString()
                : "0.00");
        detail.setServicePackageUnit(domesticOrderDO.getServicePackageUnit());
        detail.setServicePackageDuration(domesticOrderDO.getServicePackageDuration());
        detail.setServicePackageType(domesticOrderDO.getServicePackageType());
        detail.setServiceTimes(domesticOrderDO.getServiceTimes());
        detail.setUnitPrice(
                domesticOrderDO.getUnitPrice() != null ? domesticOrderDO.getUnitPrice().toString() : "0.00");
        detail.setActualAmount(
                domesticOrderDO.getActualAmount() != null ? domesticOrderDO.getActualAmount().toString() : "0.00");

        // 服务人员信息
        detail.setPractitionerName(domesticOrderDO.getPractitionerName());
        detail.setPractitionerPhone(maskPhoneNumber(domesticOrderDO.getPractitionerPhone()));

        // 根据practitioner_oneid查询阿姨详细信息
        if (StrUtil.isNotEmpty(domesticOrderDO.getPractitionerOneid())) {
            PractitionerDO practitioner = practitionerMapper.selectByAuntOneId(domesticOrderDO.getPractitionerOneid());
            if (practitioner != null) {
                // 设置阿姨头像
                detail.setPractitionerAvatar(practitioner.getAvatar() != null ? practitioner.getAvatar() : "");

                // 设置阿姨标签（暂时返回空值）
                detail.setPractitionerBadge("");

                // 设置阿姨经验（根据experience_years字段计算）
                if (practitioner.getExperienceYears() != null) {
                    detail.setPractitionerExperience(practitioner.getExperienceYears() + "年经验");
                } else {
                    detail.setPractitionerExperience("");
                }

                // 统计阿姨服务家庭数（根据practitioner_oneid统计订单数量）
                int familyCount = getPractitionerFamilyCount(domesticOrderDO.getPractitionerOneid());
                detail.setPractitionerFamilies("服务" + familyCount + "个家庭");
            } else {
                // 如果查询不到阿姨信息，设置默认值
                detail.setPractitionerAvatar("");
                detail.setPractitionerBadge("");
                detail.setPractitionerExperience("");
                detail.setPractitionerFamilies("");
            }
        } else {
            // 如果没有practitioner_oneid，设置默认值
            detail.setPractitionerAvatar("");
            detail.setPractitionerBadge("");
            detail.setPractitionerExperience("");
            detail.setPractitionerFamilies("");
        }

        // 机构信息
        detail.setAgencyName(domesticOrderDO.getAgencyName());

        // 任务进度信息
        detail.setTaskCount(domesticOrderDO.getTaskCount());
        detail.setCompletedTaskCount(domesticOrderDO.getCompletedTaskCount());
        detail.setTaskProgress(
                domesticOrderDO.getTaskProgress() != null ? domesticOrderDO.getTaskProgress().toString() : "0.00");

        // 服务时间
        if (domesticOrderDO.getServiceStartDate() != null) {
            detail.setServiceStartDate(DateUtil.formatDate(domesticOrderDO.getServiceStartDate()));
        }
        if (domesticOrderDO.getServiceEndDate() != null) {
            detail.setServiceEndDate(DateUtil.formatDate(domesticOrderDO.getServiceEndDate()));
        }

        // 构建服务安排
        List<EmployerOrderDetailRespVO.ServiceSchedule> serviceSchedules = buildServiceSchedules(domesticOrderDO);
        detail.setServiceSchedule(serviceSchedules);

        return detail;
    }

    /**
     * 构建服务安排
     */
    private List<EmployerOrderDetailRespVO.ServiceSchedule> buildServiceSchedules(DomesticOrderDO domesticOrderDO) {
        List<EmployerOrderDetailRespVO.ServiceSchedule> schedules = new ArrayList<>();

        // 这里可以根据实际业务逻辑构建服务安排
        // 目前先返回空列表，后续可以根据工单表或其他业务表来构建
        // TODO: 根据实际业务需求完善服务安排的构建逻辑

        return schedules;
    }

    /**
     * 统计阿姨服务家庭数
     * 根据practitioner_oneid统计该服务人员存在的订单数量
     *
     * @param practitionerOneId 阿姨OneID
     * @return 服务家庭数
     */
    private int getPractitionerFamilyCount(String practitionerOneId) {
        try {
            if (StrUtil.isEmpty(practitionerOneId)) {
                return 0;
            }

            // 查询该阿姨的所有订单
            List<DomesticOrderDO> orders = domesticOrderMapper.selectByPractitionerOneid(practitionerOneId);
            if (orders == null || orders.isEmpty()) {
                return 0;
            }

            // 统计不同的客户数量（服务家庭数）
            return (int) orders.stream()
                    .map(DomesticOrderDO::getCustomerOneid)
                    .filter(StrUtil::isNotEmpty)
                    .distinct()
                    .count();

        } catch (Exception e) {
            log.error("统计阿姨服务家庭数失败 - practitionerOneId: {}, 错误: {}", practitionerOneId, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 构建进度步骤
     */
    private List<EmployerOrderDetailRespVO.ProgressStep> buildProgressSteps(PublicbizOrderDO orderDO) {
        List<EmployerOrderDetailRespVO.ProgressStep> steps = new ArrayList<>();

        // 支付步骤
        EmployerOrderDetailRespVO.ProgressStep paymentStep = new EmployerOrderDetailRespVO.ProgressStep();
        paymentStep.setStep("payment");
        paymentStep.setStepName("已支付");
        if ("paid".equals(orderDO.getPaymentStatus()) || "refunded".equals(orderDO.getPaymentStatus())) {
            paymentStep.setStatus("completed");
            paymentStep.setCompletedTime(
                    orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            paymentStep.setStatus("pending");
        }
        steps.add(paymentStep);

        // 接单步骤
        EmployerOrderDetailRespVO.ProgressStep acceptedStep = new EmployerOrderDetailRespVO.ProgressStep();
        acceptedStep.setStep("accepted");
        acceptedStep.setStepName("已接单");
        if ("executing".equals(orderDO.getOrderStatus()) || "completed".equals(orderDO.getOrderStatus())) {
            acceptedStep.setStatus("completed");
            acceptedStep.setCompletedTime(
                    orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            acceptedStep.setStatus("pending");
        }
        steps.add(acceptedStep);

        // 服务中步骤
        EmployerOrderDetailRespVO.ProgressStep executingStep = new EmployerOrderDetailRespVO.ProgressStep();
        executingStep.setStep("executing");
        executingStep.setStepName("服务中");
        if ("executing".equals(orderDO.getOrderStatus())) {
            executingStep.setStatus("current");
            executingStep
                    .setStartedTime(orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else if ("completed".equals(orderDO.getOrderStatus())) {
            executingStep.setStatus("completed");
            executingStep.setCompletedTime(
                    orderDO.getUpdateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        } else {
            executingStep.setStatus("pending");
        }
        steps.add(executingStep);

        // 评价步骤
        EmployerOrderDetailRespVO.ProgressStep evaluationStep = new EmployerOrderDetailRespVO.ProgressStep();
        evaluationStep.setStep("evaluation");
        evaluationStep.setStepName("待评价");
        if ("completed".equals(orderDO.getOrderStatus())) {
            evaluationStep.setStatus("pending");
        } else {
            evaluationStep.setStatus("pending");
        }
        steps.add(evaluationStep);

        return steps;
    }

    /**
     * 构建服务频次
     *
     * @param serviceIntervalType  服务间隔类型
     * @param serviceIntervalValue 服务间隔数值
     * @return 服务频次字符串
     */
    private String buildServiceFrequency(String serviceIntervalType, Integer serviceIntervalValue) {
        if (StrUtil.isEmpty(serviceIntervalType) || serviceIntervalValue == null) {
            return "";
        }

        String typeText;
        switch (serviceIntervalType.toLowerCase()) {
            case "day":
                typeText = "每天";
                break;
            case "weekly":
                typeText = "每周";
                break;
            case "monthly":
                typeText = "每月";
                break;
            case "year":
                typeText = "每年";
                break;
            default:
                log.warn("未知的服务间隔类型: {}", serviceIntervalType);
                return "";
        }

        return typeText + serviceIntervalValue + "次";
    }

    /**
     * 构建服务时间安排JSON
     *
     * @param createReqVO    创建订单请求
     * @param servicePackage 服务套餐信息
     * @return 服务时间安排JSON字符串
     */
    private String buildServiceSchedule(EmployerOrderCreateReqVO createReqVO, ServicePackageDO servicePackage) {
        try {
            String packageType = servicePackage.getPackageType();
            log.info("构建服务时间安排JSON - 套餐类型: {}, 套餐ID: {}", packageType, servicePackage.getId());

            // 优先处理服务时间列表，如果有的话
            List<String> serviceTimes = createReqVO.getServiceTimes();
            if (serviceTimes != null && !serviceTimes.isEmpty()) {
                String result = JSONUtil.toJsonStr(serviceTimes);
                log.info("服务时间安排JSON: {}", result);
                return result;
            }

            // 如果没有服务时间列表，根据套餐类型处理
            if ("count-card".equals(packageType)) {
                // 次卡次数套餐：没有服务时间列表时返回空数组
                log.warn("次卡套餐但未提供服务时间列表");
                return "[]";
            } else if ("long-term".equals(packageType)) {
                // 长周期套餐：存储服务开始日期
                String startDate = createReqVO.getStartDate();
                if (StrUtil.isNotEmpty(startDate)) {
                    String result = JSONUtil.toJsonStr(startDate);
                    log.info("长周期套餐服务时间安排JSON: {}", result);
                    return result;
                } else {
                    log.warn("长周期套餐但未提供服务开始日期");
                    return "{}";
                }
            } else {
                log.warn("未知的套餐类型: {}", packageType);
                return "{}";
            }
        } catch (Exception e) {
            log.error("构建服务时间安排JSON失败", e);
            return "{}";
        }
    }
}
