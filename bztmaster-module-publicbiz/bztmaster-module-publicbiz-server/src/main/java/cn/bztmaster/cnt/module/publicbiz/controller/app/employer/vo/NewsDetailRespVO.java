package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 资讯详情响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 资讯详情响应")
@Data
public class NewsDetailRespVO {

    @Schema(description = "资讯ID", example = "1")
    private Long id;

    @Schema(description = "资讯标题", example = "家政服务行业新标准正式发布实施")
    private String newsTitle;

    @Schema(description = "资讯摘要", example = "为了更好地规范家政服务行业，提升服务质量...")
    private String newsSummary;

    @Schema(description = "资讯内容", example = "<p>这里是资讯的详细内容，支持HTML格式...</p>")
    private String newsContent;

    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    @Schema(description = "分类名称", example = "行业动态")
    private String categoryName;

    @Schema(description = "封面图片URL", example = "https://example.com/news1.jpg")
    private String coverImageUrl;

    @Schema(description = "关联素材文章ID", example = "123")
    private Long materialId;

    @Schema(description = "作者", example = "平台管理员")
    private String author;

    @Schema(description = "发布时间", example = "2024-01-15 10:00:00")
    private LocalDateTime publishTime;

    @Schema(description = "状态：draft-草稿/published-已发布/offline-已下架", example = "published")
    private String status;

    @Schema(description = "浏览次数", example = "1250")
    private Integer viewCount;

    @Schema(description = "点赞次数", example = "89")
    private Integer likeCount;

    @Schema(description = "分享次数", example = "23")
    private Integer shareCount;

    @Schema(description = "评论数", example = "15")
    private Long commentCount;

    @Schema(description = "排序值", example = "1")
    private Integer sort;

    @Schema(description = "创建时间", example = "2024-01-15 10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2024-01-15 10:00:00")
    private LocalDateTime updateTime;

}