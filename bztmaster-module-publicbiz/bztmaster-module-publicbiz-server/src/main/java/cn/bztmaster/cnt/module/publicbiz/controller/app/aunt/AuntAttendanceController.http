### 阿姨考勤管理接口测试

### 1. 获取阿姨月度考勤统计信息
GET {{baseUrl}}/publicbiz/aunt/attendance/summary?oneId=aunt_001&year=2024&month=12
Content-Type: application/json
Authorization: Bearer {{token}}

### 2. 提交请假或调休申请
POST {{baseUrl}}/publicbiz/aunt/attendance/apply
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "oneId": "aunt_001",
  "applyType": "LEAVE",
  "startDate": "2025-07-01",
  "startTime": "09:00",
  "endDate": "2025-07-03",
  "endTime": "18:00",
  "reason": "家中有急事需要处理",
  "duration": 3
}

### 3. 获取阿姨历史请假/调休申请列表
GET {{baseUrl}}/publicbiz/aunt/attendance/history?oneId=aunt_001&pageNo=1&pageSize=20&applyType=ALL&status=ALL
Content-Type: application/json
Authorization: Bearer {{token}}

### 4. 更新请假/调休申请状态
POST {{baseUrl}}/publicbiz/aunt/attendance/approve
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "applyId": "apply_20250701001",
  "action": "APPROVE",
  "remark": "同意申请",
  "approverId": "admin_001"
}
