package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 阿姨资质证书响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 阿姨资质证书响应 VO")
@Data
public class AuntCertificateRespVO {

    @Schema(description = "资质证书列表")
    private List<Certificate> certificates;

    @Schema(description = "资质证书")
    @Data
    public static class Certificate {
        @Schema(description = "证书ID", example = "1")
        private Long id;

        @Schema(description = "文件类型", example = "skill_cert")
        private String fileType;

        @Schema(description = "证书名称", example = "高级育婴师证")
        private String fileName;

        @Schema(description = "证书图片URL", example = "https://example.com/cert1.jpg")
        private String fileUrl;

        @Schema(description = "证书编号", example = "YY123456787654")
        private String certNumber;

        @Schema(description = "有效期至", example = "2025-12-31")
        private String validUntil;

        @Schema(description = "排序", example = "1")
        private Integer sortOrder;

        @Schema(description = "状态", example = "1")
        private Integer status;
    }
}
