package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 管理后台 - 高校实践订单纸质合同上传请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单纸质合同上传请求")
@Data
public class UniversityPracticeOrderPaperContractUploadReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "HT001")
    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

    @Schema(description = "合同编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "HT001")
    @NotBlank(message = "合同编号不能为空")
    private String contractNumber;

    @Schema(description = "合同名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试合同")
    @NotBlank(message = "合同名称不能为空")
    private String contractName;

    @Schema(description = "签署日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15")
    @NotNull(message = "签署日期不能为空")
    private LocalDate signDate;

    @Schema(description = "合同金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "1000.00")
    @NotNull(message = "合同金额不能为空")
    @DecimalMin(value = "0.01", message = "合同金额必须大于0")
    private BigDecimal contractAmount;

    @Schema(description = "已上传的文件URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/files/contract_123.pdf")
    @NotBlank(message = "文件URL不能为空")
    private String fileUrl;
}
