package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 机构统计响应 VO
 */
@Schema(description = "机构统计响应")
@Data
public class AgencyStatisticsRespVO {

    @Schema(description = "服务工单数", example = "150")
    private Long serviceOrderCount;

    @Schema(description = "面试成功率", example = "85.5")
    private BigDecimal interviewSuccessRate;

    @Schema(description = "机构评分", example = "4.8")
    private BigDecimal agencyRating;

    @Schema(description = "客户投诉率", example = "2.3")
    private BigDecimal complaintRate;

    @Schema(description = "在职阿姨总数", example = "45")
    private Long totalPractitioners;

    @Schema(description = "新增阿姨数", example = "8")
    private Long newPractitioners;

    @Schema(description = "流水阿姨数", example = "12")
    private Long flowPractitioners;

    @Schema(description = "上单阿姨数", example = "25")
    private Long activeOrderPractitioners;

    @Schema(description = "订单总金额", example = "125000.00")
    private BigDecimal totalOrderAmount;

    @Schema(description = "我方收入", example = "18750.00")
    private BigDecimal ourIncome;

    @Schema(description = "已结算金额", example = "15000.00")
    private BigDecimal settledAmount;

    @Schema(description = "待结算金额", example = "3750.00")
    private BigDecimal unsettledAmount;
} 