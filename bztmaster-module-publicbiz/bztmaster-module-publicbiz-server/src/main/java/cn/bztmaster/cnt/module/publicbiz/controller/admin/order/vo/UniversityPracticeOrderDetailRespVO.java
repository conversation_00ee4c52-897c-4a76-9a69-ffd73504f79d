package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 高校实践订单详情 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单详情 Response VO")
@Data
public class UniversityPracticeOrderDetailRespVO {

    @Schema(description = "订单ID", example = "1")
    private Long id;

    @Schema(description = "订单号", example = "HP202406001")
    private String orderNo;

    @Schema(description = "订单类型", example = "practice")
    private String orderType;

    @Schema(description = "业务线", example = "高校实践")
    private String businessLine;

    @Schema(description = "关联商机ID", example = "OPP001")
    private String opportunityId;

    @Schema(description = "关联线索ID", example = "LEAD001")
    private String leadId;

    @Schema(description = "项目名称", example = "2024年暑期社会实践项目")
    private String projectName;

    @Schema(description = "项目描述", example = "为XX大学经济管理学院学生提供暑期社会实践机会")
    private String projectDescription;

    @Schema(description = "开始日期", example = "2024-07-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-08-30")
    private LocalDate endDate;

    @Schema(description = "订单总金额", example = "580000.00")
    private BigDecimal totalAmount;

    @Schema(description = "已支付金额", example = "0.00")
    private BigDecimal paidAmount;

    @Schema(description = "退款金额", example = "0.00")
    private BigDecimal refundAmount;

    @Schema(description = "支付状态", example = "pending")
    private String paymentStatus;

    @Schema(description = "订单状态", example = "pending_approval")
    private String orderStatus;

    @Schema(description = "负责人ID", example = "1001")
    private Long managerId;

    @Schema(description = "负责人姓名", example = "张三")
    private String managerName;

    @Schema(description = "负责人电话", example = "13800138000")
    private String managerPhone;

    @Schema(description = "合同类型", example = "electronic")
    private String contractType;

    @Schema(description = "合同文件URL", example = "https://example.com/contracts/HT-202406001.pdf")
    private String contractFileUrl;

    @Schema(description = "合同状态", example = "unsigned")
    private String contractStatus;

    @Schema(description = "备注", example = "暑期社会实践项目")
    private String remark;

    @Schema(description = "结算状态", example = "pending")
    private String settlementStatus;

    @Schema(description = "结算时间")
    private LocalDateTime settlementTime;

    @Schema(description = "结算方式", example = "银行转账")
    private String settlementMethod;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // 高校实践订单详情
    @Schema(description = "合作高校名称", example = "XX大学经济管理学院")
    private String universityName;

    @Schema(description = "高校联系人", example = "李老师")
    private String universityContact;

    @Schema(description = "高校联系电话", example = "010-12345678")
    private String universityPhone;

    @Schema(description = "高校联系邮箱", example = "<EMAIL>")
    private String universityEmail;

    @Schema(description = "合作企业名称", example = "ABC科技有限公司")
    private String enterpriseName;

    @Schema(description = "企业联系人", example = "王经理")
    private String enterpriseContact;

    @Schema(description = "企业联系电话", example = "010-87654321")
    private String enterprisePhone;

    @Schema(description = "企业联系邮箱", example = "<EMAIL>")
    private String enterpriseEmail;

    @Schema(description = "参与学生人数", example = "50")
    private Integer studentCount;

    @Schema(description = "实践时长", example = "2个月")
    private String practiceDuration;

    @Schema(description = "实践地点", example = "北京市朝阳区")
    private String practiceLocation;

    @Schema(description = "服务费", example = "500000.00")
    private BigDecimal serviceFee;

    @Schema(description = "管理费", example = "80000.00")
    private BigDecimal managementFee;

    @Schema(description = "其他费用", example = "0.00")
    private BigDecimal otherFee;

    // 收款信息字段（使用现有字段）
    @Schema(description = "收款金额", example = "580000.00")
    private BigDecimal collectionAmount;

    @Schema(description = "收款方式", example = "bank_transfer")
    private String collectionMethod;

    @Schema(description = "收款日期", example = "2024-06-21")
    private LocalDate collectionDate;

    @Schema(description = "操作人", example = "李四")
    private String operatorName;

    @Schema(description = "收款备注", example = "银行转账收款，已确认到账")
    private String collectionRemark;

}





