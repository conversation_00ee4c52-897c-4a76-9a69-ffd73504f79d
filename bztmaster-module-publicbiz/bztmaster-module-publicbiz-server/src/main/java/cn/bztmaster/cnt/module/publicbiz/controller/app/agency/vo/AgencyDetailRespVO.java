package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "小程序 - 机构详情 Response VO")
@Data
public class AgencyDetailRespVO {

    @Schema(description = "机构ID")
    private Long id;

    @Schema(description = "机构编号")
    private String agencyNo;

    @Schema(description = "机构名称")
    private String name;

    @Schema(description = "机构简称")
    private String shortName;

    @Schema(description = "机构类型")
    private String agencyType;

    @Schema(description = "法定代表人")
    private String legalRepresentative;

    @Schema(description = "统一社会信用代码")
    private String unifiedSocialCreditCode;

    @Schema(description = "成立日期")
    private String establishmentDate;

    @Schema(description = "注册地址")
    private String registeredAddress;

    @Schema(description = "经营地址")
    private String operatingAddress;

    @Schema(description = "经营范围")
    private String businessScope;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系电话")
    private String contactPhone;

    @Schema(description = "联系邮箱")
    private String contactEmail;

    @Schema(description = "机构地址")
    private String agencyAddress;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区县")
    private String district;

    @Schema(description = "街道")
    private String street;

    @Schema(description = "详细地址")
    private String detailAddress;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "位置精度")
    private String locationAccuracy;

    @Schema(description = "合作状态")
    private String cooperationStatus;

    @Schema(description = "合同编号")
    private String contractNo;

    @Schema(description = "合同开始日期")
    private String contractStartDate;

    @Schema(description = "合同结束日期")
    private String contractEndDate;

    @Schema(description = "佣金比例")
    private BigDecimal commissionRate;

    @Schema(description = "审核状态")
    private String reviewStatus;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // 前端展示所需字段
    @Schema(description = "机构Logo")
    private String logo;

    @Schema(description = "机构Banner")
    private String banner;

    @Schema(description = "评分")
    private BigDecimal rating;

    @Schema(description = "评价数量")
    private Integer reviewCount;

    @Schema(description = "服务数量")
    private Integer serviceCount;

    @Schema(description = "成立年限")
    private Integer establishTime;

    // 新增字段 - 开票信息
    @Schema(description = "开票名称")
    private String invoiceName;

    @Schema(description = "纳税人识别号")
    private String taxpayerId;

    @Schema(description = "开户银行")
    private String bankName;

    @Schema(description = "银行账号")
    private String bankAccount;

    // 新增字段 - 资质信息
    @Schema(description = "服务资质等级")
    private String serviceQualificationLevel;

    @Schema(description = "员工人数")
    private Integer employeeCount;

    @Schema(description = "服务区域")
    private String serviceArea;

    // 新增字段 - 注册资本
    @Schema(description = "注册资本（万元）")
    private BigDecimal registeredCapital;
}