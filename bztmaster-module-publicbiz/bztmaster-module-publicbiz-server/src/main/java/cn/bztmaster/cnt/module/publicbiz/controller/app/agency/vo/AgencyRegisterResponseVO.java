package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 机构注册响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构注册响应 VO")
@Data
public class AgencyRegisterResponseVO {

    @Schema(description = "申请编号", example = "AG202401150001")
    private String applicationId;

    @Schema(description = "机构ID", example = "123")
    private Long agencyId;

    @Schema(description = "审核状态", example = "pending")
    private String reviewStatus;

    @Schema(description = "提交时间", example = "2024-01-15 10:30:00")
    private LocalDateTime submitTime;

    @Schema(description = "预计审核时间", example = "3-5个工作日")
    private String estimatedReviewTime;
}
