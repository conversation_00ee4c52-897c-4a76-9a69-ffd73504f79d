### 记录打卡
POST {{appApi}}/publicbiz/aunt/punch/record
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}
Content-Type: application/json

{
  "scheduleId": "1",
  "punchType": 1,
  "punchLocation": "北京市朝阳区建国路88号",
  "punchLatitude": 39.908823,
  "punchLongitude": 116.397470,
  "photoCount": 2,
  "photoUrls": "https://example.com/photo1.jpg,https://example.com/photo2.jpg",
  "remark": "按时到达服务地点"
}

### 获得打卡记录列表
GET {{appApi}}/publicbiz/aunt/punch/record/list
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### 获得打卡记录列表（按排班ID筛选）
GET {{appApi}}/publicbiz/aunt/punch/record/list?scheduleId=1
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### 获得打卡记录列表（按日期范围筛选）
GET {{appApi}}/publicbiz/aunt/punch/record/list?startDate=2024-12-01&endDate=2024-12-31
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}} 