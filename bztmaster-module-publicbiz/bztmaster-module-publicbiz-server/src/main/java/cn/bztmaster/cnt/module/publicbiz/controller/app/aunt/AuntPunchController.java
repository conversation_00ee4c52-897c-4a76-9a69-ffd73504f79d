package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntPunchRecordReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntPunchRecordRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntPunchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 阿姨打卡")
@RestController
@RequestMapping("/publicbiz/aunt/punch")
@Validated
public class AuntPunchController {

    @Resource
    private AuntPunchService auntPunchService;

    @PostMapping("/record")
    @Operation(summary = "记录打卡")
    @PermitAll
    public CommonResult<Boolean> recordPunch(@Valid @RequestBody AuntPunchRecordReqVO reqVO) {
        // 验证阿姨信息是否存在
        if (reqVO.getAuntOneId() == null || reqVO.getAuntOneId().trim().isEmpty()) {
            return CommonResult.error(400, "阿姨OneID不能为空");
        }
        
        // 记录打卡
        auntPunchService.recordPunch(reqVO);
        
        return success(Boolean.TRUE);
    }

    @GetMapping("/record/list")
    @Operation(summary = "获得打卡记录列表")
    @PermitAll
    public CommonResult<AuntPunchRecordRespVO> getPunchRecordList(
            @RequestParam("oneId") String oneId,
            @RequestParam(required = false) Long scheduleId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {

        // 验证阿姨信息是否存在
        if (oneId == null || oneId.trim().isEmpty()) {
            return CommonResult.error(400, "阿姨OneID不能为空");
        }
        
        // 获取打卡记录列表
        AuntPunchRecordRespVO result = auntPunchService.getPunchRecordList(oneId, scheduleId, startDate, endDate);
        
        if (result == null) {
            return CommonResult.error(404, "阿姨信息不存在");
        }
        
        return success(result);
    }

} 