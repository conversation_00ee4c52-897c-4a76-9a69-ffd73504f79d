package cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "资源中心 - 合作伙伴统计卡片 Response VO")
public class PartnerStatRespVO {
    @Schema(description = "合作伙伴总数")
    private Integer partnerTotal;

    @Schema(description = "活跃合作伙伴数")
    private Integer partnerActive;

    @Schema(description = "待合作伙伴数")
    private Integer partnerPending;

    @Schema(description = "风险合作伙伴数")
    private Integer partnerRisk;
}