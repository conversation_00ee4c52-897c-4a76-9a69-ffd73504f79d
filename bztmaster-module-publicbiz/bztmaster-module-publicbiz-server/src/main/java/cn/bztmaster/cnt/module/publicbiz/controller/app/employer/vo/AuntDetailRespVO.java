package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 阿姨详情响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 阿姨详情响应 VO")
@Data
public class AuntDetailRespVO {

    @Schema(description = "阿姨ID", example = "12345678-1234-1234-1234-123456789012")
    private String id;

    @Schema(description = "阿姨姓名", example = "王阿姨")
    private String name;

    @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "评分", example = "4.5")
    private BigDecimal rating;

    @Schema(description = "是否实名认证", example = "true")
    private Boolean isVerified;

    @Schema(description = "是否平台自营签约", example = "true")
    private Boolean isPlatformContract;

    @Schema(description = "服务标签列表")
    private List<ServiceTag> serviceTags;

    @Schema(description = "资质证书列表")
    private List<Certificate> certificates;

    @Schema(description = "统计数据")
    private Stats stats;

    @Schema(description = "个人简介信息")
    private Profile profile;

    @Schema(description = "服务标签")
    @Data
    public static class ServiceTag {
        @Schema(description = "标签ID", example = "1")
        private Long tagId;

        @Schema(description = "标签名称", example = "高级育婴师证")
        private String tagName;

        @Schema(description = "标签类型", example = "certificate")
        private String tagType;
    }

    @Schema(description = "资质证书")
    @Data
    public static class Certificate {
        @Schema(description = "证书名称", example = "高级育婴师证")
        private String name;

        @Schema(description = "证书编号", example = "YY123456787654")
        private String number;

        @Schema(description = "有效期至", example = "2025-12-31")
        private String validUntil;
    }

    @Schema(description = "统计数据")
    @Data
    public static class Stats {
        @Schema(description = "服务家庭数", example = "15")
        private Integer families;

        @Schema(description = "从业年限", example = "5")
        private Integer years;

        @Schema(description = "响应时间(小时)", example = "3")
        private Integer responseTime;

        @Schema(description = "复购率", example = "98")
        private Integer repurchaseRate;
    }

    @Schema(description = "个人简介信息")
    @Data
    public static class Profile {
        @Schema(description = "个人介绍", example = "从事家政行业5年,服务超过200个家庭。持有高级育婴师证、健康证。擅长高端住宅保洁、深度收纳整理与0-3岁婴幼儿看护。为人耐心、细致,责任心强。")
        private String introduction;

        @Schema(description = "籍贯", example = "河南")
        private String hometown;

        @Schema(description = "年龄", example = "35")
        private Integer age;

        @Schema(description = "从业经验", example = "5")
        private Integer experience;
    }
}
