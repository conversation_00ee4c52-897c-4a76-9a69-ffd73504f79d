package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 阿姨注册详情响应 VO
 *
 * <AUTHOR>
 */
@Data
public class AuntRegisterDetailRespVO {

    /**
     * 申请ID
     */
    private String applicationId;

    /**
     * 阿姨OneID
     */
    private String auntOneId;

    /**
     * 申请状态
     */
    private String status;

    /**
     * 提交时间
     */
    private LocalDateTime submitTime;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 拒绝原因
     */
    private String rejectReason;

    /**
     * 基本信息
     */
    private BasicInfo basicInfo;

    /**
     * 机构信息
     */
    private AgencyInfo agencyInfo;

    /**
     * 证件信息
     */
    private Certificates certificates;

    @Data
    public static class BasicInfo {

        /**
         * 姓名
         */
        private String name;

        /**
         * 手机号
         */
        private String phone;

        /**
         * 身份证号
         */
        private String idCard;

        /**
         * 申请时间
         */
        private java.time.LocalDateTime submitTime;
    }

    @Data
    public static class AgencyInfo {

        /**
         * 机构ID
         */
        private Long agencyId;

        /**
         * 机构名称
         */
        private String agencyName;

        /**
         * 申请时间
         */
        private java.time.LocalDateTime submitTime;
    }

    @Data
    public static class Certificates {

        /**
         * 身份证正面照片URL
         */
        private String idCardFront;

        /**
         * 身份证背面照片URL
         */
        private String idCardBack;

        /**
         * 健康证照片URL
         */
        private String healthCert;

        /**
         * 技能证书照片URL
         */
        private String skillCert;
    }
}
