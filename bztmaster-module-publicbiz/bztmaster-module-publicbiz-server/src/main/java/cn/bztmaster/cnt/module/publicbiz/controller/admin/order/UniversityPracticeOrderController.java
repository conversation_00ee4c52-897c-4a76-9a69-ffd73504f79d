package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.framework.excel.core.util.ExcelUtils;
import com.mzt.logapi.starter.annotation.LogRecord;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderSaveReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderApprovalReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderApprovalRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderContractUploadReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderContractUploadRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPaperContractUploadReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderPaperContractUploadRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderDeleteReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderDetailReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderDropdownDataReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderDropdownDataRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderExportReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderExcelVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderApprovalInitiateReqVO;

import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderApprovalRecordPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderApprovalRecordPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderCollectionReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderCollectionRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.order.UniversityPracticeOrderService;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderDropdownDataService;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderApprovalService;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionRespDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.Map;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;
import java.util.HashMap;
import cn.hutool.core.util.StrUtil;

/**
 * 管理后台 - 高校实践订单
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 高校实践订单")
@RestController
@RequestMapping("/publicbiz/order")
@Validated
@Slf4j
public class UniversityPracticeOrderController {

    @Resource
    private UniversityPracticeOrderService universityPracticeOrderService;

    @Resource
    private OrderDropdownDataService orderDropdownDataService;

    @Resource
    private OrderApprovalService orderApprovalService;

    @PostMapping("/create")
    @Operation(summary = "创建高校实践订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:create')")
    public CommonResult<Long> createOrder(@Valid @RequestBody UniversityPracticeOrderSaveReqVO createReqVO) {
        Long id = universityPracticeOrderService.createOrder(createReqVO);
        return success(id);
    }

    @PostMapping("/update")
    @Operation(summary = "更新高校实践订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:update')")
    public CommonResult<Boolean> updateOrder(@Valid @RequestBody UniversityPracticeOrderSaveReqVO updateReqVO) {
        universityPracticeOrderService.updateOrder(updateReqVO);
        return success(true);
    }

    @PostMapping("/delete")
    @Operation(summary = "删除高校实践订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:delete')")
    public CommonResult<Boolean> deleteOrder(@Valid @RequestBody UniversityPracticeOrderDeleteReqVO deleteReqVO) {
        universityPracticeOrderService.deleteOrder(deleteReqVO.getId(), deleteReqVO.getOrderNo());
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得高校实践订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<UniversityPracticeOrderDetailRespVO> getOrder(@RequestParam("id") Long id) {
        UniversityPracticeOrderDetailRespVO order = universityPracticeOrderService.getOrder(id);
        return success(order);
    }

    @GetMapping("/get-by-order-no")
    @Operation(summary = "通过订单号获得高校实践订单")
    @Parameter(name = "orderNo", description = "订单号", required = true, example = "HP202406001")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<UniversityPracticeOrderDetailRespVO> getOrderByOrderNo(@RequestParam("orderNo") String orderNo) {
        UniversityPracticeOrderDetailRespVO order = universityPracticeOrderService.getOrderByOrderNo(orderNo);
        return success(order);
    }

    @GetMapping("/page")
    @Operation(summary = "获得高校实践订单分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<PageResult<UniversityPracticeOrderPageRespVO>> getOrderPage(@Valid UniversityPracticeOrderPageReqVO pageReqVO) {
        PageResult<UniversityPracticeOrderPageRespVO> pageResult = universityPracticeOrderService.getOrderPage(pageReqVO);
        return success(pageResult);
    }

    @PostMapping("/detail")
    @Operation(summary = "查看高校实践订单详情")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<UniversityPracticeOrderDetailRespVO> getOrderDetail(@RequestBody UniversityPracticeOrderDetailReqVO detailReqVO) {
        UniversityPracticeOrderDetailRespVO order = universityPracticeOrderService.getOrder(detailReqVO.getId());
        return success(order);
    }

    @GetMapping("/operation-logs")
    @Operation(summary = "查询高校实践订单操作日志")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<PageResult<UniversityPracticeOrderLogPageRespVO>> getOrderOperationLogs(@Valid UniversityPracticeOrderLogPageReqVO pageReqVO) {
        PageResult<UniversityPracticeOrderLogPageRespVO> pageResult = universityPracticeOrderService.getOrderOperationLogs(pageReqVO);
        return success(pageResult);
    }



    @PostMapping("/upload-contract")
    @Operation(summary = "上传高校实践订单合同")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:upload')")
    public CommonResult<UniversityPracticeOrderContractUploadRespVO> uploadContract(@Valid UniversityPracticeOrderContractUploadReqVO uploadReqVO, 
                                                                                 @RequestParam("file") MultipartFile file) {
        UniversityPracticeOrderContractUploadRespVO result = universityPracticeOrderService.uploadContract(uploadReqVO, file);
        return success(result);
    }

    @PostMapping("/university-practice-upload-paper-contract")
    @Operation(summary = "上传高校实践订单纸质合同")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:upload')")
    public CommonResult<Map<String, Object>> uploadPaperContract(
            @RequestParam("orderId") Long orderId,
            @RequestParam("orderNo") String orderNo,
            @RequestParam("contractNumber") String contractNumber,
            @RequestParam("contractName") String contractName,
            @RequestParam("signDate") String signDate,
            @RequestParam("contractAmount") BigDecimal contractAmount,
            @RequestParam("fileUrl") String fileUrl) {
        
        // 构建VO对象
        UniversityPracticeOrderPaperContractUploadReqVO uploadReqVO = new UniversityPracticeOrderPaperContractUploadReqVO();
        uploadReqVO.setOrderId(orderId);
        uploadReqVO.setOrderNo(orderNo);
        uploadReqVO.setContractNumber(contractNumber);
        uploadReqVO.setContractName(contractName);
        uploadReqVO.setSignDate(LocalDate.parse(signDate));
        uploadReqVO.setContractAmount(contractAmount);
        uploadReqVO.setFileUrl(fileUrl);
        
        UniversityPracticeOrderPaperContractUploadRespVO result = universityPracticeOrderService.uploadPaperContract(uploadReqVO);
        
        // 构建Map响应对象以匹配方法签名
        Map<String, Object> responseMap = new HashMap<>();
        responseMap.put("success", true);
        responseMap.put("orderId", result.getOrderId());
        responseMap.put("orderNo", result.getOrderNo());
        responseMap.put("contractNumber", result.getContractNumber());
        responseMap.put("contractName", result.getContractName());
        responseMap.put("signDate", result.getSignDate());
        responseMap.put("contractAmount", result.getContractAmount());
        responseMap.put("fileUrl", result.getFileUrl());
        responseMap.put("message", "高校实践订单纸质合同上传成功");
        
        return success(responseMap);
    }

    @GetMapping("/download-contract")
    @Operation(summary = "下载高校实践订单合同附件")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:download')")
    public void downloadContract(@RequestParam("orderId") Long orderId,
                               @RequestParam("orderNo") String orderNo,
                               @RequestParam("fileUrl") String fileUrl,
                               HttpServletResponse response) throws IOException {
        // 获取合同信息
        Map<String, Object> contractInfo = universityPracticeOrderService.getContractInfo(orderId, orderNo);
        
        // 从合同信息中获取文件URL
        String actualFileUrl = (String) contractInfo.get("fileUrl");
        if (actualFileUrl == null || actualFileUrl.isEmpty()) {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            return;
        }
        
        // 下载文件
        downloadFileFromUrl(actualFileUrl, response);
    }
    
    /**
     * 从URL下载文件并写入HTTP响应
     */
    private void downloadFileFromUrl(String fileUrl, HttpServletResponse response) throws IOException {
        URL url = new URL(fileUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(30000);
        
        try {
            // 获取文件信息
            String fileName = getFileNameFromUrl(fileUrl);
            String contentType = connection.getContentType();
            long contentLength = connection.getContentLength();
            
            // 设置响应头
            response.setContentType(contentType != null ? contentType : "application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            if (contentLength > 0) {
                response.setContentLengthLong(contentLength);
            }
            
            // 复制文件流到响应流
            try (InputStream inputStream = connection.getInputStream();
                 OutputStream outputStream = response.getOutputStream()) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
                outputStream.flush();
            }
            
        } finally {
            connection.disconnect();
        }
    }
    
    /**
     * 从URL中提取文件名
     */
    private String getFileNameFromUrl(String fileUrl) {
        try {
            String fileName = fileUrl.substring(fileUrl.lastIndexOf("/") + 1);
            // 移除查询参数
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }
            // 如果文件名为空，使用默认名称
            if (StrUtil.isBlank(fileName)) {
                fileName = "contract_" + System.currentTimeMillis() + ".pdf";
            }
            return fileName;
        } catch (Exception e) {
            return "contract_" + String.valueOf(System.currentTimeMillis()) + ".pdf";
        }
    }

    @GetMapping("/statistics/overview")
    @Operation(summary = "获取高校实践订单统计概览")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<UniversityPracticeOrderStatisticsRespVO> getOrderStatisticsOverview() {
        UniversityPracticeOrderStatisticsRespVO statistics = universityPracticeOrderService.getOrderStatisticsOverview();
        return success(statistics);
    }

    @GetMapping("/test")
    @Operation(summary = "测试接口")
    public CommonResult<String> test() {
        log.info("测试接口被调用");
        return success("接口正常工作");
    }

    @PostMapping("/test-dropdown")
    @Operation(summary = "测试下拉数据接口")
    public CommonResult<String> testDropdown() {
        try {
            log.info("测试下拉数据接口被调用");
            
            // 创建测试请求对象
            OrderDropdownDataReqVO testReq = new OrderDropdownDataReqVO();
            testReq.setOrderType("practice");
            testReq.setBusinessLine("高校实践");
            
            log.info("测试请求参数：{}", testReq);
            
            // 调用服务
            OrderDropdownDataRespVO result = orderDropdownDataService.getDropdownData(testReq);
            
            log.info("测试结果：商机={}, 线索={}", 
                    result.getBusinessOptions().size(), 
                    result.getLeadOptions().size());
            
            return success("测试成功，商机数量：" + result.getBusinessOptions().size() + 
                          "，线索数量：" + result.getLeadOptions().size());
            
        } catch (Exception e) {
            log.error("测试下拉数据接口失败", e);
            return CommonResult.error(500, "测试失败：" + e.getMessage());
        }
    }

    @PostMapping("/dropdown-data")
    @Operation(summary = "获取订单下拉数据")
    public CommonResult<OrderDropdownDataRespVO> getDropdownData(@Valid @RequestBody OrderDropdownDataReqVO reqVO) {
        try {
            log.info("开始获取下拉数据，请求参数：{}", reqVO);
            
            // 参数验证
            if (reqVO == null) {
                log.error("请求参数为空");
                return CommonResult.error(500, "请求参数不能为空");
            }
            
            if (reqVO.getOrderType() == null || reqVO.getOrderType().trim().isEmpty()) {
                log.error("订单类型为空");
                return CommonResult.error(400, "订单类型不能为空");
            }
            
            OrderDropdownDataRespVO dropdownData = orderDropdownDataService.getDropdownData(reqVO);
            log.info("下拉数据获取成功");
            return success(dropdownData);
            
        } catch (Exception e) {
            log.error("获取下拉数据失败，异常详情：", e);
            // 返回友好的错误信息，而不是抛出异常
            return CommonResult.error(500, "获取下拉数据失败：" + e.getMessage());
        }
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出高校实践订单 Excel")
    public void exportOrderExcel(@Valid UniversityPracticeOrderExportReqVO exportReqVO,
                                HttpServletResponse response) throws IOException {
        // 设置分页大小为无限制，获取所有数据
        exportReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        
        // 获取订单列表
        List<UniversityPracticeOrderExcelVO> excelList = universityPracticeOrderService.getOrderExcelList(exportReqVO);
        
        // 导出 Excel
        ExcelUtils.write(response, "高校实践订单.xls", "数据", UniversityPracticeOrderExcelVO.class, excelList);
    }

    // ========== 审批相关接口 ==========

    @PostMapping("/initiate-approval")
    @Operation(summary = "发起审批")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:approve')")
    public CommonResult<Boolean> initiateApproval(@Valid @RequestBody OrderApprovalInitiateReqVO reqVO) {
        Boolean result = orderApprovalService.initiateApproval(reqVO);
        return success(result);
    }

    @GetMapping("/approval-records")
    @Operation(summary = "获取审批记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:query')")
    public CommonResult<PageResult<OrderApprovalRecordPageRespVO>> getApprovalRecords(@Valid OrderApprovalRecordPageReqVO reqVO) {
        PageResult<OrderApprovalRecordPageRespVO> result = orderApprovalService.getApprovalRecordPage(reqVO);
        return success(result);
    }

    @PostMapping("/approve")
    @Operation(summary = "审批通过")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:approve')")
    public CommonResult<Boolean> approveOrder(@RequestParam("orderId") Long orderId,
                                           @RequestParam("orderNo") String orderNo,
                                           @RequestParam("approvalType") String approvalType,
                                           @RequestParam("comments") String comments) {
        orderApprovalService.approveOrder(orderId, orderNo, approvalType, comments);
        return success(true);
    }

    @PostMapping("/reject")
    @Operation(summary = "审批驳回")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:approve')")
    public CommonResult<Boolean> rejectOrder(@RequestParam("orderId") Long orderId,
                                          @RequestParam("orderNo") String orderNo,
                                          @RequestParam("approvalType") String approvalType,
                                          @RequestParam("rejectReason") String rejectReason,
                                          @RequestParam("comments") String comments) {
        orderApprovalService.rejectOrder(orderId, orderNo, approvalType, rejectReason, comments);
        return success(true);
    }

    // ========== 收款相关接口 ==========

    @PostMapping("/confirm-collection")
    @Operation(summary = "确认收款")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:collection')")
    public CommonResult<OrderCollectionRespVO> confirmCollection(@Valid @RequestBody OrderCollectionReqVO reqVO) {
        // 转换为DTO
        OrderCollectionReqDTO reqDTO = BeanUtils.toBean(reqVO, OrderCollectionReqDTO.class);
        
        // 设置操作人姓名（TODO: 从安全上下文获取）
        reqDTO.setOperatorName("当前用户");
        
        // 调用服务确认收款
        OrderCollectionRespDTO respDTO = universityPracticeOrderService.confirmCollection(reqDTO);
        
        // 转换为VO并返回
        OrderCollectionRespVO respVO = BeanUtils.toBean(respDTO, OrderCollectionRespVO.class);
        return success(respVO);
    }

    @PostMapping("/update-collection")
    @Operation(summary = "更新收款信息")
    @PreAuthorize("@ss.hasPermission('publicbiz:university-practice-order:collection')")
    public CommonResult<OrderCollectionRespVO> updateCollection(@Valid @RequestBody OrderCollectionReqVO reqVO) {
        // 转换为DTO
        OrderCollectionReqDTO reqDTO = BeanUtils.toBean(reqVO, OrderCollectionReqDTO.class);
        
        // 设置操作人姓名（TODO: 从安全上下文获取）
        reqDTO.setOperatorName("当前用户");
        
        // 调用服务更新收款
        OrderCollectionRespDTO respDTO = universityPracticeOrderService.updateCollection(reqDTO);
        
        // 转换为VO并返回
        OrderCollectionRespVO respVO = BeanUtils.toBean(respDTO, OrderCollectionRespVO.class);
        return success(respVO);
    }

}
