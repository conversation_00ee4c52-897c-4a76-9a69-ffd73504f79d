package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订单审批记录分页响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 订单审批记录分页响应 VO")
@Data
public class OrderApprovalRecordPageRespVO {

    @Schema(description = "审批记录ID", example = "1")
    private Long id;

    @Schema(description = "审批ID", example = "AP20240601001")
    private String approvalId;

    @Schema(description = "审批编号", example = "AP001")
    private String approvalNo;

    @Schema(description = "订单ID", example = "1")
    private Long orderId;

    @Schema(description = "订单号", example = "HP202406001")
    private String orderNo;

    @Schema(description = "审批类型", example = "合同审批")
    private String approvalType;

    @Schema(description = "优先级", example = "normal")
    private String priority;

    @Schema(description = "审批状态", example = "pending")
    private String status;

    @Schema(description = "操作动作", example = "发起审批")
    private String action;

    @Schema(description = "审批意见", example = "同意")
    private String comments;

    @Schema(description = "审批人ID", example = "1")
    private Long approverId;

    @Schema(description = "审批人姓名", example = "张三")
    private String approverName;

    @Schema(description = "审批时间", example = "2024-06-01 10:00:00")
    private LocalDateTime approvalTime;

    @Schema(description = "创建时间", example = "2024-06-01 09:00:00")
    private LocalDateTime createTime;

}
