package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 阿姨请假/调休申请 Response VO")
@Data
public class AuntAttendanceApplyRespVO {

    @Schema(description = "申请单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "apply_20250701001")
    private String applyId;

    @Schema(description = "申请状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "PENDING", allowableValues = {"PENDING", "APPROVED", "REJECTED"})
    private String status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-07-01 10:00:00")
    private LocalDateTime createTime;
}
