### 雇主端小程序授权登录接口测试

### 1. 微信小程序一键授权登录
POST {{baseUrl}}/publicbiz/employer/auth/weixin-mini-app-login
Content-Type: application/json

{
  "code": "001frTkl21JUf94VGxol2hSlff1frTkR",
  "nickname": "张三",
  "headImageUrl": "https://example.com/avatar.jpg",
  "mobile": "13800138000"
}

### 2. 发送手机验证码
POST {{baseUrl}}/publicbiz/employer/auth/send-sms-code
Content-Type: application/json

{
  "mobile": "13800138000"
}

### 3. 校验手机验证码
POST {{baseUrl}}/publicbiz/employer/auth/validate-sms-code
Content-Type: application/json

{
  "mobile": "13800138000",
  "code": "123456"
}

### 4. 手机验证码登录
POST {{baseUrl}}/publicbiz/employer/auth/sms-login
Content-Type: application/json

{
  "mobile": "13800138000",
  "code": "123456",
  "nickname": "张三",
  "headImageUrl": "https://example.com/avatar.jpg"
}

### 环境变量配置
# @baseUrl = http://localhost:48080
# @baseUrl = https://api.example.com 