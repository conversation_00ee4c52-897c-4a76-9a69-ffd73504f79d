package cn.bztmaster.cnt.module.publicbiz.controller.admin.enterprisetraining;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.enterprisetraining.dto.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.enterprisetraining.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.enterprisetraining.EnterpriseTrainingOrderConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.enterprisetraining.EnterpriseTrainingOrderDO;
import cn.bztmaster.cnt.module.publicbiz.service.enterprisetraining.EnterpriseTrainingOrderService;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.enterprisetraining.EnterpriseTrainingOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.order.PublicbizPartnerContractMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizPartnerContractDO;
import cn.bztmaster.cnt.framework.tenant.core.context.TenantContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.math.BigDecimal;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.time.LocalDate;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 企业培训订单
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 企业培训订单")
@RestController
@RequestMapping("/publicbiz/enterprise-training-order")
@Validated
@Slf4j
public class EnterpriseTrainingOrderController {

    @Resource
    private EnterpriseTrainingOrderService enterpriseTrainingOrderService;

    @Resource
    private EnterpriseTrainingOrderMapper enterpriseTrainingOrderMapper;

    @Resource
    private PublicbizOrderMapper publicbizOrderMapper;

    @Resource
    private PublicbizPartnerContractMapper publicbizPartnerContractMapper;

    // ========== 基础CRUD接口 ==========

    @PostMapping("/create")
    @Operation(summary = "创建企业培训订单")
    // @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:create')")  // 暂时注释掉权限验证
    public CommonResult<Long> createOrder(@Valid @RequestBody EnterpriseTrainingOrderSaveReqVO createReqVO) {
        EnterpriseTrainingOrderSaveReqDTO createReqDTO = EnterpriseTrainingOrderConvert.INSTANCE.convert(createReqVO);
        return success(enterpriseTrainingOrderService.createOrder(createReqDTO));
    }

    @PostMapping("/create-test")
    @Operation(summary = "创建企业培训订单（测试接口，无权限验证）")
    public CommonResult<Long> createOrderTest(@Valid @RequestBody EnterpriseTrainingOrderSaveReqVO createReqVO) {
        EnterpriseTrainingOrderSaveReqDTO createReqDTO = EnterpriseTrainingOrderConvert.INSTANCE.convert(createReqVO);
        return success(enterpriseTrainingOrderService.createOrder(createReqDTO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新企业培训订单")
    // @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:update')")  // 暂时注释掉权限验证
    public CommonResult<Boolean> updateOrder(@Valid @RequestBody EnterpriseTrainingOrderSaveReqVO updateReqVO) {
        EnterpriseTrainingOrderSaveReqDTO updateReqDTO = EnterpriseTrainingOrderConvert.INSTANCE.convert(updateReqVO);
        enterpriseTrainingOrderService.updateOrder(updateReqDTO);
        return success(true);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除企业培训订单")
    @Parameter(name = "id", description = "订单编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:delete')")
    public CommonResult<Boolean> deleteOrder(@PathVariable("id") Long id) {
        enterpriseTrainingOrderService.deleteOrder(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得企业培训订单")
    @Parameter(name = "id", description = "订单编号", required = true, example = "1024")
    // @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:query')")  // 暂时注释掉权限验证
    public CommonResult<EnterpriseTrainingOrderRespVO> getOrder(@RequestParam("id") Long id) {
        log.info("getOrder endpoint called with id: {}", id);
        EnterpriseTrainingOrderDO order = enterpriseTrainingOrderService.getOrder(id);
        if (order != null) {
            log.info("DO object - orderType: {}, businessLine: {}, opportunityId: {}, leadId: {}, totalAmount: {}, paymentStatus: {}", 
                    order.getOrderType(), order.getBusinessLine(), order.getOpportunityId(), order.getLeadId(), order.getTotalAmount(), order.getPaymentStatus());
        }
        
        // 临时使用手动转换方法，绕过MapStruct
        EnterpriseTrainingOrderRespVO vo = enterpriseTrainingOrderService.convertToVOManually(order);
        
        if (vo != null) {
            log.info("VO object (manual) - orderType: {}, businessLine: {}, opportunityId: {}, leadId: {}, totalAmount: {}, paymentStatus: {}", 
                    vo.getOrderType(), vo.getBusinessLine(), vo.getOpportunityId(), vo.getLeadId(), vo.getTotalAmount(), vo.getPaymentStatus());
        }
        return success(vo);
    }

    @GetMapping("/get-by-order-no")
    @Operation(summary = "根据订单号获得企业培训订单")
    @Parameter(name = "orderNo", description = "订单号", required = true, example = "ET202406001")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:query')")
    public CommonResult<EnterpriseTrainingOrderRespVO> getOrderByOrderNo(@RequestParam("orderNo") String orderNo) {
        EnterpriseTrainingOrderDO order = enterpriseTrainingOrderService.getOrderByOrderNo(orderNo);
        return success(EnterpriseTrainingOrderConvert.INSTANCE.convertToVO(order));
    }

    @GetMapping("/list")
    @Operation(summary = "获得企业培训订单列表")
    @Parameter(name = "ids", description = "订单编号数组", required = true, example = "1024,2048")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:query')")
    public CommonResult<List<EnterpriseTrainingOrderRespVO>> getOrderList(@RequestParam("ids") List<Long> ids) {
        List<EnterpriseTrainingOrderDO> list = enterpriseTrainingOrderService.getOrderList(ids);
        return success(EnterpriseTrainingOrderConvert.INSTANCE.convertToVOList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得企业培训订单分页")
    // @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:query')")  // 暂时注释掉权限验证
    public CommonResult<PageResult<EnterpriseTrainingOrderRespVO>> getOrderPage(@Valid EnterpriseTrainingOrderPageReqVO pageReqVO) {
        EnterpriseTrainingOrderPageReqDTO pageReqDTO = EnterpriseTrainingOrderConvert.INSTANCE.convert(pageReqVO);
        PageResult<EnterpriseTrainingOrderDO> pageResult = enterpriseTrainingOrderService.getOrderPage(pageReqDTO);
        return success(EnterpriseTrainingOrderConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/page-test")
    @Operation(summary = "获得企业培训订单分页（测试接口，无权限验证）")
    public CommonResult<PageResult<EnterpriseTrainingOrderRespVO>> getOrderPageTest(@Valid EnterpriseTrainingOrderPageReqVO pageReqVO) {
        try {
            EnterpriseTrainingOrderPageReqDTO pageReqDTO = EnterpriseTrainingOrderConvert.INSTANCE.convert(pageReqVO);
            PageResult<EnterpriseTrainingOrderDO> pageResult = enterpriseTrainingOrderService.getOrderPage(pageReqDTO);
            return success(EnterpriseTrainingOrderConvert.INSTANCE.convertPage(pageResult));
        } catch (Exception e) {
            // 记录详细错误信息
            log.error("分页查询失败", e);
            return CommonResult.error(500, "分页查询失败：" + e.getMessage());
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "通过ID获得企业培训订单详情")
    @Parameter(name = "id", description = "订单编号", required = true, example = "1024")
    @Parameter(name = "tenantId", description = "租户ID", required = false, example = "1")
    // @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:query')")  // 暂时注释掉权限验证
    public CommonResult<EnterpriseTrainingOrderRespVO> getOrderById(@PathVariable("id") Long id, 
                                                                  @RequestParam(value = "tenantId", required = false) Long tenantId) {
        EnterpriseTrainingOrderDO order = enterpriseTrainingOrderService.getOrder(id);
        return success(EnterpriseTrainingOrderConvert.INSTANCE.convertToVO(order));
    }

    // ========== 调试方法 ==========

    @GetMapping("/debug/{id}")
    @Operation(summary = "调试：查看订单关联关系")
    @Parameter(name = "id", description = "订单编号", required = true, example = "1024")
    public CommonResult<Map<String, Object>> debugOrderRelation(@PathVariable("id") Long id) {
        Map<String, Object> result = enterpriseTrainingOrderService.debugOrderRelation(id);
        return success(result);
    }

    @GetMapping("/debug-main/{id}")
    @Operation(summary = "调试：查看主查询结果")
    @Parameter(name = "id", description = "订单编号", required = true, example = "1024")
    public CommonResult<Map<String, Object>> debugMainQuery(@PathVariable("id") Long id) {
        Map<String, Object> result = enterpriseTrainingOrderService.debugMainQuery(id);
        return success(result);
    }

    // ========== 合同管理 ==========

    @PostMapping("/enterprise-training-upload-paper-contract")
    @Operation(summary = "上传企业培训订单纸质合同")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:upload')")
    public CommonResult<Map<String, Object>> uploadPaperContract(
            @RequestParam("orderId") Long orderId,
            @RequestParam("orderNo") String orderNo,
            @RequestParam("contractNumber") String contractNumber,
            @RequestParam("contractName") String contractName,
            @RequestParam("signDate") String signDate,
            @RequestParam("contractAmount") BigDecimal contractAmount,
            @RequestParam("fileUrl") String fileUrl) {
        
        try {
            // 获取企业培训订单
            EnterpriseTrainingOrderDO order = enterpriseTrainingOrderService.getOrder(orderId);
            if (order == null) {
                throw new RuntimeException("企业培训订单不存在，订单ID：" + orderId);
            }
            
            // 校验订单号是否匹配
            if (!order.getOrderNo().equals(orderNo)) {
                throw new RuntimeException("订单号不匹配");
            }
            
            // 校验文件URL是否有效
            if (StrUtil.isBlank(fileUrl)) {
                throw new RuntimeException("文件URL不能为空");
            }
            
            // 更新订单的合同信息
            order.setContractType("paper");
            order.setContractFileUrl(fileUrl);
            order.setContractStatus("unsigned");
            
            // 保存更新 - 分别更新两个表
            // 1. 更新企业培训订单表（只更新基本字段，避免字段不匹配）
            EnterpriseTrainingOrderDO trainingOrder = new EnterpriseTrainingOrderDO();
            trainingOrder.setId(orderId);
            trainingOrder.setOrderNo(orderNo);
            // 不设置其他字段，避免更新不存在的字段
            enterpriseTrainingOrderMapper.updateById(trainingOrder);
            
            // 2. 更新公共订单表（包含合同信息）
            PublicbizOrderDO publicOrder = new PublicbizOrderDO();
            publicOrder.setId(order.getOrderId()); // 使用关联的订单ID
            publicOrder.setContractType("paper");
            publicOrder.setContractFileUrl(fileUrl);
            publicOrder.setContractStatus("unsigned");
            publicbizOrderMapper.updateById(publicOrder);

            // 创建合同记录
            PublicbizPartnerContractDO contractDO = PublicbizPartnerContractDO.builder()
                    .partnerId(orderId) // 使用订单ID作为合作伙伴ID
                    .contractName(contractName)
                    .contractNumber(contractNumber)
                    .startDate(LocalDate.parse(signDate)) // 使用签署日期作为开始日期
                    .endDate(LocalDate.parse(signDate).plusYears(1)) // 设置结束日期为签署日期后一年
                    .amount(contractAmount)
                    .status("有效") // 设置合同状态为有效
                    .attachmentPath(fileUrl) // 使用附件路径字段存储文件URL
                    .signer("系统用户") // 设置签约人
                    .tenantId(TenantContextHolder.getTenantId()) // 使用当前租户ID
                    .build();
            
            // 检查是否已存在合同记录
            LambdaQueryWrapper<PublicbizPartnerContractDO> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(PublicbizPartnerContractDO::getPartnerId, orderId);
            PublicbizPartnerContractDO existingContract = publicbizPartnerContractMapper.selectOne(wrapper);
            
            if (existingContract != null) {
                // 更新现有记录
                contractDO.setContractId(existingContract.getContractId());
                publicbizPartnerContractMapper.updateById(contractDO);
                log.info("更新现有合同记录，合同ID：{}", existingContract.getContractId());
            } else {
                // 创建新记录
                publicbizPartnerContractMapper.insert(contractDO);
                log.info("创建新的合同记录，合同ID：{}", contractDO.getContractId());
            }
            
            // 构建响应结果
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("fileUrl", fileUrl);
            result.put("fileName", StrUtil.subAfter(fileUrl, "/", true));
            result.put("contractNumber", contractNumber);
            result.put("contractName", contractName);
            result.put("signDate", signDate);
            result.put("contractAmount", contractAmount);
            result.put("message", "企业培训订单纸质合同上传成功");
            
            log.info("企业培训订单纸质合同上传成功，订单号：{}，合同编号：{}", orderNo, contractNumber);
            return success(result);
            
        } catch (Exception e) {
            log.error("上传企业培训订单纸质合同失败，订单ID：{}，错误：{}", orderId, e.getMessage(), e);
            throw new RuntimeException("上传纸质合同失败：" + e.getMessage());
        }
    }

    // ========== 收款相关接口 ==========

    @PostMapping("/confirm-payment")
    @Operation(summary = "确认收款")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:payment')")
    public CommonResult<EnterpriseTrainingOrderPaymentRespDTO> confirmPayment(@Valid @RequestBody EnterpriseTrainingOrderPaymentReqDTO reqDTO) {
        return success(enterpriseTrainingOrderService.confirmPayment(reqDTO));
    }

    @PutMapping("/update-payment")
    @Operation(summary = "更新收款信息")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:payment')")
    public CommonResult<EnterpriseTrainingOrderPaymentRespDTO> updatePayment(@Valid @RequestBody EnterpriseTrainingOrderPaymentUpdateReqDTO reqDTO) {
        return success(enterpriseTrainingOrderService.updatePayment(reqDTO));
    }

    // ========== 合同相关接口 ==========

    @PostMapping("/submit-contract")
    @Operation(summary = "提交合同")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:contract')")
    public CommonResult<EnterpriseTrainingOrderContractRespDTO> submitContract(@Valid @RequestBody EnterpriseTrainingOrderContractReqDTO reqDTO) {
        return success(enterpriseTrainingOrderService.submitContract(reqDTO));
    }

    @GetMapping("/contract-info")
    @Operation(summary = "获取合同信息")
    @Parameter(name = "orderId", description = "订单ID", required = true, example = "1024")
    @Parameter(name = "tenantId", description = "租户ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:contract')")
    public CommonResult<EnterpriseTrainingOrderContractRespDTO> getContractInfo(@RequestParam("orderId") Long orderId, @RequestParam("tenantId") Long tenantId) {
        return success(enterpriseTrainingOrderService.getContractInfo(orderId, tenantId));
    }

    // ========== 审批相关接口 ==========

    @PostMapping("/initiate-approval")
    @Operation(summary = "发起审批")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:approval')")
    public CommonResult<EnterpriseTrainingOrderApprovalRespDTO> initiateApproval(@Valid @RequestBody EnterpriseTrainingOrderApprovalReqDTO reqDTO) {
        return success(enterpriseTrainingOrderService.initiateApproval(reqDTO));
    }

    @PostMapping("/approve-order")
    @Operation(summary = "审批通过")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:approval')")
    public CommonResult<EnterpriseTrainingOrderApprovalRespDTO> approveOrder(@Valid @RequestBody EnterpriseTrainingOrderApprovalActionReqDTO reqDTO) {
        return success(enterpriseTrainingOrderService.approveOrder(reqDTO));
    }

    @PostMapping("/reject-order")
    @Operation(summary = "审批拒绝")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:approval')")
    public CommonResult<EnterpriseTrainingOrderApprovalRespDTO> rejectOrder(@Valid @RequestBody EnterpriseTrainingOrderApprovalActionReqDTO reqDTO) {
        return success(enterpriseTrainingOrderService.rejectOrder(reqDTO));
    }

    @GetMapping("/approval-records")
    @Operation(summary = "获取审批记录")
    @Parameter(name = "orderId", description = "订单ID", required = true, example = "1024")
    @Parameter(name = "tenantId", description = "租户ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:approval')")
    public CommonResult<EnterpriseTrainingOrderApprovalRecordRespDTO> getApprovalRecords(@RequestParam("orderId") Long orderId, @RequestParam("tenantId") Long tenantId) {
        return success(enterpriseTrainingOrderService.getApprovalRecords(orderId, tenantId));
    }

    // ========== 日志相关接口 ==========

    @GetMapping("/operation-logs")
    @Operation(summary = "获取操作日志")
    @Parameter(name = "orderNo", description = "订单号", required = true, example = "ET202406001")
    @Parameter(name = "tenantId", description = "租户ID", required = true, example = "1")
    @Parameter(name = "page", description = "页码", example = "1")
    @Parameter(name = "size", description = "每页大小", example = "20")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:log')")
    public CommonResult<EnterpriseTrainingOrderLogRespDTO> getOperationLogs(@RequestParam("orderNo") String orderNo, @RequestParam("tenantId") Long tenantId,
                                                                          @RequestParam(value = "page", defaultValue = "1") Integer page,
                                                                          @RequestParam(value = "size", defaultValue = "20") Integer size) {
        return success(enterpriseTrainingOrderService.getOperationLogs(orderNo, tenantId, page, size));
    }

    // ========== 导出和统计接口 ==========

    @PostMapping("/export")
    @Operation(summary = "导出企业培训订单")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:export')")
    public CommonResult<EnterpriseTrainingOrderExportRespDTO> exportOrders(@Valid @RequestBody EnterpriseTrainingOrderExportReqDTO reqDTO) {
        return success(enterpriseTrainingOrderService.exportOrders(reqDTO));
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取统计数据")
    @Parameter(name = "tenantId", description = "租户ID", required = true, example = "1")
    @Parameter(name = "startDate", description = "开始日期", example = "2024-06-01")
    @Parameter(name = "endDate", description = "结束日期", example = "2024-06-30")
    @PreAuthorize("@ss.hasPermission('publicbiz:enterprisetraining:statistics')")
    public CommonResult<EnterpriseTrainingOrderStatisticsRespDTO> getStatistics(@RequestParam("tenantId") Long tenantId,
                                                                              @RequestParam(value = "startDate", required = false) String startDate,
                                                                              @RequestParam(value = "endDate", required = false) String endDate) {
        return success(enterpriseTrainingOrderService.getStatistics(tenantId, startDate, endDate));
    }


}
