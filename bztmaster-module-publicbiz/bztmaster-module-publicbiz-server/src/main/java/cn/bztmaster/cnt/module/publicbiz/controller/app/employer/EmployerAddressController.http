### 获取用户地址列表
GET {{baseUrl}}/publicbiz/employer/address/list?page=1&size=10&status=1
Authorization: Bearer {{token}}

### 获取地址详情
GET {{baseUrl}}/publicbiz/employer/address/detail/1
Authorization: Bearer {{token}}

### 新增地址
POST {{baseUrl}}/publicbiz/employer/address/create
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "label": "家庭地址",
  "receiverName": "李四",
  "receiverPhone": "13900139001",
  "provinceCode": "510000",
  "provinceName": "四川省",
  "cityCode": "510100",
  "cityName": "成都市",
  "districtCode": "510104",
  "districtName": "锦江区",
  "region": "四川省成都市锦江区",
  "address": "春熙路1号太古里购物中心2楼201室",
  "longitude": 104.0800000,
  "latitude": 30.6500000,
  "isDefault": true,
  "sort": 1,
  "remark": "备注信息"
}

### 更新地址
POST {{baseUrl}}/publicbiz/employer/address/update/1
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "label": "家庭地址",
  "receiverName": "李四",
  "receiverPhone": "13900139001",
  "provinceCode": "510000",
  "provinceName": "四川省",
  "cityCode": "510100",
  "cityName": "成都市",
  "districtCode": "510104",
  "districtName": "锦江区",
  "region": "四川省成都市锦江区",
  "address": "春熙路1号太古里购物中心2楼201室",
  "longitude": 104.0800000,
  "latitude": 30.6500000,
  "isDefault": true,
  "sort": 1,
  "remark": "备注信息"
}

### 删除地址
POST {{baseUrl}}/publicbiz/employer/address/delete/1
Authorization: Bearer {{token}}

### 设置默认地址
POST {{baseUrl}}/publicbiz/employer/address/set-default/1
Authorization: Bearer {{token}}

### 获取用户默认地址
GET {{baseUrl}}/publicbiz/employer/address/default
Authorization: Bearer {{token}}

### 批量删除地址
POST {{baseUrl}}/publicbiz/employer/address/batch-delete
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "ids": [1, 2, 3]
}
