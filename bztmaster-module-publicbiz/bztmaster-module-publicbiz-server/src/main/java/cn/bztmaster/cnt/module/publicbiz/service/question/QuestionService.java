package cn.bztmaster.cnt.module.publicbiz.service.question;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.question.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.question.QuestionDO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 考题服务接口
 *
 * <AUTHOR>
 */
public interface QuestionService {

    /**
     * 考题分页查询
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    PageResult<QuestionRespVO> pageQuestion(QuestionPageReqVO reqVO);

    /**
     * 根据ID查询考题详情
     *
     * @param id 考题ID
     * @return 考题详情
     */
    QuestionRespVO getQuestion(Long id);

    /**
     * 新增考题
     *
     * @param reqVO 考题信息
     * @return 考题ID
     */
    Long createQuestion(QuestionSaveReqVO reqVO);

    /**
     * 编辑考题
     *
     * @param reqVO 考题信息
     */
    void updateQuestion(QuestionSaveReqVO reqVO);

    /**
     * 删除考题
     *
     * @param id 考题ID
     */
    void deleteQuestion(Long id);

    /**
     * 考题统计报表
     *
     * @param biz 业务模块
     * @param level1Name 一级分类名称
     * @param level2Name 二级分类名称
     * @param level3Name 三级分类名称
     * @return 统计结果
     */
    QuestionStatisticsRespVO getQuestionStatistics(String biz, String level1Name, String level2Name, String level3Name);

    /**
     * 校验Excel/CSV导入的考题数据
     *
     * @param excelList Excel/CSV数据列表
     * @return 校验结果
     */
    QuestionImportValidateRespVO validateImportExcelQuestions(List<QuestionImportExcelVO> excelList);

    /**
     * 批量导入考题（只导入校验通过的数据）
     *
     * @param questionList 考题数据列表
     * @return 导入结果
     */
    QuestionImportRespVO importValidQuestions(List<QuestionSaveReqVO> questionList);

    // ==================== API接口需要的方法 ====================

    /**
     * 根据ID查询考题DO
     *
     * @param id 考题ID
     * @return 考题DO
     */
    QuestionDO getQuestionDO(Long id);

    /**
     * 根据ID列表查询考题DO列表
     *
     * @param ids 考题ID列表
     * @return 考题DO列表
     */
    List<QuestionDO> getQuestionDOList(Collection<Long> ids);

    /**
     * 校验考题是否存在
     *
     * @param id 考题ID
     */
    void validateQuestionExists(Long id);

}
