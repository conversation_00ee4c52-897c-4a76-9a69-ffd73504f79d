package cn.bztmaster.cnt.module.publicbiz.controller.app.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerAddressService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;
import static cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * 雇主端地址 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 雇主端地址")
@RestController
@RequestMapping("/publicbiz/employer/address")
@Validated
@Slf4j
public class EmployerAddressController {

    @Resource
    private EmployerAddressService employerAddressService;

    @GetMapping("/list")
    @Operation(summary = "获取用户地址列表")
    public CommonResult<AddressListRespVO> getAddressList(
            @Parameter(description = "页码，默认1", example = "1") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页数量，默认10", example = "10") @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = "状态筛选：1-启用，0-禁用，不传则查询所有", example = "1") @RequestParam(value = "status", required = false) Integer status) {

        log.info("获取用户地址列表请求，page: {}, size: {}, status: {}", page, size, status);

        Long userId = getLoginUserId();

        AddressListRespVO result = employerAddressService.getAddressList(userId, page, size, status);
        return success(result);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取地址详情")

    public CommonResult<AddressDetailRespVO> getAddressDetail(
            @Parameter(description = "地址ID", example = "1") @PathVariable("id") Long id) {

        log.info("获取地址详情请求，id: {}", id);

        Long userId = getLoginUserId();

        AddressDetailRespVO result = employerAddressService.getAddressDetail(userId, id);
        return success(result);
    }

    @PostMapping("/create")
    @Operation(summary = "新增地址")

    public CommonResult<Long> createAddress(@Valid @RequestBody AddressCreateReqVO createReqVO) {

        log.info("新增地址请求，createReqVO: {}", createReqVO);

        Long userId = getLoginUserId();

        Long addressId = employerAddressService.createAddress(userId, createReqVO);
        return success(addressId);
    }

    @PostMapping("/update/{id}")
    @Operation(summary = "更新地址")

    public CommonResult<Void> updateAddress(
            @Parameter(description = "地址ID", example = "1") @PathVariable("id") Long id,
            @Valid @RequestBody AddressUpdateReqVO updateReqVO) {

        log.info("更新地址请求，id: {}, updateReqVO: {}", id, updateReqVO);

        Long userId = getLoginUserId();

        employerAddressService.updateAddress(userId, id, updateReqVO);
        return success(null);
    }

    @PostMapping("/delete/{id}")
    @Operation(summary = "删除地址")

    public CommonResult<Void> deleteAddress(
            @Parameter(description = "地址ID", example = "1") @PathVariable("id") Long id) {

        log.info("删除地址请求，id: {}", id);

        Long userId = getLoginUserId();

        employerAddressService.deleteAddress(userId, id);
        return success(null);
    }

    @PostMapping("/set-default/{id}")
    @Operation(summary = "设置默认地址")

    public CommonResult<Void> setDefaultAddress(
            @Parameter(description = "地址ID", example = "1") @PathVariable("id") Long id) {

        log.info("设置默认地址请求，id: {}", id);

        Long userId = getLoginUserId();

        employerAddressService.setDefaultAddress(userId, id);
        return success(null);
    }

    @GetMapping("/default")
    @Operation(summary = "获取用户默认地址")

    public CommonResult<AddressDetailRespVO> getDefaultAddress() {

        log.info("获取默认地址请求");

        Long userId = getLoginUserId();

        AddressDetailRespVO result = employerAddressService.getDefaultAddress(userId);
        return success(result);
    }

    @PostMapping("/batch-delete")
    @Operation(summary = "批量删除地址")

    public CommonResult<Void> batchDeleteAddress(@Valid @RequestBody AddressBatchDeleteReqVO batchDeleteReqVO) {

        log.info("批量删除地址请求，batchDeleteReqVO: {}", batchDeleteReqVO);

        Long userId = getLoginUserId();

        employerAddressService.batchDeleteAddress(userId, batchDeleteReqVO.getIds());
        return success(null);
    }

}
