package cn.bztmaster.cnt.module.publicbiz.controller.app.auth.vo;

import cn.bztmaster.cnt.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 雇主端发送手机验证码请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端发送手机验证码请求 VO")
@Data
public class EmployerAuthSmsSendReqVO {

    @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    @NotEmpty(message = "手机号不能为空")
    @Mobile
    private String mobile;

}