package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.DecimalMax;
import java.math.BigDecimal;

@Data
@Schema(description = "就业服务-阿姨评级更新 Request VO")
public class PractitionerRatingUpdateReqVO {
    @Schema(description = "阿姨ID")
    private Long id;

    @Schema(description = "阿姨oneID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "阿姨oneID不能为空")
    private Long practitionerOneId;

    @Schema(description = "新评级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "新评级不能为空")
    @DecimalMin(value = "1.0", message = "评级不能小于1.0")
    @DecimalMax(value = "5.0", message = "评级不能大于5.0")
    private BigDecimal newRating;

    @Schema(description = "评级原因")
    private String ratingReason;

    @Schema(description = "评级类型")
    private String ratingType;
} 