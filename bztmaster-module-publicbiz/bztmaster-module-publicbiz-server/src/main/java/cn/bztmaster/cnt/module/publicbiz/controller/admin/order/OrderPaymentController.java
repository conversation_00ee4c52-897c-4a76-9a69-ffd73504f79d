package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.order.dto.OrderCollectionRespDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderCollectionReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.OrderCollectionRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.order.PublicbizOrderPaymentDO;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderPaymentService;
import java.util.stream.Collectors;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 订单支付记录管理
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 订单支付记录")
@RestController
@RequestMapping("/publicbiz/order-payment")
@Validated
@Slf4j
public class OrderPaymentController {

    @Resource
    private OrderPaymentService orderPaymentService;

    @PostMapping("/confirm-collection")
    @Operation(summary = "确认收款")
    @PreAuthorize("@ss.hasPermission('publicbiz:order-payment:create')")
    public CommonResult<OrderCollectionRespVO> confirmCollection(@Valid @RequestBody OrderCollectionReqVO reqVO) {
        // 转换为DTO
        OrderCollectionReqDTO reqDTO = BeanUtils.toBean(reqVO, OrderCollectionReqDTO.class);

        // 设置操作人姓名（TODO: 从安全上下文获取）
        reqDTO.setOperatorName("当前用户");

        // 调用服务确认收款
        OrderCollectionRespDTO respDTO = orderPaymentService.confirmCollection(reqDTO);

        // 转换为VO并返回
        OrderCollectionRespVO respVO = BeanUtils.toBean(respDTO, OrderCollectionRespVO.class);
        return success(respVO);
    }

    @PostMapping("/update-collection")
    @Operation(summary = "更新收款信息")
    @PreAuthorize("@ss.hasPermission('publicbiz:order-payment:update')")
    public CommonResult<OrderCollectionRespVO> updateCollection(@Valid @RequestBody OrderCollectionReqVO reqVO) {
        // 转换为DTO
        OrderCollectionReqDTO reqDTO = BeanUtils.toBean(reqVO, OrderCollectionReqDTO.class);

        // 设置操作人姓名（TODO: 从安全上下文获取）
        reqDTO.setOperatorName("当前用户");

        // 调用服务更新收款
        OrderCollectionRespDTO respDTO = orderPaymentService.updateCollection(reqDTO);

        // 转换为VO并返回
        OrderCollectionRespVO respVO = BeanUtils.toBean(respDTO, OrderCollectionRespVO.class);
        return success(respVO);
    }

    @GetMapping("/list-by-order-id")
    @Operation(summary = "根据订单ID查询支付记录")
    @Parameter(name = "orderId", description = "订单ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('publicbiz:order-payment:query')")
    public CommonResult<List<OrderCollectionRespVO>> getPaymentRecordsByOrderId(@RequestParam("orderId") Long orderId) {
        // 调用服务查询支付记录
        List<PublicbizOrderPaymentDO> paymentDOList = orderPaymentService.getPaymentRecordsByOrderId(orderId);

        // 转换为DTO，再转换为VO
        List<OrderCollectionRespDTO> respDTOList = paymentDOList.stream()
                .map(this::convertToRespDTO)
                .collect(Collectors.toList());

        // 转换为VO并返回
        List<OrderCollectionRespVO> respVOList = respDTOList.stream()
                .map(dto -> BeanUtils.toBean(dto, OrderCollectionRespVO.class))
                .collect(Collectors.toList());
        return success(respVOList);
    }

    @GetMapping("/list-by-order-no")
    @Operation(summary = "根据订单号查询支付记录")
    @Parameter(name = "orderNo", description = "订单号", required = true, example = "HP202406001")
    @PreAuthorize("@ss.hasPermission('publicbiz:order-payment:query')")
    public CommonResult<List<OrderCollectionRespVO>> getPaymentRecordsByOrderNo(
            @RequestParam("orderNo") String orderNo) {
        // 调用服务查询支付记录
        List<PublicbizOrderPaymentDO> paymentDOList = orderPaymentService.getPaymentRecordsByOrderNo(orderNo);

        // 转换为DTO，再转换为VO
        List<OrderCollectionRespDTO> respDTOList = paymentDOList.stream()
                .map(this::convertToRespDTO)
                .collect(Collectors.toList());

        // 转换为VO并返回
        List<OrderCollectionRespVO> respVOList = respDTOList.stream()
                .map(dto -> BeanUtils.toBean(dto, OrderCollectionRespVO.class))
                .collect(Collectors.toList());
        return success(respVOList);
    }

    /**
     * 将PublicbizOrderPaymentDO转换为OrderCollectionRespDTO
     */
    private OrderCollectionRespDTO convertToRespDTO(PublicbizOrderPaymentDO paymentDO) {
        OrderCollectionRespDTO respDTO = new OrderCollectionRespDTO();
        respDTO.setPaymentId(paymentDO.getId());
        respDTO.setOrderId(paymentDO.getOrderId());
        respDTO.setOrderNo(paymentDO.getOrderNo());
        respDTO.setPaymentNo(paymentDO.getPaymentNo());
        respDTO.setPaymentType(paymentDO.getPaymentType());
        respDTO.setPaymentAmount(paymentDO.getPaymentAmount());
        respDTO.setPaymentStatus(paymentDO.getPaymentStatus());
        respDTO.setPaymentTime(paymentDO.getPaymentTime());
        respDTO.setOperatorId(paymentDO.getOperatorId());
        respDTO.setOperatorName(paymentDO.getOperatorName());
        respDTO.setPaymentRemark(paymentDO.getPaymentRemark());
        respDTO.setTransactionId(paymentDO.getTransactionId());
        respDTO.setCreateTime(paymentDO.getCreateTime());
        return respDTO;
    }
}
