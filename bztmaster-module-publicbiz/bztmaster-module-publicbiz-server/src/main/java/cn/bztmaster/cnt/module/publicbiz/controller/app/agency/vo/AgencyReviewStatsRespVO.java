package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "小程序 - 机构评价统计 Response VO")
@Data
public class AgencyReviewStatsRespVO {

    @Schema(description = "评价总数")
    private Integer totalReviews;

    @Schema(description = "平均评分")
    private BigDecimal averageRating;
}