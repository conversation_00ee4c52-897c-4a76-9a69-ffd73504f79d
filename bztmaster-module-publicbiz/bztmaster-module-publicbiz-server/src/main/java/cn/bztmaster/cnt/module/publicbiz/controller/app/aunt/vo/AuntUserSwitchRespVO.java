package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 阿姨用户切换响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 阿姨用户切换响应 VO")
@Data
public class AuntUserSwitchRespVO {

    @Schema(description = "是否已注册家政人员", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean isRegistered;

    @Schema(description = "阿姨OneID", example = "12345678-1234-1234-1234-123456789012")
    private String auntOneId;

    @Schema(description = "申请单ID", example = "APP1704067200000")
    private String applicationId;

    @Schema(description = "阿姨姓名", example = "张阿姨")
    private String name;

    @Schema(description = "手机号", example = "13800138000")
    private String phone;

    @Schema(description = "身份证号", example = "110101199001011234")
    private String idCard;

    @Schema(description = "申请时间", example = "2024-01-01T10:00:00")
    private LocalDateTime submitTime;

    @Schema(description = "主要服务类型", example = "月嫂")
    private String serviceType;

    @Schema(description = "从业年限", example = "5")
    private Integer experienceYears;

    @Schema(description = "平台状态", example = "cooperating")
    private String platformStatus;

    @Schema(description = "评级", example = "4.5")
    private BigDecimal rating;

    @Schema(description = "状态", example = "active")
    private String status;

    @Schema(description = "当前状态", example = "待岗")
    private String currentStatus;

}
