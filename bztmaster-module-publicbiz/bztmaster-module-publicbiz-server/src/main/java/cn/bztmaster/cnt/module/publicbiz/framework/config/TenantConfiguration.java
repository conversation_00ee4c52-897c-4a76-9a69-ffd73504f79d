package cn.bztmaster.cnt.module.publicbiz.framework.config;

import cn.bztmaster.cnt.framework.tenant.core.context.TenantContextHolder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * 租户配置类
 * 在开发环境中禁用租户验证
 *
 * <AUTHOR>
 */
@Configuration
@ConditionalOnProperty(name = "bztmaster.tenant.enable", havingValue = "false", matchIfMissing = false)
public class TenantConfiguration {

    @PostConstruct
    public void init() {
        // 设置默认租户ID为1，避免租户验证失败
        TenantContextHolder.setIgnore(true);
        TenantContextHolder.setTenantId(1L);
    }

}