package cn.bztmaster.cnt.module.publicbiz.controller.admin.leads;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.leads.dto.LeadFollowUpLogSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.leads.LeadFollowUpLogConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.leads.LeadFollowUpLogDO;
import cn.bztmaster.cnt.module.publicbiz.service.leads.LeadFollowUpLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 线索跟进记录
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 线索跟进记录")
@RestController
@RequestMapping("/publicbiz/leads/follow-up")
@Validated
public class LeadFollowUpController {

    @Resource
    private LeadFollowUpLogService leadFollowUpLogService;

    @PostMapping("/create")
    @Operation(summary = "创建线索跟进记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:follow-up:create')")
    public CommonResult<Long> createLeadFollowUpLog(@Valid @RequestBody LeadFollowUpLogSaveReqVO createReqVO) {
        LeadFollowUpLogSaveReqDTO createReqDTO = LeadFollowUpLogConvert.INSTANCE.convert(createReqVO);
        Long id = leadFollowUpLogService.createLeadFollowUpLog(createReqDTO);
        return success(id);
    }

    @PutMapping("/update")
    @Operation(summary = "更新线索跟进记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:follow-up:update')")
    public CommonResult<Boolean> updateLeadFollowUpLog(@Valid @RequestBody LeadFollowUpLogSaveReqVO updateReqVO) {
        LeadFollowUpLogSaveReqDTO updateReqDTO = LeadFollowUpLogConvert.INSTANCE.convert(updateReqVO);
        leadFollowUpLogService.updateLeadFollowUpLog(updateReqDTO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除线索跟进记录")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:follow-up:delete')")
    public CommonResult<Boolean> deleteLeadFollowUpLog(@RequestParam("id") Long id) {
        leadFollowUpLogService.deleteLeadFollowUpLog(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得线索跟进记录详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:follow-up:query')")
    public CommonResult<LeadFollowUpLogRespVO> getLeadFollowUpLog(@RequestParam("id") Long id) {
        LeadFollowUpLogDO leadFollowUpLog = leadFollowUpLogService.getLeadFollowUpLog(id);
        return success(LeadFollowUpLogConvert.INSTANCE.convert(leadFollowUpLog));
    }

    @GetMapping("/list-by-lead-id")
    @Operation(summary = "获得线索的跟进记录列表")
    @Parameter(name = "leadId", description = "线索ID", required = true, example = "LEAD20250721001")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:follow-up:query')")
    public CommonResult<List<LeadFollowUpLogRespVO>> getLeadFollowUpLogListByLeadId(@RequestParam("leadId") String leadId) {
        List<LeadFollowUpLogDO> list = leadFollowUpLogService.getLeadFollowUpLogListByLeadId(leadId);
        return success(LeadFollowUpLogConvert.INSTANCE.convertList(list));
    }

    @GetMapping("/page")
    @Operation(summary = "获得线索跟进记录分页")
    @PreAuthorize("@ss.hasPermission('publicbiz:leads:follow-up:query')")
    public CommonResult<PageResult<LeadFollowUpLogRespVO>> getLeadFollowUpLogPage(@Valid LeadFollowUpLogPageReqVO pageVO) {
        PageResult<LeadFollowUpLogDO> pageResult = leadFollowUpLogService.getLeadFollowUpLogPage(
                LeadFollowUpLogConvert.INSTANCE.convert(pageVO));
        return success(LeadFollowUpLogConvert.INSTANCE.convertPage(pageResult));
    }
}