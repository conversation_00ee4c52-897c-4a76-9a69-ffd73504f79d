package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.DecimalMax;
import java.math.BigDecimal;

@Schema(description = "用户 APP - 阿姨打卡记录 Request VO")
@Data
public class AuntPunchRecordReqVO {

    @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "550e8400-e29b-41d4-a716-446655440000")
    @NotNull(message = "阿姨OneID不能为空")
    private String auntOneId;

    @Schema(description = "排班ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "排班ID不能为空")
    private String scheduleId;

    @Schema(description = "打卡类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "打卡类型不能为空")
    private Integer punchType; // 1-开始打卡,2-完成打卡

    @Schema(description = "打卡位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京市朝阳区建国路88号")
    @NotNull(message = "打卡位置不能为空")
    private String punchLocation;

    @Schema(description = "打卡纬度", requiredMode = Schema.RequiredMode.REQUIRED, example = "39.908823")
    @NotNull(message = "打卡纬度不能为空")
    @DecimalMin(value = "-90.0", message = "纬度必须在-90到90之间")
    @DecimalMax(value = "90.0", message = "纬度必须在-90到90之间")
    private BigDecimal punchLatitude;

    @Schema(description = "打卡经度", requiredMode = Schema.RequiredMode.REQUIRED, example = "116.397470")
    @NotNull(message = "打卡经度不能为空")
    @DecimalMin(value = "-180.0", message = "经度必须在-180到180之间")
    @DecimalMax(value = "180.0", message = "经度必须在-180到180之间")
    private BigDecimal punchLongitude;

    @Schema(description = "照片数量", example = "2")
    private Integer photoCount;

    @Schema(description = "照片URL列表", example = "https://example.com/photo1.jpg,https://example.com/photo2.jpg")
    private String photoUrls;

    @Schema(description = "打卡备注", example = "按时到达服务地点")
    private String remark;

} 