package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 机构记录附件保存 Request VO")
@Data
public class AgencyRecordAttachmentSaveReqVO {

    @Schema(description = "附件ID（更新时可选）", example = "1")
    private Long id;

    @Schema(description = "文件名", requiredMode = Schema.RequiredMode.REQUIRED, example = "客户表扬录音.mp3")
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    @Schema(description = "文件类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "mp3")
    @NotBlank(message = "文件类型不能为空")
    private String fileType;

    @Schema(description = "文件大小（字节）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1536000")
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;

    @Schema(description = "文件访问URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/files/客户表扬录音.mp3")
    @NotBlank(message = "文件访问URL不能为空")
    private String fileUrl;
} 