package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "用户 APP - 阿姨订单详情 Response VO")
@Data
public class AuntOrderDetailRespVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long orderId;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDER20241201001")
    private String orderNo;

    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "pending")
    private String orderStatus;

    @Schema(description = "支付状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "paid")
    private String paymentStatus;

    @Schema(description = "结算状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "pending")
    private String settlementStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-12-01 10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "客户信息")
    private CustomerInfo customerInfo;

    @Schema(description = "服务信息")
    private ServiceInfo serviceInfo;

    @Schema(description = "总金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "200.00")
    private BigDecimal totalAmount;

    @Schema(description = "阿姨收入", requiredMode = Schema.RequiredMode.REQUIRED, example = "160.00")
    private BigDecimal practitionerIncome;

    @Schema(description = "平台分成", requiredMode = Schema.RequiredMode.REQUIRED, example = "40.00")
    private BigDecimal platformIncome;

    @Schema(description = "所属机构", example = "传能家政")
    private String agencyName;

    @Schema(description = "客户信息")
    @Data
    public static class CustomerInfo {

        @Schema(description = "客户姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张先生")
        private String customerName;

        @Schema(description = "客户电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
        private String customerPhone;

        @Schema(description = "服务地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京市朝阳区建国路88号")
        private String serviceAddress;

        @Schema(description = "客户备注", example = "请准时到达")
        private String customerRemark;
    }

    @Schema(description = "服务信息")
    @Data
    public static class ServiceInfo {

        @Schema(description = "服务套餐名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "日常保洁套餐")
        private String servicePackageName;

        @Schema(description = "套餐价格", requiredMode = Schema.RequiredMode.REQUIRED, example = "200.00")
        private BigDecimal servicePackagePrice;

        @Schema(description = "服务开始日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-12-01")
        private LocalDate serviceStartDate;

        @Schema(description = "服务结束日期", example = "2024-12-31")
        private LocalDate serviceEndDate;

        @Schema(description = "服务时长", requiredMode = Schema.RequiredMode.REQUIRED, example = "4小时")
        private String serviceDuration;

        @Schema(description = "服务描述", example = "日常保洁服务，包括房间清洁、物品整理等")
        private String serviceDescription;

        @Schema(description = "详细服务内容", example = "1. 房间清洁 2. 物品整理 3. 垃圾清理")
        private String serviceDetails;

        @Schema(description = "服务流程", example = "1. 到达现场 2. 开始服务 3. 完成服务 4. 客户确认")
        private String serviceProcess;
    }

} 