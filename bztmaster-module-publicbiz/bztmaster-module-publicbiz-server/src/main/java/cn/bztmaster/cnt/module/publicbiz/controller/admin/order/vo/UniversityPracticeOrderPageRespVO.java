package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 高校实践订单分页 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单分页 Response VO")
@Data
public class UniversityPracticeOrderPageRespVO {

    @Schema(description = "订单ID", example = "1")
    private Long id;

    @Schema(description = "订单号", example = "HP202406001")
    private String orderNo;

    @Schema(description = "关联商机ID", example = "OPP001")
    private String opportunityId;

    @Schema(description = "项目名称", example = "2024年暑期社会实践项目")
    private String projectName;

    @Schema(description = "合作高校名称", example = "XX大学经济管理学院")
    private String universityName;

    @Schema(description = "合作企业名称", example = "ABC科技有限公司")
    private String enterpriseName;

    @Schema(description = "开始日期", example = "2024-07-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-08-30")
    private LocalDate endDate;

    @Schema(description = "负责人姓名", example = "张三")
    private String managerName;

    @Schema(description = "订单总金额", example = "580000.00")
    private BigDecimal totalAmount;

    @Schema(description = "订单状态", example = "pending_approval")
    private String orderStatus;

    @Schema(description = "支付状态", example = "pending")
    private String paymentStatus;

    @Schema(description = "参与学生人数", example = "50")
    private Integer studentCount;

    @Schema(description = "实践时长", example = "2个月")
    private String practiceDuration;

    @Schema(description = "实践地点", example = "北京市朝阳区")
    private String practiceLocation;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}





