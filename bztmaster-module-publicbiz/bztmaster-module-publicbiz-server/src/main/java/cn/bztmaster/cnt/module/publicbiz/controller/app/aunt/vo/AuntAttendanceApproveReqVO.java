package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Schema(description = "用户 APP - 阿姨请假/调休申请审批 Request VO")
@Data
public class AuntAttendanceApproveReqVO {

    @Schema(description = "申请单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "apply_20250701001")
    @NotNull(message = "申请单号不能为空")
    private String applyId;

    @Schema(description = "操作", requiredMode = Schema.RequiredMode.REQUIRED, example = "APPROVE", allowableValues = {"APPROVE", "REJECT"})
    @NotNull(message = "操作不能为空")
    @Pattern(regexp = "^(APPROVE|REJECT)$", message = "操作只能是APPROVE或REJECT")
    private String action;

    @Schema(description = "审批备注", example = "同意申请")
    private String remark;

    @Schema(description = "审批人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "admin_001")
    @NotBlank(message = "审批人ID不能为空")
    private String approverId;
}
