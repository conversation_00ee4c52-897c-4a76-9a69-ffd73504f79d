package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构详情响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端 - 机构详情响应 VO")
@Data
public class AgencyDetailRespVO {

    @Schema(description = "机构ID", example = "1001")
    private Long id;

    @Schema(description = "机构编号", example = "AG20241201001")
    private String agencyNo;

    @Schema(description = "机构全称", example = "金牌家政服务有限公司")
    private String name;

    @Schema(description = "机构简称", example = "金牌家政")
    private String shortName;

    @Schema(description = "机构类型", example = "cooperation")
    private String agencyType;

    @Schema(description = "法人代表", example = "张三")
    private String legalRepresentative;

    @Schema(description = "统一社会信用代码", example = "91410100MA12345678")
    private String unifiedSocialCreditCode;

    @Schema(description = "成立日期", example = "2016-01-01")
    private LocalDate establishmentDate;

    @Schema(description = "注册地址", example = "河南省郑州市二七区大学路128号")
    private String registeredAddress;

    @Schema(description = "经营地址", example = "河南省郑州市二七区大学路128号")
    private String operatingAddress;

    @Schema(description = "经营范围", example = "家政服务、保洁服务、月嫂服务等")
    private String businessScope;

    @Schema(description = "联系人", example = "李四")
    private String contactPerson;

    @Schema(description = "联系电话", example = "0371-8888-8888")
    private String contactPhone;

    @Schema(description = "联系邮箱", example = "<EMAIL>")
    private String contactEmail;

    @Schema(description = "机构地址", example = "郑州市二七区大学路128号")
    private String agencyAddress;

    @Schema(description = "省份", example = "河南省")
    private String province;

    @Schema(description = "城市", example = "郑州市")
    private String city;

    @Schema(description = "区县", example = "二七区")
    private String district;

    @Schema(description = "街道", example = "大学路街道")
    private String street;

    @Schema(description = "详细地址", example = "128号")
    private String detailAddress;

    @Schema(description = "经度", example = "113.6401")
    private BigDecimal longitude;

    @Schema(description = "纬度", example = "34.7236")
    private BigDecimal latitude;

    @Schema(description = "位置精度", example = "high")
    private String locationAccuracy;

    @Schema(description = "合作状态", example = "cooperating")
    private String cooperationStatus;

    @Schema(description = "合同编号", example = "HT20241201001")
    private String contractNo;

    @Schema(description = "合同开始日期", example = "2024-01-01")
    private LocalDate contractStartDate;

    @Schema(description = "合同结束日期", example = "2024-12-31")
    private LocalDate contractEndDate;

    @Schema(description = "佣金比例", example = "15.00")
    private BigDecimal commissionRate;

    @Schema(description = "审核状态", example = "approved")
    private String reviewStatus;

    @Schema(description = "状态", example = "active")
    private String status;

    @Schema(description = "备注", example = "优质家政服务机构")
    private String remark;

    @Schema(description = "创建时间", example = "2024-01-01 10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2024-01-01 10:00:00")
    private LocalDateTime updateTime;

    // 前端展示所需字段
    @Schema(description = "机构Logo", example = "https://example.com/logo.png")
    private String logo;

    @Schema(description = "机构横幅", example = "https://example.com/banner.png")
    private String banner;

    @Schema(description = "评分", example = "4.5")
    private BigDecimal rating;

    @Schema(description = "评价数量", example = "2.3万")
    private String reviewCount;

    @Schema(description = "服务家庭数", example = "12.5万+")
    private String servedFamilies;

    @Schema(description = "成立年限", example = "8年")
    private String establishedYears;

    @Schema(description = "地址", example = "郑州市二七区大学路128号")
    private String address;

    @Schema(description = "电话", example = "0371-8888-8888")
    private String phone;

    @Schema(description = "门头照片")
    private List<String> storefrontImages;

    @Schema(description = "资质证照")
    private List<Certificate> certificates;

    /**
     * 资质证照
     */
    @Schema(description = "资质证照")
    @Data
    public static class Certificate {

        @Schema(description = "证照名称", example = "营业执照")
        private String name;

        @Schema(description = "证照图片", example = "https://example.com/cert1.png")
        private String image;

    }

}