package cn.bztmaster.cnt.module.publicbiz.service.partner.impl;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.*;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.partner.PartnerMapper;
import cn.bztmaster.cnt.module.publicbiz.convert.partner.PartnerConvert;
import cn.bztmaster.cnt.module.publicbiz.service.partner.PartnerService;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerPageReqVO;
import org.springframework.transaction.annotation.Transactional;
import java.util.Map;
import java.util.stream.Collectors;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerActiveListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerActiveSimpleVO;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyService;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyCreateReqVO;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants.*;

/**
 * 合作伙伴 Service 实现类
 * 
 * 包含操作日志记录功能：
 * 1. 新增合作伙伴时记录操作日志
 * 2. 更新合作伙伴时记录字段级别的变更日志
 * 3. 删除合作伙伴时记录操作日志
 * 
 * <AUTHOR>
 */
@Service
public class PartnerServiceImpl implements PartnerService {

    @Resource
    private PartnerMapper partnerMapper;
    @Resource
    private PartnerConvert partnerConvert;
    @Resource
    private AgencyService agencyService;
    @Resource
    private AgencyMapper agencyMapper;

    @Override
    public PageResult<PartnerRespVO> getPartnerPage(PartnerPageReqVO reqVO) {
        PageResult<PartnerDO> pageResult = partnerMapper.selectPage(reqVO);
        List<PartnerRespVO> voList = partnerConvert.convertList(pageResult.getList());

        return new PageResult<>(voList, pageResult.getTotal());
    }

    /**
     * 同步就业服务机构数据
     * 仅当机构类型为"家政机构"时执行同步逻辑
     *
     * @param partnerDO 合作伙伴数据对象
     */
    private void syncAgencyData(PartnerDO partnerDO) {
        // 仅当机构类型为"家政机构"时才执行同步逻辑
        if (!"家政机构".equals(partnerDO.getType())) {
            return;
        }

        // 统一社会信用代码必须不为空
        if (partnerDO.getCreditCode() == null || partnerDO.getCreditCode().trim().isEmpty()) {
            throw new IllegalArgumentException("家政机构的统一社会信用代码不能为空");
        }

        // 根据统一社会信用代码查询是否已存在机构记录
        AgencyDO existingAgency = agencyMapper.selectByUnifiedSocialCreditCode(partnerDO.getCreditCode());

        if (existingAgency == null) {
            // 不存在相同统一社会信用代码的机构记录，新增机构数据
            AgencyCreateReqVO agencyCreateReqVO = new AgencyCreateReqVO();
            agencyCreateReqVO.setAgencyName(partnerDO.getName());
            agencyCreateReqVO.setAgencyShortName(partnerDO.getShortName());
            agencyCreateReqVO.setAgencyNo(generateAgencyNo()); // 生成机构编号
            agencyCreateReqVO.setAgencyType("cooperation"); // 设置为合作类型
            agencyCreateReqVO.setLegalRepresentative(partnerDO.getLegalPerson());
            agencyCreateReqVO.setUnifiedSocialCreditCode(partnerDO.getCreditCode());
            agencyCreateReqVO.setEstablishmentDate(partnerDO.getFoundationDate() != null ?
                partnerDO.getFoundationDate().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate() : null);
            agencyCreateReqVO.setRegisteredAddress(partnerDO.getRegisterAddress());
            agencyCreateReqVO.setOperatingAddress(partnerDO.getBusinessAddress());
            agencyCreateReqVO.setBusinessScope(partnerDO.getMainBusiness());
            agencyCreateReqVO.setContactPerson(partnerDO.getContactName());
            agencyCreateReqVO.setContactPhone(partnerDO.getContactPhone());
            agencyCreateReqVO.setApplicationTime(java.time.LocalDateTime.now());

            // 创建机构记录
            Long agencyId = agencyService.createAgency(agencyCreateReqVO);

            // 更新通过合作伙伴同步创建的机构状态
            // 通过合作伙伴同步创建的机构直接设置为"合作中"和"已通过审核"状态
            // 区别于直接通过 AgencyController 创建的机构（默认为"待审核"状态）
            AgencyDO createdAgency = agencyMapper.selectById(agencyId);
            if (createdAgency != null) {
                createdAgency.setCooperationStatus("cooperating"); // 合作中（区别于默认的"pending"）
                createdAgency.setReviewStatus("approved"); // 已通过审核（区别于默认的"pending"）
                agencyMapper.updateById(createdAgency);
            }

            // 将新创建的机构ID和名称回写到合作伙伴记录
            partnerDO.setAgencyId(agencyId);
            partnerDO.setAgencyName(partnerDO.getName());
        } else {
            // 已存在相同统一社会信用代码的机构记录
            // 检查当前合作伙伴的 agency_id 和 agency_name 是否为空
            if (partnerDO.getAgencyId() == null || partnerDO.getAgencyName() == null ||
                partnerDO.getAgencyName().trim().isEmpty()) {
                // 将已存在机构的ID和名称冗余存储到合作伙伴记录中
                partnerDO.setAgencyId(existingAgency.getId());
                partnerDO.setAgencyName(existingAgency.getAgencyName());
            }
        }
    }

    /**
     * 生成机构编号
     * 简单实现：使用时间戳 + 随机数
     */
    private String generateAgencyNo() {
        return "AG" + System.currentTimeMillis() + String.format("%03d", (int)(Math.random() * 1000));
    }

    /**
     * 新增合作伙伴
     * 
     * 操作日志记录：
     * - 记录新增操作，包含合作伙伴名称
     * - 使用 @LogRecord 注解自动记录操作日志
     * - 通过 LogRecordContext.putVariable 设置日志上下文变量
     * 
     * @param reqVO 新增请求参数
     * @return 合作伙伴ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = PARTNER_TYPE, subType = PARTNER_CREATE_SUB_TYPE, bizNo = "{{#partner.id}}", success = PARTNER_CREATE_SUCCESS)
    public Long createPartner(PartnerSaveReqVO reqVO) {
        // 参数校验
        if (reqVO == null) {
            throw new IllegalArgumentException("新增参数不能为空");
        }

        // 验证统一社会信用代码唯一性
        if (reqVO.getCreditCode() != null && !reqVO.getCreditCode().trim().isEmpty()) {
            PartnerDO existingPartner = partnerMapper.selectByCreditCode(reqVO.getCreditCode());
            if (existingPartner != null) {
                throw new IllegalArgumentException("统一社会信用代码已存在，不能重复");
            }
        }

        PartnerDO partnerDO = partnerConvert.convert(reqVO);
        partnerDO.setDeleted(false);
        partnerDO.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
        partnerDO.setCreateTime(new Date());
        partnerDO.setTenantId(
                SecurityFrameworkUtils.getLoginUser() != null ? SecurityFrameworkUtils.getLoginUser().getTenantId()
                        : 1L);

        // 同步就业服务机构数据（仅当机构类型为"家政机构"时）
        syncAgencyData(partnerDO);

        partnerMapper.insert(partnerDO);

        // 记录操作日志上下文 - 用于日志模板中的变量替换
        LogRecordContext.putVariable("partner", partnerDO);

        return partnerDO.getId();
    }

    /**
     * 更新合作伙伴
     * 
     * 操作日志记录：
     * - 记录更新操作，包含合作伙伴名称
     * - 使用 _DIFF 函数记录字段级别的变更
     * - 通过 LogRecordContext.putVariable 设置新旧对象用于对比
     * 
     * @param reqVO 更新请求参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = PARTNER_TYPE, subType = PARTNER_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}", success = PARTNER_UPDATE_SUCCESS)
    public void updatePartner(PartnerUpdateReqVO reqVO) {
        // 参数校验
        if (reqVO == null || reqVO.getId() == null) {
            throw new IllegalArgumentException("更新参数不能为空");
        }

        // 获取更新前的合作伙伴信息 - 用于操作日志的字段对比
        PartnerDO oldPartner = partnerMapper.selectById(reqVO.getId());
        if (oldPartner == null) {
            throw new IllegalArgumentException("合作伙伴不存在");
        }

        // 验证统一社会信用代码唯一性（排除当前记录）
        if (reqVO.getCreditCode() != null && !reqVO.getCreditCode().trim().isEmpty()) {
            PartnerDO existingPartner = partnerMapper.selectByCreditCode(reqVO.getCreditCode());
            if (existingPartner != null && !existingPartner.getId().equals(reqVO.getId())) {
                throw new IllegalArgumentException("统一社会信用代码已存在，不能重复");
            }
        }

        PartnerDO partnerDO = partnerConvert.convert(reqVO);
        partnerDO.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
        partnerDO.setUpdateTime(new Date());

        // 同步就业服务机构数据（仅当机构类型为"家政机构"时）
        syncAgencyData(partnerDO);

        partnerMapper.updateById(partnerDO);

        // 记录操作日志上下文 - 用于 _DIFF 函数的字段对比
        // 使用相同的转换器确保新旧对象的格式一致，避免误报日期变更
        PartnerUpdateReqVO oldPartnerVO = partnerConvert.convertToUpdateReqVO(oldPartner);
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, oldPartnerVO);
        LogRecordContext.putVariable("partner", oldPartner);
        LogRecordContext.putVariable("updateReqVO", reqVO);
    }

    /**
     * 删除合作伙伴（逻辑删除）
     * 
     * 操作日志记录：
     * - 记录删除操作，包含合作伙伴名称
     * - 使用 @LogRecord 注解自动记录操作日志
     * - 通过 LogRecordContext.putVariable 设置日志上下文变量
     * 
     * @param id 合作伙伴ID
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = PARTNER_TYPE, subType = PARTNER_DELETE_SUB_TYPE, bizNo = "{{#partner.id}}", success = PARTNER_DELETE_SUCCESS)
    public void deletePartner(Long id) {
        // 参数校验
        if (id == null) {
            throw new IllegalArgumentException("删除参数不能为空");
        }

        PartnerDO partnerDO = partnerMapper.selectById(id);
        if (partnerDO == null) {
            throw new IllegalArgumentException("合作伙伴不存在");
        }

        if (!Boolean.TRUE.equals(partnerDO.getDeleted())) {
            partnerDO.setDeleted(true);
            partnerDO.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
            partnerDO.setUpdateTime(new Date());
            partnerMapper.updateById(partnerDO);

            // 记录操作日志上下文 - 用于日志模板中的变量替换
            LogRecordContext.putVariable("partner", partnerDO);
        }
    }

    @Override
    public PartnerRespVO getPartnerDetail(Long id) {
        PartnerDO partnerDO = partnerMapper.selectById(id);
        if (partnerDO == null || Boolean.TRUE.equals(partnerDO.getDeleted())) {
            return null;
        }
        return partnerConvert.convert(partnerDO);
    }

    @Override
    public PartnerStatRespVO getPartnerStat() {
        PartnerStatRespVO stat = new PartnerStatRespVO();
        // 合作伙伴总数
        PartnerPageReqVO allReq = new PartnerPageReqVO();
        allReq.setPageNo(1);
        allReq.setPageSize(1);
        PageResult<PartnerRespVO> allPage = getPartnerPage(allReq);
        stat.setPartnerTotal(allPage.getTotal().intValue());
        // 合作中的总数
        PartnerPageReqVO coopReq = new PartnerPageReqVO();
        coopReq.setStatus("合作中");
        coopReq.setPageNo(1);
        coopReq.setPageSize(1);
        PageResult<PartnerRespVO> coopPage = getPartnerPage(coopReq);
        stat.setPartnerActive(coopPage.getTotal().intValue());
        // 待审核的总数
        PartnerPageReqVO pendingReq = new PartnerPageReqVO();
        pendingReq.setStatus("待审核");
        pendingReq.setPageNo(1);
        pendingReq.setPageSize(1);
        PageResult<PartnerRespVO> pendingPage = getPartnerPage(pendingReq);
        stat.setPartnerPending(pendingPage.getTotal().intValue());
        // 风险伙伴数，暂时返回0
        stat.setPartnerRisk(0);
        return stat;
    }

    @Override
    public PartnerActiveListRespVO getActivePartnerList(String status, String type, String biz, String keyword) {
        LambdaQueryWrapperX<PartnerDO> queryWrapper = new LambdaQueryWrapperX<PartnerDO>()
                .eqIfPresent(PartnerDO::getStatus, status)
                .eqIfPresent(PartnerDO::getType, type)
                .eqIfPresent(PartnerDO::getBiz, biz)
                .eq(PartnerDO::getDeleted, false);
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                    .like(PartnerDO::getName, keyword)
                    .or()
                    .like(PartnerDO::getShortName, keyword));
        }
        queryWrapper.orderByAsc(PartnerDO::getName);
        List<PartnerDO> partnerList = partnerMapper.selectList(queryWrapper);
        List<PartnerActiveSimpleVO> voList = partnerList.stream().map(partner -> {
            PartnerActiveSimpleVO vo = new PartnerActiveSimpleVO();
            vo.setId(partner.getId());
            vo.setName(partner.getName());
            vo.setShortName(partner.getShortName());
            vo.setType(partner.getType());
            vo.setBiz(partner.getBiz());
            vo.setStatus(partner.getStatus());
            vo.setContactName(partner.getContactName());
            vo.setContactPhone(partner.getContactPhone());
            vo.setAgencyId(partner.getAgencyId());
            vo.setAgencyName(partner.getAgencyName());
            return vo;
        }).collect(Collectors.toList());
        PartnerActiveListRespVO respVO = new PartnerActiveListRespVO();
        respVO.setList(voList);
        return respVO;
    }

    @Override
    public PartnerRespVO getPartnerByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }
        PartnerDO partnerDO = partnerMapper.selectByName(name.trim());
        if (partnerDO == null || Boolean.TRUE.equals(partnerDO.getDeleted())) {
            return null;
        }
        return partnerConvert.convert(partnerDO);
    }
}
