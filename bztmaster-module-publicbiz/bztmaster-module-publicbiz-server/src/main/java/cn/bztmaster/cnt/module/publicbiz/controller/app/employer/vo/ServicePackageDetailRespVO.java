package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 服务套餐详情响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端 - 服务套餐详情响应 VO")
@Data
public class ServicePackageDetailRespVO {

    @Schema(description = "套餐ID", example = "1")
    private Long id;

    @Schema(description = "套餐名称", example = "月度保洁服务 | 30天")
    private String name;

    @Schema(description = "服务分类", example = "日常保洁")
    private String category;

    @Schema(description = "套餐主图URL", example = "https://example.com/package1.png")
    private String thumbnail;

    @Schema(description = "套餐价格", example = "2800.00")
    private BigDecimal price;

    @Schema(description = "原价", example = "3200.00")
    private BigDecimal originalPrice;

    @Schema(description = "价格单位", example = "月")
    private String unit;

    @Schema(description = "服务时长", example = "30天")
    private String serviceDuration;

    @Schema(description = "套餐类型", example = "long-term")
    private String packageType;

    @Schema(description = "服务描述", example = "专业保洁服务，每日2小时，30天服务周期")
    private String serviceDescription;

    @Schema(description = "详细服务内容", example = "<p>详细服务内容...</p>")
    private String serviceDetails;

    @Schema(description = "服务流程", example = "<p>服务流程...</p>")
    private String serviceProcess;

    @Schema(description = "购买须知", example = "购买须知...")
    private String purchaseNotice;

    @Schema(description = "状态", example = "active")
    private String status;

    @Schema(description = "提前预约天数", example = "1")
    private Integer advanceBookingDays;

    @Schema(description = "时间选择模式", example = "fixed")
    private String timeSelectionMode;

    @Schema(description = "预约模式", example = "start-date")
    private String appointmentMode;

    @Schema(description = "服务开始时间", example = "within-3-days")
    private String serviceStartTime;

    @Schema(description = "地址设置", example = "fixed")
    private String addressSetting;

    @Schema(description = "最大预约天数", example = "30")
    private Integer maxBookingDays;

    @Schema(description = "取消政策", example = "取消政策...")
    private String cancellationPolicy;

    @Schema(description = "审核状态", example = "approved")
    private String auditStatus;

    @Schema(description = "机构ID", example = "1001")
    private Long agencyId;

    @Schema(description = "机构名称", example = "金牌家政服务有限公司")
    private String agencyName;

    @Schema(description = "分类ID", example = "1")
    private Long categoryId;

    @Schema(description = "服务开始时间", example = "09:00:00")
    private String serviceTimeStart;

    @Schema(description = "服务结束时间", example = "11:00:00")
    private String serviceTimeEnd;

    @Schema(description = "休息日类型", example = "sunday")
    private String restDayType;

    @Schema(description = "服务时间段", example = "9:00-11:00")
    private String serviceTimespan;

    @Schema(description = "服务次数", example = "30")
    private Integer serviceTimes;

    @Schema(description = "有效期", example = "30")
    private Integer validityPeriod;

    @Schema(description = "有效期单位", example = "day")
    private String validityPeriodUnit;

    @Schema(description = "服务间隔类型", example = "daily")
    private String serviceIntervalType;

    @Schema(description = "服务间隔数值", example = "1")
    private Integer serviceIntervalValue;

    @Schema(description = "单次服务时长（小时）", example = "2")
    private Integer singleDurationHours;

    @Schema(description = "创建时间", example = "2024-01-01 10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2024-01-01 10:00:00")
    private LocalDateTime updateTime;

    // 前端展示所需字段
    @Schema(description = "标题", example = "月度保洁服务 | 30天")
    private String title;

    @Schema(description = "服务时长描述", example = "30天 | 每日2小时")
    private String duration;

    @Schema(description = "图片URL", example = "https://example.com/package1.png")
    private String image;

    @Schema(description = "标签", example = "长周期套餐")
    private String tag;

    @Schema(description = "当前价格", example = "2800.00")
    private String currentPrice;

    @Schema(description = "原价", example = "3200.00")
    private String originalPriceDisplay;

    @Schema(description = "服务次数", example = "已服务 129次")
    private String serviceCount;

    @Schema(description = "规格", example = "月嫂:三星级")
    private String specification;

    @Schema(description = "服务图片数组，支持多张图片轮播")
    private List<String> images;

}