package cn.bztmaster.cnt.module.publicbiz.controller.app.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.AuntHomeListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.AuntDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.AuntReviewListRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.AuntCertificateRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerAuntService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 雇主端阿姨 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 雇主端阿姨")
@RestController
@RequestMapping("/publicbiz/employer/aunt")
@Validated
@Slf4j
public class EmployerAuntController {

    @Resource
    private EmployerAuntService employerAuntService;

    @GetMapping("/home-list")
    @Operation(summary = "获取首页金牌阿姨列表")
    @PermitAll
    public CommonResult<AuntHomeListRespVO> getHomeAuntList(
            @Parameter(description = "返回数据条数，最大8条", example = "8") @RequestParam(value = "limit", required = false, defaultValue = "8") Integer limit) {

        log.info("获取首页阿姨列表请求，limit: {}", limit);

        // 参数验证
        if (limit != null && (limit <= 0 || limit > 8)) {
            return CommonResult.error(400, "limit参数必须在1-8之间");
        }

        AuntHomeListRespVO result = employerAuntService.getHomeAuntList(limit);
        return success(result);
    }

    @GetMapping("/detail")
    @Operation(summary = "获取阿姨详情信息")
    @PermitAll
    public CommonResult<AuntDetailRespVO> getAuntDetail(
            @Parameter(description = "阿姨ID", example = "11") @RequestParam("auntId") String auntId) {

        log.info("获取阿姨详情请求，auntId: {}", auntId);

        // 参数验证
        if (auntId == null || auntId.trim().isEmpty()) {
            return CommonResult.error(400, "阿姨ID不能为空");
        }

        AuntDetailRespVO result = employerAuntService.getAuntDetail(auntId);
        if (result == null) {
            return CommonResult.error(404, "阿姨信息不存在");
        }

        return success(result);
    }

    @GetMapping("/reviews")
    @Operation(summary = "获取阿姨评价列表")
    @PermitAll
    public CommonResult<AuntReviewListRespVO> getAuntReviews(
            @Parameter(description = "阿姨ID", example = "11") @RequestParam("auntId") String auntId,
            @Parameter(description = "页码，默认1", example = "1") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页数量，默认10", example = "10") @RequestParam(value = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = "评分筛选(1-5)", example = "5") @RequestParam(value = "rating", required = false) Integer rating) {

        log.info("获取阿姨评价列表请求，auntId: {}, page: {}, size: {}, rating: {}", auntId, page, size, rating);

        // 参数验证
        if (auntId == null || auntId.trim().isEmpty()) {
            return CommonResult.error(400, "阿姨ID不能为空");
        }

        if (rating != null && (rating < 1 || rating > 5)) {
            return CommonResult.error(400, "评分必须在1-5之间");
        }

        AuntReviewListRespVO result = employerAuntService.getAuntReviews(auntId, page, size, rating);
        return success(result);
    }

    @GetMapping("/certificates")
    @Operation(summary = "获取阿姨资质证书")
    @PermitAll
    public CommonResult<AuntCertificateRespVO> getAuntCertificates(
            @Parameter(description = "阿姨ID", example = "11") @RequestParam("auntId") String auntId) {

        log.info("获取阿姨资质证书请求，auntId: {}", auntId);

        // 参数验证
        if (auntId == null || auntId.trim().isEmpty()) {
            return CommonResult.error(400, "阿姨ID不能为空");
        }

        AuntCertificateRespVO result = employerAuntService.getAuntCertificates(auntId);
        return success(result);
    }
}