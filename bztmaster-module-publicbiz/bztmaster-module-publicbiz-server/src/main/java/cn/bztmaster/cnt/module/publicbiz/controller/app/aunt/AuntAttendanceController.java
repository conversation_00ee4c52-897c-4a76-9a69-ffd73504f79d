package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntAttendanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 阿姨考勤管理")
@RestController
@RequestMapping("/publicbiz/aunt/attendance")
@Validated
public class AuntAttendanceController {

    @Resource
    private AuntAttendanceService auntAttendanceService;

    @GetMapping("/summary")
    @Operation(summary = "获得阿姨月度考勤统计信息")
    @PermitAll
    public CommonResult<AuntAttendanceSummaryRespVO> getAttendanceSummary(@Valid AuntAttendanceSummaryReqVO reqVO) {
        // 验证阿姨信息是否存在
        if (reqVO.getOneId() == null || reqVO.getOneId().trim().isEmpty()) {
            return CommonResult.error(400, "阿姨OneID不能为空");
        }
        
        // 获取考勤统计信息
        AuntAttendanceSummaryRespVO result = auntAttendanceService.getAttendanceSummary(reqVO);
        
        if (result == null) {
            return CommonResult.error(404, "阿姨信息不存在");
        }
        
        return success(result);
    }

    @PostMapping("/apply")
    @Operation(summary = "提交请假或调休申请")
    @PermitAll
    public CommonResult<AuntAttendanceApplyRespVO> submitAttendanceApply(@Valid @RequestBody AuntAttendanceApplyReqVO reqVO) {
        // 验证阿姨信息是否存在
        if (reqVO.getOneId() == null || reqVO.getOneId().trim().isEmpty()) {
            return CommonResult.error(400, "阿姨OneID不能为空");
        }
        
        // 提交申请
        AuntAttendanceApplyRespVO result = auntAttendanceService.submitAttendanceApply(reqVO);
        
        if (result == null) {
            return CommonResult.error(404, "阿姨信息不存在");
        }
        
        return success(result);
    }

    @GetMapping("/history")
    @Operation(summary = "获得阿姨历史请假/调休申请列表")
    @PermitAll
    public CommonResult<PageResult<AuntAttendanceHistoryRespVO>> getAttendanceHistory(@Valid AuntAttendanceHistoryReqVO reqVO) {
        // 验证阿姨信息是否存在
        if (reqVO.getOneId() == null || reqVO.getOneId().trim().isEmpty()) {
            return CommonResult.error(400, "阿姨OneID不能为空");
        }
        
        // 获取申请列表
        PageResult<AuntAttendanceHistoryRespVO> result = auntAttendanceService.getAttendanceHistory(reqVO);
        
        if (result == null) {
            return CommonResult.error(404, "阿姨信息不存在");
        }
        
        return success(result);
    }

    @PostMapping("/approve")
    @Operation(summary = "更新请假/调休申请状态")
    @PermitAll
    public CommonResult<AuntAttendanceApproveRespVO> approveAttendanceApply(@Valid @RequestBody AuntAttendanceApproveReqVO reqVO) {
        // 审批申请
        AuntAttendanceApproveRespVO result = auntAttendanceService.approveAttendanceApply(reqVO);
        
        return success(result);
    }
}
