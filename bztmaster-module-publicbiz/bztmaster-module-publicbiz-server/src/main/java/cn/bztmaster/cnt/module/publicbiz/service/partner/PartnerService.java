package cn.bztmaster.cnt.module.publicbiz.service.partner;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.*;

public interface PartnerService {
    PageResult<PartnerRespVO> getPartnerPage(PartnerPageReqVO reqVO);

    Long createPartner(PartnerSaveReqVO reqVO);

    void updatePartner(PartnerUpdateReqVO reqVO);

    void deletePartner(Long id);

    PartnerRespVO getPartnerDetail(Long id);

    PartnerStatRespVO getPartnerStat();

    PartnerActiveListRespVO getActivePartnerList(String status, String type, String biz, String keyword);

    PartnerRespVO getPartnerByName(String name);
}