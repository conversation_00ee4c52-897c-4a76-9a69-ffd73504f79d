package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "用户 APP - 阿姨订单列表 Response VO")
@Data
public class AuntOrderListRespVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Long orderId;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "ORDER20241201001")
    private String orderNo;

    @Schema(description = "客户姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张先生")
    private String customerName;

    @Schema(description = "客户电话", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
    private String customerPhone;

    @Schema(description = "服务地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京市朝阳区建国路88号")
    private String serviceAddress;

    @Schema(description = "服务套餐名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "日常保洁套餐")
    private String servicePackageName;

    @Schema(description = "服务开始日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-12-01")
    private LocalDate serviceStartDate;

    @Schema(description = "服务结束日期", example = "2024-12-31")
    private LocalDate serviceEndDate;

    @Schema(description = "服务时长", requiredMode = Schema.RequiredMode.REQUIRED, example = "4小时")
    private String serviceDuration;

    @Schema(description = "服务金额", requiredMode = Schema.RequiredMode.REQUIRED, example = "200.00")
    private BigDecimal serviceAmount;

    @Schema(description = "订单状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "pending")
    private String orderStatus;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-12-01 10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "所属机构", example = "传能家政")
    private String agencyName;

} 