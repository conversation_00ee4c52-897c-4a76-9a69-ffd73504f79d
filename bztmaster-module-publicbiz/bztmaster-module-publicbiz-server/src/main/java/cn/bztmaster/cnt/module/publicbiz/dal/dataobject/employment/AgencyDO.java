package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 机构 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_agency")
@KeySequence("publicbiz_agency_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgencyDO extends BaseDO {

    /**
     * 机构ID
     */
    @TableId
    private Long id;
    /**
     *租户ID
     */
    private Long tenantId;

    /**
     * 机构编号
     */
    private String agencyNo;

    /**
     * 机构全称
     */
    private String agencyName;

    /**
     * 机构简称
     */
    private String agencyShortName;

    /**
     * 机构类型：cooperation-合作/competitor-竞争对手/other-其他
     */
    private String agencyType;

    /**
     * 法人代表
     */
    private String legalRepresentative;

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditCode;

    /**
     * 成立日期
     */
    private LocalDate establishmentDate;

    /**
     * 注册地址
     */
    private String registeredAddress;

    /**
     * 经营地址
     */
    private String operatingAddress;

    /**
     * 经营范围
     */
    private String businessScope;

    /**
     * 申请人姓名
     */
    private String applicantName;

    /**
     * 申请人电话
     */
    private String applicantPhone;

    /**
     * 申请时间
     */
    private LocalDateTime applicationTime;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 机构地址
     */
    private String agencyAddress;

    /**
     * 省份code
     */
    private String provinceCode;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市code
     */
    private String cityCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 区县code
     */
    private String districtCode;

    /**
     * 区县
     */
    private String district;

    /**
     * 街道
     */
    private String streetCode;

    /**
     * 街道
     */
    private String street;

    /**
     * 详细地址
     */
    private String detailAddress;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 位置精度：high-高精度/medium-中等精度/low-低精度
     */
    private String locationAccuracy;

    /**
     * 合作状态：cooperating-合作中/suspended-已暂停/terminated-已终止,pending-待审核
     */
    private String cooperationStatus;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 合同开始日期
     */
    private LocalDate contractStartDate;

    /**
     * 合同结束日期
     */
    private LocalDate contractEndDate;

    /**
     * 佣金比例
     */
    private BigDecimal commissionRate;

    /**
     * 审核状态：pending-待审核/approved-已通过/rejected-已拒绝
     */
    private String reviewStatus;

    /**
     * 审核人
     */
    private String reviewer;

    /**
     * 审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 审核备注
     */
    private String reviewRemark;

    /**
     * 状态：active-正常/inactive-停用/pending-待审核
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    // 新增字段 - 开票信息
    /**
     * 注册资本（万元）
     */
    private BigDecimal registeredCapital;

    /**
     * 开票名称
     */
    private String invoiceName;

    /**
     * 纳税人识别号
     */
    private String taxpayerId;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    // 新增字段 - 资质信息
    /**
     * 服务资质等级
     */
    private String serviceQualificationLevel;

    /**
     * 员工人数
     */
    private Integer employeeCount;

    /**
     * 服务区域
     */
    private String serviceArea;
}