package cn.bztmaster.cnt.module.publicbiz.service.employment.impl;

import cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyQualificationCreateVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyStatisticsReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyTrendReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyTrendRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.*;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.AgencyConvert;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyQualificationDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.PractitionerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.ServicePackageDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.DomesticOrderDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employer.AuntReviewDO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.partner.PartnerDO;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.AgencyQualificationMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.PractitionerMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.ServicePackageMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employment.DomesticOrderMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.employer.AuntReviewMapper;
import cn.bztmaster.cnt.module.publicbiz.dal.mysql.partner.PartnerMapper;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyService;
import cn.bztmaster.cnt.module.publicbiz.enums.LogRecordConstants;
import cn.bztmaster.cnt.module.infra.api.file.FileApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.multipart.MultipartFile;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.starter.annotation.LogRecord;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.date.DateUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.AGENCY_NOT_EXISTS;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.AGENCY_AGENCY_NO_EXISTS;
import static cn.bztmaster.cnt.module.publicbiz.enums.ErrorCodeConstants.AGENCY_UNIFIED_SOCIAL_CREDIT_CODE_EXISTS;

/**
 * 机构 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class AgencyServiceImpl implements AgencyService {

    @Resource
    private AgencyMapper agencyMapper;

    @Resource
    private AgencyQualificationMapper agencyQualificationMapper;

    @Resource
    private PractitionerMapper practitionerMapper;

    @Resource
    private ServicePackageMapper servicePackageMapper;

    @Resource
    private AuntReviewMapper auntReviewMapper;

    @Resource
    private DomesticOrderMapper domesticOrderMapper;

    @Resource
    private PartnerMapper partnerMapper;

    @Resource
    private FileApi fileApi;

    @Override
    public PageResult<AgencyRespVO> pageAgency(AgencyPageReqVO reqVO) {
        // 分页查询机构列表
        PageResult<AgencyDO> pageResult = agencyMapper.selectPage(reqVO);
        // 转换为VO
        return AgencyConvert.INSTANCE.convertPage(pageResult);
    }

    @Override
    public List<AgencyRespVO> listAgency(AgencyListReqVO reqVO) {
        // 查询机构列表（不分页）
        List<AgencyDO> agencyList = agencyMapper.selectList(reqVO);
        // 转换为VO
        return AgencyConvert.INSTANCE.convertList(agencyList);
    }

    @Override
    public AgencyRespVO getAgency(Long id) {
        // 获取机构信息
        AgencyDO agency = getAgencyDO(id);
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        // 获取资质文件列表
        List<AgencyQualificationDO> qualifications = agencyQualificationMapper.selectListByAgencyId(id);

        // 转换为VO
        AgencyRespVO agencyRespVO = AgencyConvert.INSTANCE.convert(agency);
        agencyRespVO.setQualifications(AgencyConvert.INSTANCE.convertQualificationList(qualifications));

        return agencyRespVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.AGENCY_TYPE, subType = LogRecordConstants.AGENCY_REVIEW_SUB_TYPE, bizNo = "{{#reqVO.id}}", success = LogRecordConstants.AGENCY_REVIEW_SUCCESS)
    public void updateAgency(AgencyUpdateReqVO reqVO) {
        // 校验机构是否存在
        AgencyDO agency = getAgencyDO(reqVO.getId());
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }
        // 更新审核状态
        agency.setReviewStatus(reqVO.getReviewStatus());
        agency.setReviewRemark(reqVO.getReviewRemark());
        agency.setReviewer(SecurityFrameworkUtils.getLoginUserNickname()); // 从当前登录用户获取
        agency.setReviewTime(LocalDateTime.now());

        // 根据审核状态更新合作状态
        if ("rejected".equals(reqVO.getReviewStatus())) {
            // 审核状态为已拒绝，合作状态修改为已暂停
            agency.setCooperationStatus("suspended");
        } else if ("approved".equals(reqVO.getReviewStatus())) {
            // 审核状态为已通过，合作状态为合作中
            agency.setCooperationStatus("cooperating");
        }

        // 执行数据库更新
        int updateResult = agencyMapper.updateById(agency);
        if (updateResult <= 0) {
            throw new RuntimeException("更新机构审核状态失败");
        }
        // 当审核状态为通过时，向合作伙伴表添加数据
        if ("approved".equals(reqVO.getReviewStatus())) {
            PartnerDO partner = new PartnerDO();
            partner.setName(agency.getAgencyName()); // 机构名称
            partner.setAgencyId(agency.getId()); // 关联机构ID
            partner.setStatus("合作中"); // 合作状态默认为合作中
            partner.setType("家政机构"); // 机构类型默认家政机构
            partner.setBiz("家政业务"); // 业务模块默认
            partner.setLegalPerson(agency.getLegalRepresentative()); // 法人代表
            partner.setFoundationDate(agency.getEstablishmentDate() != null ? 
                Date.from(agency.getEstablishmentDate().atStartOfDay(ZoneId.systemDefault()).toInstant()) : null); // 成立时间
            partner.setCreditCode(agency.getUnifiedSocialCreditCode()); // 统一社会信用代码
            partner.setRegisterAddress(agency.getRegisteredAddress()); // 注册地址
            partner.setBusinessAddress(agency.getOperatingAddress()); // 经营地址
            partner.setShortName(agency.getAgencyShortName()); // 机构简称
            partner.setMainBusiness(agency.getBusinessScope()); // 主营业务
            partner.setCreateTime(new Date());
            partner.setUpdateTime(new Date());
            partner.setCreator(SecurityFrameworkUtils.getLoginUserNickname());
            partner.setUpdater(SecurityFrameworkUtils.getLoginUserNickname());
            partner.setDeleted(false);
            partner.setTenantId(agency.getTenantId());
            
            partnerMapper.insert(partner);

            // 设置操作日志上下文 - 必须在方法返回前设置
            LogRecordContext.putVariable("agency", agency);
            LogRecordContext.putVariable("review", reqVO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.AGENCY_TYPE, subType = LogRecordConstants.AGENCY_CREATE_SUB_TYPE, bizNo = "{{#agency.id}}", success = LogRecordConstants.AGENCY_CREATE_SUCCESS)
    public Long createAgency(AgencyCreateReqVO reqVO) {
        // 校验机构编号是否已存在
        AgencyDO existingAgency = getAgencyDOByAgencyNo(reqVO.getAgencyNo());
        if (existingAgency != null) {
            throw ServiceExceptionUtil.exception(AGENCY_AGENCY_NO_EXISTS);
        }

        // 校验统一社会信用代码是否已存在
        if (reqVO.getUnifiedSocialCreditCode() != null) {
            AgencyDO existingByCode = agencyMapper.selectByUnifiedSocialCreditCode(reqVO.getUnifiedSocialCreditCode());
            if (existingByCode != null) {
                throw ServiceExceptionUtil.exception(AGENCY_UNIFIED_SOCIAL_CREDIT_CODE_EXISTS);
            }
        }

        // 转换为DO
        AgencyDO agency = AgencyConvert.INSTANCE.convert(reqVO);

        // 设置默认值
        agency.setCooperationStatus("pending");
        agency.setReviewStatus("pending");
        agency.setStatus("active");

        // 保存机构信息
        agencyMapper.insert(agency);

        // 保存资质文件信息
        if (reqVO.getQualifications() != null && !reqVO.getQualifications().isEmpty()) {
            for (AgencyQualificationCreateVO qualificationVO : reqVO.getQualifications()) {
                AgencyQualificationDO qualification = AgencyConvert.INSTANCE.convert(qualificationVO);
                qualification.setAgencyId(agency.getId());
                agencyQualificationMapper.insert(qualification);
            }
        }

        // 设置操作日志上下文
        LogRecordContext.putVariable("agency", agency);

        return agency.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = LogRecordConstants.AGENCY_TYPE, subType = LogRecordConstants.AGENCY_DELETE_SUB_TYPE, bizNo = "{{#agency.id}}", success = LogRecordConstants.AGENCY_DELETE_SUCCESS)
    public void deleteAgency(Long id) {
        // 校验机构是否存在
        AgencyDO agency = getAgencyDO(id);
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        // 删除资质文件
        agencyQualificationMapper.deleteByAgencyId(id);

        // 删除机构
        agencyMapper.deleteById(id);

        // 设置操作日志上下文
        LogRecordContext.putVariable("agency", agency);
    }

    @Override
    public AgencyDO getAgencyDO(Long id) {
        return agencyMapper.selectById(id);
    }

    @Override
    public AgencyDO getAgencyDOByAgencyNo(String agencyNo) {
        return agencyMapper.selectByAgencyNo(agencyNo);
    }

    // ========== 机构详情模块方法实现 ==========

    @Override
    public AgencyDetailRespVO getAgencyDetail(Long id) {
        // 查询机构基本信息
        AgencyDO agency = agencyMapper.selectOne(
                new LambdaQueryWrapperX<AgencyDO>()
                        .eq(AgencyDO::getId, id)
                        .eq(AgencyDO::getDeleted, false)
                        .eq(AgencyDO::getStatus, "active"));

        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        // 转换为响应对象
        AgencyDetailRespVO respVO = new AgencyDetailRespVO();
        respVO.setId(agency.getId());
        respVO.setAgencyNo(agency.getAgencyNo());
        respVO.setName(agency.getAgencyName());
        respVO.setShortName(agency.getAgencyShortName());
        respVO.setAgencyType(agency.getAgencyType());
        respVO.setLegalRepresentative(agency.getLegalRepresentative());
        respVO.setUnifiedSocialCreditCode(agency.getUnifiedSocialCreditCode());
        respVO.setEstablishmentDate(
                agency.getEstablishmentDate() != null ? agency.getEstablishmentDate().toString() : null);
        respVO.setRegisteredAddress(agency.getRegisteredAddress());
        respVO.setOperatingAddress(agency.getOperatingAddress());
        respVO.setBusinessScope(agency.getBusinessScope());
        respVO.setContactPerson(agency.getContactPerson());
        respVO.setContactPhone(agency.getContactPhone());
        respVO.setContactEmail(agency.getContactEmail());
        respVO.setAgencyAddress(agency.getAgencyAddress());
        respVO.setProvince(agency.getProvince());
        respVO.setCity(agency.getCity());
        respVO.setDistrict(agency.getDistrict());
        respVO.setStreet(agency.getStreet());
        respVO.setDetailAddress(agency.getDetailAddress());
        respVO.setLongitude(agency.getLongitude());
        respVO.setLatitude(agency.getLatitude());
        respVO.setLocationAccuracy(agency.getLocationAccuracy());
        respVO.setCooperationStatus(agency.getCooperationStatus());
        respVO.setContractNo(agency.getContractNo());
        respVO.setContractStartDate(
                agency.getContractStartDate() != null ? agency.getContractStartDate().toString() : null);
        respVO.setContractEndDate(agency.getContractEndDate() != null ? agency.getContractEndDate().toString() : null);
        respVO.setCommissionRate(agency.getCommissionRate());
        respVO.setReviewStatus(agency.getReviewStatus());
        respVO.setStatus(agency.getStatus());
        respVO.setRemark(agency.getRemark());
        respVO.setCreateTime(agency.getCreateTime());
        respVO.setUpdateTime(agency.getUpdateTime());

        // 新增字段映射
        respVO.setRegisteredCapital(agency.getRegisteredCapital());
        respVO.setInvoiceName(agency.getInvoiceName());
        respVO.setTaxpayerId(agency.getTaxpayerId());
        respVO.setBankName(agency.getBankName());
        respVO.setBankAccount(agency.getBankAccount());
        respVO.setServiceQualificationLevel(agency.getServiceQualificationLevel());
        respVO.setEmployeeCount(agency.getEmployeeCount());
        respVO.setServiceArea(agency.getServiceArea());

        // 计算前端展示字段
        calculateDisplayFields(respVO, agency);

        return respVO;
    }

    @Override
    public PageResult<AgencyAuntRespVO> getAgencyAunts(Long agencyId, Integer page, Integer size) {
        // 查询阿姨列表
        List<PractitionerDO> practitionerList = practitionerMapper.selectList(
                new LambdaQueryWrapperX<PractitionerDO>()
                        .eq(PractitionerDO::getAgencyId, agencyId)
                        .eq(PractitionerDO::getDeleted, false)
                        .eq(PractitionerDO::getStatus, "active")
                        .eq(PractitionerDO::getPlatformStatus, "cooperating")
                        .orderByDesc(PractitionerDO::getRating)
                        .orderByDesc(PractitionerDO::getTotalOrders)
                        .last("LIMIT " + (page - 1) * size + ", " + size));

        // 查询总数
        Long total = practitionerMapper.selectCount(
                new LambdaQueryWrapperX<PractitionerDO>()
                        .eq(PractitionerDO::getAgencyId, agencyId)
                        .eq(PractitionerDO::getDeleted, false)
                        .eq(PractitionerDO::getStatus, "active")
                        .eq(PractitionerDO::getPlatformStatus, "cooperating"));

        // 转换为响应对象
        List<AgencyAuntRespVO> respList = practitionerList.stream()
                .map(this::convertToAuntRespVO)
                .collect(Collectors.toList());

        return new PageResult<>(respList, total);
    }

    @Override
    public PageResult<AgencyPackageRespVO> getAgencyPackages(Long agencyId, Integer page, Integer size) {
        // 查询套餐列表
        List<ServicePackageDO> packageList = servicePackageMapper.selectList(
                new LambdaQueryWrapperX<ServicePackageDO>()
                        .eq(ServicePackageDO::getAgencyId, agencyId)
                        .eq(ServicePackageDO::getDeleted, false)
                        .eq(ServicePackageDO::getStatus, "active")
                        .eq(ServicePackageDO::getAuditStatus, "approved")
                        .orderByDesc(ServicePackageDO::getCreateTime)
                        .last("LIMIT " + (page - 1) * size + ", " + size));

        // 查询总数
        Long total = servicePackageMapper.selectCount(
                new LambdaQueryWrapperX<ServicePackageDO>()
                        .eq(ServicePackageDO::getAgencyId, agencyId)
                        .eq(ServicePackageDO::getDeleted, false)
                        .eq(ServicePackageDO::getStatus, "active")
                        .eq(ServicePackageDO::getAuditStatus, "approved"));

        // 转换为响应对象
        List<AgencyPackageRespVO> respList = packageList.stream()
                .map(this::convertToPackageRespVO)
                .collect(Collectors.toList());

        return new PageResult<>(respList, total);
    }

    @Override
    public AgencyReviewStatsRespVO getAgencyReviewStats(Long agencyId) {
        // 查询评价统计
        List<AuntReviewDO> reviewList = auntReviewMapper.selectList(
                new LambdaQueryWrapperX<AuntReviewDO>()
                        .eq(AuntReviewDO::getAgencyId, agencyId)
                        .eq(AuntReviewDO::getDeleted, false)
                        .eq(AuntReviewDO::getStatus, 1));

        AgencyReviewStatsRespVO respVO = new AgencyReviewStatsRespVO();
        respVO.setTotalReviews(reviewList.size());

        if (!reviewList.isEmpty()) {
            BigDecimal totalRating = reviewList.stream()
                    .map(AuntReviewDO::getRating)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal averageRating = totalRating.divide(BigDecimal.valueOf(reviewList.size()), 1,
                    BigDecimal.ROUND_HALF_UP);
            respVO.setAverageRating(averageRating);
        } else {
            respVO.setAverageRating(BigDecimal.ZERO);
        }

        return respVO;
    }

    /**
     * 计算前端展示字段
     */
    private void calculateDisplayFields(AgencyDetailRespVO respVO, AgencyDO agency) {
        // 设置默认值
        respVO.setLogo("");
        respVO.setBanner("");
        respVO.setRating(new BigDecimal("0"));
        respVO.setReviewCount(0);
        respVO.setServiceCount(0);
        respVO.setEstablishTime(0);

        // 查询机构门头照作为banner
        AgencyQualificationDO doorPhoto = agencyQualificationMapper.selectOne(
                new LambdaQueryWrapperX<AgencyQualificationDO>()
                        .eq(AgencyQualificationDO::getAgencyId, agency.getId())
                        .eq(AgencyQualificationDO::getFileType, "door_photo")
                        .eq(AgencyQualificationDO::getStatus, 1)
                        .eq(AgencyQualificationDO::getDeleted, false)
                        .orderByAsc(AgencyQualificationDO::getSortOrder)
                        .last("LIMIT 1"));

        if (doorPhoto != null) {
            respVO.setBanner(doorPhoto.getFileUrl());
        }

        // 计算成立年限
        if (agency.getEstablishmentDate() != null) {
            try {
                LocalDate now = LocalDate.now();
                int years = now.getYear() - agency.getEstablishmentDate().getYear();
                if (now.getDayOfYear() < agency.getEstablishmentDate().getDayOfYear()) {
                    years--;
                }
                respVO.setEstablishTime(Math.max(0, years));
            } catch (Exception e) {
                log.warn("计算成立年限失败: {}", agency.getEstablishmentDate());
            }
        }

        // 查询评价统计
        AgencyReviewStatsRespVO reviewStats = getAgencyReviewStats(agency.getId());
        respVO.setReviewCount(reviewStats.getTotalReviews());
        respVO.setRating(reviewStats.getAverageRating());

        // 查询服务数量（统计该机构下的订单总数）
        Long serviceCount = domesticOrderMapper.selectCount(
                new LambdaQueryWrapperX<DomesticOrderDO>()
                        .eq(DomesticOrderDO::getAgencyId, agency.getId())
                        .eq(DomesticOrderDO::getDeleted, false));
        respVO.setServiceCount(serviceCount != null ? serviceCount.intValue() : 0);
    }

    /**
     * 转换为阿姨响应对象
     */
    private AgencyAuntRespVO convertToAuntRespVO(PractitionerDO practitioner) {
        AgencyAuntRespVO respVO = new AgencyAuntRespVO();
        respVO.setId(practitioner.getId());
        respVO.setAuntOneid(practitioner.getAuntOneid());
        respVO.setName(practitioner.getName());
        respVO.setPhone(practitioner.getPhone());
        respVO.setIdCard(practitioner.getIdCard());
        respVO.setHometown(practitioner.getHometown());
        respVO.setAge(practitioner.getAge());
        respVO.setGender(practitioner.getGender());
        respVO.setAvatar(practitioner.getAvatar());
        respVO.setServiceType(practitioner.getServiceType());
        respVO.setExperienceYears(practitioner.getExperienceYears());
        respVO.setPlatformStatus(practitioner.getPlatformStatus());
        respVO.setRating(practitioner.getRating());
        respVO.setAgencyId(practitioner.getAgencyId());
        respVO.setAgencyName(practitioner.getAgencyName());
        respVO.setStatus(practitioner.getStatus());
        respVO.setCurrentStatus(practitioner.getCurrentStatus());
        respVO.setCurrentOrderId(practitioner.getCurrentOrderId());
        respVO.setTotalOrders(practitioner.getTotalOrders());
        respVO.setTotalIncome(practitioner.getTotalIncome());
        respVO.setCustomerSatisfaction(practitioner.getCustomerSatisfaction());
        respVO.setCreateTime(convertToLocalDateTime(practitioner.getCreateTime()));
        respVO.setUpdateTime(convertToLocalDateTime(practitioner.getUpdateTime()));

        // 设置标签
        respVO.setTag("金牌" + practitioner.getServiceType());

        return respVO;
    }

    /**
     * 转换为套餐响应对象
     */
    private AgencyPackageRespVO convertToPackageRespVO(ServicePackageDO servicePackage) {
        AgencyPackageRespVO respVO = new AgencyPackageRespVO();
        respVO.setId(servicePackage.getId());
        respVO.setName(servicePackage.getName());
        respVO.setCategory(servicePackage.getCategory());
        respVO.setThumbnail(servicePackage.getThumbnail());
        respVO.setPrice(servicePackage.getPrice());
        respVO.setOriginalPrice(servicePackage.getOriginalPrice());
        respVO.setUnit(servicePackage.getUnit());
        respVO.setServiceDuration(servicePackage.getServiceDuration());
        respVO.setPackageType(servicePackage.getPackageType());
        respVO.setServiceDescription(servicePackage.getServiceDescription());
        respVO.setServiceDetails(servicePackage.getServiceDetails());
        respVO.setServiceProcess(servicePackage.getServiceProcess());
        respVO.setPurchaseNotice(servicePackage.getPurchaseNotice());
        respVO.setStatus(servicePackage.getStatus());
        respVO.setAdvanceBookingDays(servicePackage.getAdvanceBookingDays());
        respVO.setTimeSelectionMode(servicePackage.getTimeSelectionMode());
        respVO.setAppointmentMode(servicePackage.getAppointmentMode());
        respVO.setServiceStartTime(servicePackage.getServiceStartTime());
        respVO.setAddressSetting(servicePackage.getAddressSetting());
        respVO.setMaxBookingDays(servicePackage.getMaxBookingDays());
        respVO.setCancellationPolicy(servicePackage.getCancellationPolicy());
        respVO.setAuditStatus(servicePackage.getAuditStatus());
        respVO.setAgencyId(servicePackage.getAgencyId());
        respVO.setAgencyName(servicePackage.getAgencyName());
        respVO.setCategoryId(servicePackage.getCategoryId());
        respVO.setServiceTimeStart(servicePackage.getServiceTimeStart());
        respVO.setServiceTimeEnd(servicePackage.getServiceTimeEnd());
        respVO.setRestDayType(servicePackage.getRestDayType());
        respVO.setServiceTimespan(servicePackage.getServiceTimespan());
        respVO.setServiceTimes(servicePackage.getServiceTimes());
        respVO.setValidityPeriod(servicePackage.getValidityPeriod());
        respVO.setValidityPeriodUnit(servicePackage.getValidityPeriodUnit());
        respVO.setServiceIntervalType(servicePackage.getServiceIntervalType());
        respVO.setServiceIntervalValue(servicePackage.getServiceIntervalValue());
        respVO.setSingleDurationHours(servicePackage.getSingleDurationHours());
        respVO.setCreateTime(servicePackage.getCreateTime());
        respVO.setUpdateTime(servicePackage.getUpdateTime());

        // 设置前端展示字段
        respVO.setTitle(servicePackage.getName());
        respVO.setDuration(servicePackage.getServiceDuration() + " | " +
                (servicePackage.getSingleDurationHours() != null ? servicePackage.getSingleDurationHours() + "小时"
                        : ""));
        respVO.setImage(servicePackage.getThumbnail());
        respVO.setTag("长周期套餐");

        return respVO;
    }

    /**
     * 将 Date 转换为 LocalDateTime
     */
    private LocalDateTime convertToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    @Override
    public AgencyStatisticsRespVO getAgencyStatistics(AgencyStatisticsReqVO reqVO) {
        Long agencyId = reqVO.getAgencyId();
        String rangeType = reqVO.getRangeType() != null ? reqVO.getRangeType() : "30";
        
        // 校验机构是否存在
        AgencyDO agency = getAgencyDO(agencyId);
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        AgencyStatisticsRespVO respVO = new AgencyStatisticsRespVO();

        // 根据范围类型计算时间范围
        LocalDateTime rangeStart = calculateRangeStart(rangeType);

        // 1. 服务工单数（订单总数）
        Long serviceOrderCount = domesticOrderMapper.selectCount(
            new LambdaQueryWrapperX<DomesticOrderDO>()
                .eq(DomesticOrderDO::getAgencyId, agencyId)
                .eq(DomesticOrderDO::getDeleted, false)
                .geIfPresent(DomesticOrderDO::getCreateTime, rangeStart));
        respVO.setServiceOrderCount(serviceOrderCount);

        // 2. 面试成功率（需要根据实际业务逻辑计算）
        // 这里假设有面试记录表，暂时设置为默认值
        respVO.setInterviewSuccessRate(new BigDecimal("0.0"));

        // 3. 机构评分
        AgencyReviewStatsRespVO reviewStats = getAgencyReviewStats(agencyId);
        respVO.setAgencyRating(reviewStats.getAverageRating());

        // 4. 客户投诉率（需要根据实际业务逻辑计算）
        // 这里暂时设置为默认值
        respVO.setComplaintRate(new BigDecimal("2.3"));

        // 5. 在职阿姨总数
        Long totalPractitioners = practitionerMapper.selectCount(
            new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getAgencyId, agencyId)
                .eq(PractitionerDO::getDeleted, false)
                .eq(PractitionerDO::getStatus, "active")
                .eq(PractitionerDO::getPlatformStatus, "cooperating"));
        respVO.setTotalPractitioners(totalPractitioners);

        // 6. 新增阿姨数（根据范围类型统计）
        Long newPractitioners = practitionerMapper.selectCount(
            new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getAgencyId, agencyId)
                .eq(PractitionerDO::getDeleted, false)
                .eq(PractitionerDO::getStatus, "active")
                .eq(PractitionerDO::getPlatformStatus, "cooperating")
                .geIfPresent(PractitionerDO::getCreateTime, rangeStart));
        respVO.setNewPractitioners(newPractitioners);

        // 7. 流失阿姨数（待岗阿姨）
        Long flowPractitioners = practitionerMapper.selectCount(
            new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getAgencyId, agencyId)
                .eq(PractitionerDO::getDeleted, false)
                .eq(PractitionerDO::getStatus, "inactive")
                .eq(PractitionerDO::getPlatformStatus, "terminated")
                .geIfPresent(PractitionerDO::getTerminatedTime, rangeStart));
        respVO.setFlowPractitioners(flowPractitioners);

        // 8. 上单阿姨数（正在服务中的阿姨）
        Long activeOrderPractitioners = practitionerMapper.selectCount(
            new LambdaQueryWrapperX<PractitionerDO>()
                .eq(PractitionerDO::getAgencyId, agencyId)
                .eq(PractitionerDO::getDeleted, false)
                .eq(PractitionerDO::getStatus, "active")
                .eq(PractitionerDO::getPlatformStatus, "cooperating")
                .eq(PractitionerDO::getCurrentStatus, "服务中"));
        respVO.setActiveOrderPractitioners(activeOrderPractitioners);

        // 9. 订单总金额（根据范围类型统计）
        List<DomesticOrderDO> allOrders = domesticOrderMapper.selectList(
            new LambdaQueryWrapperX<DomesticOrderDO>()
                .eq(DomesticOrderDO::getAgencyId, agencyId)
                .eq(DomesticOrderDO::getDeleted, false)
                .geIfPresent(DomesticOrderDO::getCreateTime, rangeStart));
        
        BigDecimal totalOrderAmount = allOrders.stream()
            .map(DomesticOrderDO::getTotalAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        respVO.setTotalOrderAmount(totalOrderAmount);

        // 10. 我方收入（假设按15%佣金计算）
        BigDecimal ourIncome = totalOrderAmount.multiply(new BigDecimal("0.0"));
        respVO.setOurIncome(ourIncome);

        // 11. 已结算金额（已完成订单的结算金额，根据范围类型统计）？？
        List<DomesticOrderDO> completedOrders = domesticOrderMapper.selectList(
            new LambdaQueryWrapperX<DomesticOrderDO>()
                .eq(DomesticOrderDO::getAgencyId, agencyId)
                .eq(DomesticOrderDO::getDeleted, false)
                //.eq(DomesticOrderDO::getStatus, "completed")
                .geIfPresent(DomesticOrderDO::getCreateTime, rangeStart));
        
        BigDecimal settledAmount = completedOrders.stream()
            .map(DomesticOrderDO::getTotalAmount)
            .filter(amount -> amount != null)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .multiply(new BigDecimal("0.15"));
        respVO.setSettledAmount(settledAmount);

        // 12. 待结算金额（我方收入 - 已结算金额）？？
        BigDecimal unsettledAmount = ourIncome.subtract(settledAmount);
        respVO.setUnsettledAmount(unsettledAmount);

        return respVO;
    }

    /**
     * 根据范围类型计算时间范围起点
     */
    private LocalDateTime calculateRangeStart(String rangeType) {
        LocalDateTime now = LocalDateTime.now();
        
        switch (rangeType) {
            case "30":
                return now.minusDays(30);
            case "90":
                return now.minusDays(90);
            case "year":
                return now.withMonth(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
            case "all":
            default:
                return null; // 全部时间，不设置时间范围
        }
    }

    @Override
    public AgencyTrendRespVO getAgencyTrendData(Long id) {
        Long agencyId = id;
        
        // 校验机构是否存在
        AgencyDO agency = getAgencyDO(agencyId);
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        AgencyTrendRespVO respVO = new AgencyTrendRespVO();

        // 1. 获取订单趋势数据（最近6个月）
        List<AgencyTrendRespVO.OrderTrendItem> orderTrends = getOrderTrends(agencyId);
        respVO.setOrderTrends(orderTrends);

        // 2. 获取服务分类数据
        List<AgencyTrendRespVO.ServiceCategoryItem> serviceCategories = getServiceCategories(agencyId);
        respVO.setServiceCategories(serviceCategories);

        // 3. 获取服务质量数据（最近6个月）
        List<AgencyTrendRespVO.ServiceQualityItem> serviceQualities = getServiceQualities(agencyId);
        respVO.setServiceQualities(serviceQualities);

        return respVO;
    }

    /**
     * 获取订单趋势数据
     */
    private List<AgencyTrendRespVO.OrderTrendItem> getOrderTrends(Long agencyId) {
        List<AgencyTrendRespVO.OrderTrendItem> trends = new ArrayList<>();

        // 计算最近6个月的起止时间（含当月）：从5个月前月初 00:00:00 到本月末 23:59:59.999999999
        LocalDateTime startMonth = LocalDateTime.now().minusMonths(5)
                .withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endMonthInclusive = startMonth.plusMonths(6).minusNanos(1);

        // 调用 Mapper 一次性按月分组统计
        List<Map<String, Object>> rows = domesticOrderMapper.selectCountGroupByCreateMonth(
                agencyId,
                java.sql.Timestamp.valueOf(startMonth),
                java.sql.Timestamp.valueOf(endMonthInclusive)
        );

        // 先构造最近6个月的月份序列，默认计数为0
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM");
        java.util.LinkedHashMap<String, Integer> monthToCount = new java.util.LinkedHashMap<>();
        for (int i = 0; i < 6; i++) {
            LocalDateTime monthPoint = startMonth.plusMonths(i);
            monthToCount.put(monthPoint.format(formatter), 0);
        }

        // 用数据库返回结果覆盖对应月份的计数
        if (rows != null) {
            for (Map<String, Object> row : rows) {
                String monthStr = String.valueOf(row.get("month"));
                Integer count = row.get("count") == null ? 0 : Integer.parseInt(String.valueOf(row.get("count")));
                if (monthToCount.containsKey(monthStr)) {
                    monthToCount.put(monthStr, count);
                }
            }
        }

        // 输出为按时间顺序的列表
        for (Map.Entry<String, Integer> entry : monthToCount.entrySet()) {
            AgencyTrendRespVO.OrderTrendItem item = new AgencyTrendRespVO.OrderTrendItem();
            item.setMonth(entry.getKey());
            item.setCount(entry.getValue());
            trends.add(item);
        }

        return trends;
    }

    /**
     * 获取服务分类数据
     */
    private List<AgencyTrendRespVO.ServiceCategoryItem> getServiceCategories(Long agencyId) {
        List<AgencyTrendRespVO.ServiceCategoryItem> categories = new ArrayList<>();
        
        // 获取近30天订单的服务类型统计
        LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        List<DomesticOrderDO> recentOrders = domesticOrderMapper.selectList(
            new LambdaQueryWrapperX<DomesticOrderDO>()
                .eq(DomesticOrderDO::getAgencyId, agencyId)
                .eq(DomesticOrderDO::getDeleted, false)
                .ge(DomesticOrderDO::getCreateTime, thirtyDaysAgo));
        
        // 按服务类型分组统计
        Map<String, Long> serviceTypeCounts = recentOrders.stream()
            .collect(Collectors.groupingBy(
                order -> order.getServiceCategoryName() != null ? order.getServiceCategoryName() : "其他",
                Collectors.counting()));
        
        // 计算总数
        long totalCount = recentOrders.size();
        
        // 转换为百分比
        for (Map.Entry<String, Long> entry : serviceTypeCounts.entrySet()) {
            AgencyTrendRespVO.ServiceCategoryItem item = new AgencyTrendRespVO.ServiceCategoryItem();
            item.setServiceType(entry.getKey());
            
            BigDecimal percentage = totalCount > 0 
                ? new BigDecimal(entry.getValue()).multiply(new BigDecimal("100"))
                    .divide(new BigDecimal(totalCount), 2, java.math.RoundingMode.HALF_UP)
                : new BigDecimal("0.0");
            item.setPercentage(percentage);
            
            categories.add(item);
        }
        
        return categories;
    }

    /**
     * 获取服务质量数据
     */
    private List<AgencyTrendRespVO.ServiceQualityItem> getServiceQualities(Long agencyId) {
        List<AgencyTrendRespVO.ServiceQualityItem> qualities = new ArrayList<>();

        // 计算最近6个月的起止时间（含当月）
        LocalDateTime startMonth = LocalDateTime.now().minusMonths(5)
                .withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0).withNano(0);
        LocalDateTime endMonthInclusive = startMonth.plusMonths(6).minusNanos(1);

        // 一次性按月分组统计均分
        List<Map<String, Object>> rows = auntReviewMapper.selectAvgRatingGroupByCreateMonth(
                agencyId,
                java.sql.Timestamp.valueOf(startMonth),
                java.sql.Timestamp.valueOf(endMonthInclusive)
        );

        // 先构造最近6个月的月份序列，默认评分为0.00
        java.time.format.DateTimeFormatter formatter = java.time.format.DateTimeFormatter.ofPattern("yyyy-MM");
        java.util.LinkedHashMap<String, BigDecimal> monthToScore = new java.util.LinkedHashMap<>();
        for (int i = 0; i < 6; i++) {
            LocalDateTime monthPoint = startMonth.plusMonths(i);
            monthToScore.put(monthPoint.format(formatter), new BigDecimal("0.00"));
        }

        // 用数据库返回结果覆盖对应月份的评分
        if (rows != null) {
            for (Map<String, Object> row : rows) {
                String monthStr = String.valueOf(row.get("month"));
                Object avgObj = row.get("avgRating");
                BigDecimal avg = avgObj == null ? new BigDecimal("0.00") : new BigDecimal(String.valueOf(avgObj));
                avg = avg.setScale(2, java.math.RoundingMode.HALF_UP);
                if (monthToScore.containsKey(monthStr)) {
                    monthToScore.put(monthStr, avg);
                }
            }
        }

        // 输出为按时间顺序的列表
        for (Map.Entry<String, BigDecimal> entry : monthToScore.entrySet()) {
            AgencyTrendRespVO.ServiceQualityItem item = new AgencyTrendRespVO.ServiceQualityItem();
            item.setMonth(entry.getKey());
            item.setScore(entry.getValue());
            qualities.add(item);
        }

        return qualities;
    }

    // ========== 机构注册相关方法实现 ==========

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AgencyRegisterResponseVO registerAgency(AgencyRegisterRequestVO request) {
        // 1. 参数验证
        validateAgencyInfo(request.getAgencyInfo());
        validateDocumentFiles(request.getDocumentFiles());

        // 2. 检查统一社会信用代码是否已存在
        if (existsByCreditCode(request.getAgencyInfo().getCreditCode())) {
            throw ServiceExceptionUtil.exception(AGENCY_UNIFIED_SOCIAL_CREDIT_CODE_EXISTS);
        }

        // 3. 生成机构编号
        String agencyNo = generateAgencyNo();

        // 4. 保存机构基本信息
        AgencyDO agency = new AgencyDO();
        agency.setAgencyNo(agencyNo);
        agency.setAgencyName(request.getAgencyInfo().getAgencyName());
        agency.setAgencyType(request.getAgencyInfo().getAgencyType());
        agency.setUnifiedSocialCreditCode(request.getAgencyInfo().getCreditCode());
        agency.setEstablishmentDate(LocalDate.parse(request.getAgencyInfo().getEstablishDate()));
        agency.setRegisteredCapital(request.getAgencyInfo().getRegisteredCapital());
        agency.setBusinessScope(request.getAgencyInfo().getBusinessScope());
        agency.setContactPerson(request.getAgencyInfo().getContactName());
        agency.setContactPhone(request.getAgencyInfo().getContactPhone());
        agency.setContactEmail(request.getAgencyInfo().getEmail());
        agency.setAgencyAddress(request.getAgencyInfo().getAddress());
        agency.setProvinceCode(request.getAgencyInfo().getProvinceCode());
        agency.setProvince(request.getAgencyInfo().getProvince());
        agency.setCityCode(request.getAgencyInfo().getCityCode());
        agency.setCity(request.getAgencyInfo().getCity());
        agency.setDistrictCode(request.getAgencyInfo().getDistrictCode());
        agency.setDistrict(request.getAgencyInfo().getDistrict());
        agency.setStreetCode(request.getAgencyInfo().getStreetCode());
        agency.setStreet(request.getAgencyInfo().getStreet());
        agency.setDetailAddress(request.getAgencyInfo().getDetailAddress());
        agency.setLongitude(request.getAgencyInfo().getLongitude());
        agency.setLatitude(request.getAgencyInfo().getLatitude());
        agency.setInvoiceName(request.getAgencyInfo().getInvoiceName());
        agency.setTaxpayerId(request.getAgencyInfo().getTaxNumber());
        agency.setBankName(request.getAgencyInfo().getBankName());
        agency.setBankAccount(request.getAgencyInfo().getBankAccount());
        agency.setServiceQualificationLevel(request.getAgencyInfo().getQualificationLevel());
        agency.setEmployeeCount(request.getAgencyInfo().getEmployeeCount());
        agency.setServiceArea(request.getAgencyInfo().getServiceArea());
        agency.setReviewStatus("pending");
        agency.setStatus("pending");
        agency.setApplicationTime(LocalDateTime.now());
        agency.setApplicantName(request.getAgencyInfo().getContactName());
        agency.setApplicantPhone(request.getAgencyInfo().getContactPhone());

        agencyMapper.insert(agency);

        // 5. 保存证照文件信息
        saveAgencyDocumentFiles(agency.getId(), request.getDocumentFiles());

        // 6. 生成申请编号
        String applicationId = generateApplicationId(agency.getId());

        // 7. 组装返回数据
        AgencyRegisterResponseVO response = new AgencyRegisterResponseVO();
        response.setApplicationId(applicationId);
        response.setAgencyId(agency.getId());
        response.setReviewStatus("pending");
        response.setSubmitTime(LocalDateTime.now());
        response.setEstimatedReviewTime("3-5个工作日");

        return response;
    }

    @Override
    public FileUploadResponseVO uploadAgencyFile(MultipartFile file, String fileType, String fileCategory, Long agencyId, String sessionId) {
        // 1. 文件验证
        if (file == null || file.isEmpty()) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "文件不能为空");
        }

        // 2. 文件大小验证（10MB）
        if (file.getSize() > 10 * 1024 * 1024) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "文件大小不能超过10MB");
        }

        // 3. 文件类型验证
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !isValidFileType(originalFilename)) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "不支持的文件类型");
        }

        // 4. 生成文件名并上传
        String fileName = generateFileName(fileCategory, originalFilename);
        String fileUrl = uploadFileToStorage(file, fileName);

        // 5. 处理文件信息
        if (agencyId != null) {
            // 已有机构ID，直接保存到数据库
            AgencyQualificationDO qualification = new AgencyQualificationDO();
            qualification.setAgencyId(agencyId);
            qualification.setFileType(fileType);
            qualification.setFileCategory(fileCategory);
            qualification.setFileName(fileName);
            qualification.setFileUrl(fileUrl);
            qualification.setFileSize(file.getSize());
            qualification.setFileExtension(getFileExtension(originalFilename));
            qualification.setSortOrder(0);
            qualification.setStatus(1);

            agencyQualificationMapper.insert(qualification);

            // 6. 组装返回数据
            FileUploadResponseVO response = new FileUploadResponseVO();
            response.setFileUrl(fileUrl);
            response.setFileName(fileName);
            response.setFileSize(file.getSize());
            response.setFileId(qualification.getId());

            return response;
        } else {
            // 注册阶段，只返回文件URL，不保存到数据库
            log.info("注册阶段上传文件，sessionId: {}, fileName: {}, fileUrl: {}", sessionId, fileName, fileUrl);
            
            // 6. 组装返回数据
            FileUploadResponseVO response = new FileUploadResponseVO();
            response.setFileUrl(fileUrl);
            response.setFileName(fileName);
            response.setFileSize(file.getSize());
            response.setFileId(null); // 注册阶段，没有数据库ID

            return response;
        }
    }

    @Override
    public AgencyRegisterStatusRespVO getRegisterStatus(String applicationId) {
        // 1. 从申请编号中提取机构ID
        Long agencyId = extractAgencyIdFromApplicationId(applicationId);
        if (agencyId == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "申请编号格式不正确");
        }

        // 2. 查询机构信息
        AgencyDO agency = getAgencyDO(agencyId);
        if (agency == null) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS);
        }

        // 3. 组装返回数据
        AgencyRegisterStatusRespVO response = new AgencyRegisterStatusRespVO();
        response.setApplicationId(applicationId);
        response.setAgencyId(agencyId);
        response.setReviewStatus(agency.getReviewStatus());
        response.setSubmitTime(agency.getApplicationTime());
        response.setReviewTime(agency.getReviewTime());
        response.setReviewRemark(agency.getReviewRemark());
        response.setEstimatedReviewTime("3-5个工作日");

        return response;
    }

    @Override
    public boolean existsByCreditCode(String creditCode) {
        if (StrUtil.isEmpty(creditCode)) {
            return false;
        }
        AgencyDO existingAgency = agencyMapper.selectByUnifiedSocialCreditCode(creditCode);
        return existingAgency != null;
    }

    // ========== 私有方法 ==========

    /**
     * 验证机构信息
     */
    private void validateAgencyInfo(AgencyRegisterRequestVO.AgencyInfoVO agencyInfo) {
        // 机构名称验证
        if (StrUtil.isEmpty(agencyInfo.getAgencyName()) || agencyInfo.getAgencyName().length() > 200) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "机构名称不能为空且长度不能超过200个字符");
        }

        // 统一社会信用代码验证（18位）
        if (!agencyInfo.getCreditCode().matches("^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$")) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "统一社会信用代码格式不正确");
        }

        // 机构类型验证
        if (StrUtil.isEmpty(agencyInfo.getAgencyType())) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "机构类型不能为空");
        }

        // 成立时间验证
        if (StrUtil.isEmpty(agencyInfo.getEstablishDate())) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "成立时间不能为空");
        }

        // 注册资本验证
        if (agencyInfo.getRegisteredCapital() == null || agencyInfo.getRegisteredCapital().compareTo(BigDecimal.ZERO) <= 0) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "注册资本必须大于0");
        }

        // 经营范围验证
        if (StrUtil.isEmpty(agencyInfo.getBusinessScope()) || agencyInfo.getBusinessScope().length() > 500) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "经营范围不能为空且长度不能超过500个字符");
        }

        // 联系人信息验证
        if (StrUtil.isEmpty(agencyInfo.getContactName()) || agencyInfo.getContactName().length() > 50) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "联系人姓名不能为空且长度不能超过50个字符");
        }

        if (!agencyInfo.getContactPhone().matches("^1[3-9]\\d{9}$")) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "联系电话格式不正确");
        }

        if (StrUtil.isNotEmpty(agencyInfo.getEmail()) && 
            !agencyInfo.getEmail().matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$")) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "邮箱格式不正确");
        }

        if (StrUtil.isEmpty(agencyInfo.getAddress()) || agencyInfo.getAddress().length() > 500) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "联系地址不能为空且长度不能超过500个字符");
        }

        // 开票信息验证
        if (StrUtil.isEmpty(agencyInfo.getInvoiceName()) || agencyInfo.getInvoiceName().length() > 200) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "开票名称不能为空且长度不能超过200个字符");
        }

        if (StrUtil.isEmpty(agencyInfo.getTaxNumber()) || agencyInfo.getTaxNumber().length() > 50) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "纳税人识别号不能为空且长度不能超过50个字符");
        }
    }

    /**
     * 验证证照文件列表
     */
    private void validateDocumentFiles(List<AgencyRegisterRequestVO.DocumentFileVO> documentFiles) {
        if (documentFiles == null || documentFiles.isEmpty()) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "证照文件不能为空");
        }

        // 验证必填文件类型
        boolean hasBusinessLicense = false;
        boolean hasIdCardFront = false;
        boolean hasIdCardBack = false;

        for (AgencyRegisterRequestVO.DocumentFileVO file : documentFiles) {
            if (StrUtil.isEmpty(file.getFileUrl())) {
                throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "文件路径不能为空");
            }
            if (StrUtil.isEmpty(file.getFileType())) {
                throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "文件类型不能为空");
            }
            if (StrUtil.isEmpty(file.getFileCategory())) {
                throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "文件分类不能为空");
            }

            // 检查必填文件类型
            if ("business_license".equals(file.getFileCategory())) {
                hasBusinessLicense = true;
            } else if ("id_card_front".equals(file.getFileCategory())) {
                hasIdCardFront = true;
            } else if ("id_card_back".equals(file.getFileCategory())) {
                hasIdCardBack = true;
            }
        }

        if (!hasBusinessLicense) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "营业执照不能为空");
        }
        if (!hasIdCardFront) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "法人身份证正面不能为空");
        }
        if (!hasIdCardBack) {
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "法人身份证反面不能为空");
        }
    }

    /**
     * 生成机构编号
     */
    private String generateAgencyNo() {
        String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
        String sequence = String.format("%04d", getNextSequence("agency_no"));
        return "AG" + dateStr + sequence;
    }

    /**
     * 生成申请编号
     */
    private String generateApplicationId(Long agencyId) {
        String dateStr = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
        String dateStr2 = DateUtil.format(LocalDateTime.now(), "yyyyMMdd");
        String sequence = String.format("%04d", agencyId % 10000);
        return "AG" + dateStr2 + sequence;
    }

    /**
     * 保存机构证照文件列表
     */
    private void saveAgencyDocumentFiles(Long agencyId, List<AgencyRegisterRequestVO.DocumentFileVO> documentFiles) {
        for (AgencyRegisterRequestVO.DocumentFileVO file : documentFiles) {
            AgencyQualificationDO qualification = new AgencyQualificationDO();
            qualification.setAgencyId(agencyId);
            qualification.setFileType(file.getFileType());
            qualification.setFileCategory(file.getFileCategory());
            qualification.setFileUrl(file.getFileUrl());
            qualification.setFileName(generateFileNameFromUrl(file.getFileUrl()));
            qualification.setSortOrder(file.getSortOrder() != null ? file.getSortOrder() : 0);
            qualification.setStatus(1);
            qualification.setFileDescription(file.getFileDescription());
            agencyQualificationMapper.insert(qualification);
        }
    }



    /**
     * 获取下一个序列号
     */
    private int getNextSequence(String sequenceName) {
        // TODO: 实现序列号生成逻辑，这里暂时返回随机数
        return (int) (Math.random() * 9999) + 1;
    }

    /**
     * 验证文件类型
     */
    private boolean isValidFileType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        return extension.matches("^(jpg|jpeg|png|pdf)$");
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1) : "";
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String fileCategory, String originalFilename) {
        String extension = getFileExtension(originalFilename);
        return fileCategory + "_" + IdUtil.fastSimpleUUID() + "." + extension;
    }

    /**
     * 从URL生成文件名
     */
    private String generateFileNameFromUrl(String fileUrl) {
        if (StrUtil.isEmpty(fileUrl)) {
            return "unknown_file.jpg";
        }
        
        // 从URL中提取文件名
        int lastSlashIndex = fileUrl.lastIndexOf('/');
        if (lastSlashIndex >= 0 && lastSlashIndex < fileUrl.length() - 1) {
            String fileName = fileUrl.substring(lastSlashIndex + 1);
            // 如果文件名包含查询参数，去掉查询参数
            int queryIndex = fileName.indexOf('?');
            if (queryIndex > 0) {
                fileName = fileName.substring(0, queryIndex);
            }
            return fileName;
        }
        
        // 如果无法从URL提取文件名，生成一个默认文件名
        return "file_" + IdUtil.fastSimpleUUID() + ".jpg";
    }

    /**
     * 上传文件到存储
     */
    private String uploadFileToStorage(MultipartFile file, String fileName) {
        try {
            // 获取文件内容
            byte[] fileContent = file.getBytes();
            
            // 获取文件类型
            String contentType = file.getContentType();
            
            // 使用Feign调用文件上传服务
            String fileUrl = fileApi.createFile(fileContent, fileName, "agency_qualification", contentType);
            
            log.info("文件上传成功，文件名：{}，文件URL：{}", fileName, fileUrl);
            return fileUrl;
        } catch (Exception e) {
            log.error("文件上传失败，文件名：{}，错误信息：{}", fileName, e.getMessage(), e);
            throw ServiceExceptionUtil.exception(AGENCY_NOT_EXISTS, "文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 从申请编号中提取机构ID
     */
    private Long extractAgencyIdFromApplicationId(String applicationId) {
        try {
            // 申请编号格式：AG + 年月日 + 4位序号
            String sequence = applicationId.substring(10);
            return Long.parseLong(sequence);
        } catch (Exception e) {
            return null;
        }
    }


}