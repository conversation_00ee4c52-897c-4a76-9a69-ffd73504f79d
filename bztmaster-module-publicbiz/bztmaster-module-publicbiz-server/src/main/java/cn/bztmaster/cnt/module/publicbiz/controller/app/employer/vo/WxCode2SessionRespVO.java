package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import lombok.Data;

/**
 * 微信登录响应 VO
 *
 * <AUTHOR>
 */
@Data
public class WxCode2SessionRespVO {

    /**
     * 用户唯一标识
     */
    private String openid;

    /**
     * 会话密钥
     */
    private String session_key;

    /**
     * 用户在开放平台的唯一标识符
     */
    private String unionid;

    /**
     * 错误码
     */
    private Integer errcode;

    /**
     * 错误信息
     */
    private String errmsg;

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return errcode == null || errcode == 0;
    }

}