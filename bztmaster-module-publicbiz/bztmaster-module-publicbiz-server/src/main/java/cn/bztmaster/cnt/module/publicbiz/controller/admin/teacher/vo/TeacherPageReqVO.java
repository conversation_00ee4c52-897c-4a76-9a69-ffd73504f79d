package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "师资库 - 讲师分页 Request VO")
public class TeacherPageReqVO extends PageParam {
    private String type;
    private String biz;
    private String status;
    private String keyword;
} 