package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 阿姨请假/调休申请审批 Response VO")
@Data
public class AuntAttendanceApproveRespVO {

    @Schema(description = "申请单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "apply_20250701001")
    private String applyId;

    @Schema(description = "申请状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "APPROVED")
    private String status;

    @Schema(description = "审批时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-07-01 14:00:00")
    private LocalDateTime approveTime;
}
