package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 服务分类列表 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "服务分类列表 Response VO")
@Data
public class ServiceCategoryListRespVO {

    @Schema(description = "服务分类列表")
    private List<ServiceCategoryRespVO> list;

    @Schema(description = "总记录数", requiredMode = Schema.RequiredMode.REQUIRED, example = "100")
    private Long total;

    @Schema(description = "当前页码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer page;

    @Schema(description = "每页数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer pageSize;

    @Schema(description = "总页数", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    private Integer totalPages;

}