package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "用户 APP - 阿姨考勤统计查询 Response VO")
@Data
public class AuntAttendanceSummaryRespVO {

    @Schema(description = "已工作天数", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    private Integer workedDays;

    @Schema(description = "总工作日天数", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    private Integer totalWorkDays;

    @Schema(description = "请假天数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer leaveDays;

    @Schema(description = "调休天数", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer adjustDays;

    @Schema(description = "出勤天数", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer attendanceDays;

    @Schema(description = "查询年份", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024")
    private Integer year;

    @Schema(description = "查询月份", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer month;
}
