package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 高校实践订单审批响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单审批响应")
@Data
public class UniversityPracticeOrderApprovalRespVO {

    @Schema(description = "发起是否成功", example = "true")
    private Boolean success;

    @Schema(description = "审批流程ID", example = "AP202406001")
    private String approvalId;

    @Schema(description = "审批编号", example = "AP-202406001")
    private String approvalNo;

    @Schema(description = "审批状态", example = "pending")
    private String approvalStatus;

    @Schema(description = "创建时间", example = "2024-06-21 10:30:00")
    private String createTime;

}
