package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 发起审批请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 发起审批请求")
@Data
public class OrderApprovalInitiateReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "HT001")
    @NotBlank(message = "订单编号不能为空")
    private String orderNo;

    @Schema(description = "审批类型：order_approval(订单审批)、contract_approval(合同审批)、payment_approval(付款审批)", requiredMode = Schema.RequiredMode.REQUIRED, example = "order_approval")
    @NotBlank(message = "审批类型不能为空")
    private String approvalType;

    @Schema(description = "优先级：low(低)、normal(普通)、high(高)、urgent(紧急)", example = "normal")
    private String priority;

    @Schema(description = "审批人ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "审批人ID列表不能为空")
    private List<Long> approverIds;

    @Schema(description = "审批说明", example = "请审批该高校实践订单")
    private String comments;
}
