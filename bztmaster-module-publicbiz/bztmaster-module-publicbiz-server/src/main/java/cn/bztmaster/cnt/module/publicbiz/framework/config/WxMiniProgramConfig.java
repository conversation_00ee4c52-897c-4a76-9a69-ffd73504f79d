package cn.bztmaster.cnt.module.publicbiz.framework.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 微信小程序配置
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "wx.miniprogram")
public class WxMiniProgramConfig {

    /**
     * 小程序AppID
     */
    private String appId = "wxf0cb5e5168f30b6a";

    /**
     * 小程序AppSecret
     */
    private String secret = "c4add048842fa0d7149d82f51f2d1852";

    /**
     * 微信登录接口URL
     */
    private String code2SessionUrl = "https://api.weixin.qq.com/sns/jscode2session";

}