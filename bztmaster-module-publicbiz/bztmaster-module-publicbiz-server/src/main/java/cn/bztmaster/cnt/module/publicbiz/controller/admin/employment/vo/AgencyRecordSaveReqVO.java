package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Schema(description = "管理后台 - 机构记录保存 Request VO")
@Data
public class AgencyRecordSaveReqVO {

    @Schema(description = "记录ID（更新时必填）", example = "1")
    private Long id;

    @Schema(description = "机构ID", example = "1001")
    private Long agencyId;

    @Schema(description = "记录类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "incentive")
    @NotBlank(message = "记录类型不能为空")
    private String recordType;

    @Schema(description = "记录日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-15")
    @NotNull(message = "记录日期不能为空")
    private LocalDate recordDate;

    @Schema(description = "记录标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "客户好评 - 超出预期")
    @NotBlank(message = "记录标题不能为空")
    @Size(max = 200, message = "记录标题长度不能超过200字符")
    private String title;

    @Schema(description = "记录详细描述", example = "关联订单 20240110021, 客户致电表扬阿姨工作细致, 服务超出预期。")
    private String description;

    @Schema(description = "信用分影响", example = "5")
    private Integer creditImpact;

    @Schema(description = "金额影响", example = "50.00")
    private BigDecimal amountImpact;

    @Schema(description = "其他影响", example = "暂停接单、降级等")
    private String otherImpact;

    @Schema(description = "关联信息", example = "订单号：20240110021")
    private String relatedInfo;

    @Schema(description = "处理状态", example = "pending")
    private String status;

    @Schema(description = "跟进日期", example = "2024-01-16")
    private LocalDate followUpDate;

    @Schema(description = "跟进事项", example = "联系机构负责人了解情况")
    private String followUpItem;

    @Schema(description = "备注说明", example = "客户主动表扬，建议给予奖励")
    private String remarks;

    @Schema(description = "附件列表")
    private List<AgencyRecordAttachmentSaveReqVO> attachments;
} 