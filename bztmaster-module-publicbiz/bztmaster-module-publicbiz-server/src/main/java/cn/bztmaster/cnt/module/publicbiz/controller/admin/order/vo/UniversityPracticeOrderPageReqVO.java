package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.bztmaster.cnt.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 高校实践订单分页 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UniversityPracticeOrderPageReqVO extends PageParam {

    @Schema(description = "订单状态", example = "pending_approval")
    private String orderStatus;

    @Schema(description = "支付状态", example = "pending")
    private String paymentStatus;

    @Schema(description = "关键词搜索（项目名称、高校、企业）", example = "暑期实践")
    private String keyword;

    @Schema(description = "关联商机ID", example = "OPP001")
    private String opportunityId;

    @Schema(description = "开始日期", example = "2024-06-01")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "负责人ID", example = "1001")
    private Long managerId;

    // 添加对前端传递的page和size参数的支持
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page;

    @Schema(description = "每页条数，最大值为100", example = "10")
    private Integer size;

    /**
     * 重写getPageNo方法，优先使用page参数
     */
    @Override
    public Integer getPageNo() {
        if (page != null) {
            return page;
        }
        return super.getPageNo();
    }

    /**
     * 重写getPageSize方法，优先使用size参数
     */
    @Override
    public Integer getPageSize() {
        if (size != null) {
            return size;
        }
        return super.getPageSize();
    }
}





