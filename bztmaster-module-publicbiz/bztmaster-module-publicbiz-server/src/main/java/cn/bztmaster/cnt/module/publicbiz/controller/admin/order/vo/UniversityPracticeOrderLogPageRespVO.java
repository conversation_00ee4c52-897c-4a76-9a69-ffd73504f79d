package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 高校实践订单日志分页 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单日志分页 Response VO")
@Data
public class UniversityPracticeOrderLogPageRespVO {

    @Schema(description = "日志ID", example = "1")
    private Long id;

    @Schema(description = "日志类型", example = "订单创建")
    private String logType;

    @Schema(description = "日志标题", example = "创建高校实践订单")
    private String logTitle;

    @Schema(description = "日志内容", example = "创建了新的高校实践订单")
    private String logContent;

    @Schema(description = "原状态", example = "")
    private String oldStatus;

    @Schema(description = "新状态", example = "draft")
    private String newStatus;

    @Schema(description = "操作人ID", example = "1001")
    private Long operatorId;

    @Schema(description = "操作人姓名", example = "张三")
    private String operatorName;

    @Schema(description = "操作人角色", example = "管理员")
    private String operatorRole;

    @Schema(description = "关联方类型", example = "高校")
    private String relatedPartyType;

    @Schema(description = "关联方名称", example = "XX大学")
    private String relatedPartyName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

}





