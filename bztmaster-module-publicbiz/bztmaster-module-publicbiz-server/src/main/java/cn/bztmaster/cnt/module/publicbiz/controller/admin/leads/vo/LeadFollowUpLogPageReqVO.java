package cn.bztmaster.cnt.module.publicbiz.controller.admin.leads.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 线索跟进记录分页查询 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 线索跟进记录分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class LeadFollowUpLogPageReqVO extends PageParam {

    @Schema(description = "关联的线索ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "LEAD20250721001")
    @NotEmpty(message = "线索ID不能为空")
    @Size(max = 32, message = "线索ID长度不能超过32")
    private String leadId;

    @Schema(description = "创建时间范围开始", example = "2025-07-01 00:00:00")
    private String beginCreateTime;

    @Schema(description = "创建时间范围结束", example = "2025-07-21 23:59:59")
    private String endCreateTime;
}