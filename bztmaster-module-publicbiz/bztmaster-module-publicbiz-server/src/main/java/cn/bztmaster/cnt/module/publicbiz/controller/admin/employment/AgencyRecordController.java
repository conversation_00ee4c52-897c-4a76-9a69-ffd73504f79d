package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.api.employment.dto.*;
import cn.bztmaster.cnt.module.publicbiz.convert.employment.AgencyRecordConvert;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.employment.AgencyRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 机构记录管理")
@RestController
@RequestMapping("/publicbiz/agencyRecord")
@Validated
@Slf4j
public class AgencyRecordController {

    @Resource
    private AgencyRecordService agencyRecordService;

    @PostMapping("/page")
    @Operation(summary = "分页查询激励/处罚记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency-record:query')")
    public CommonResult<PageResult<AgencyRecordRespVO>> pageAgencyRecord(@Valid @RequestBody AgencyRecordPageReqVO reqVO) {
        // 转换为DTO
        AgencyRecordPageReqDTO reqDTO = AgencyRecordConvert.INSTANCE.convert(reqVO);
        
        // 调用Service
        PageResult<AgencyRecordRespDTO> pageResult = agencyRecordService.pageAgencyRecord(reqDTO);
        
        // 转换为VO
        List<AgencyRecordRespVO> voList = AgencyRecordConvert.INSTANCE.convertVOList(pageResult.getList());
        PageResult<AgencyRecordRespVO> voPageResult = new PageResult<>(voList, pageResult.getTotal());
        
        return success(voPageResult);
    }

    @PostMapping("/create")
    @Operation(summary = "创建激励/处罚记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency-record:create')")
    public CommonResult<AgencyRecordRespVO> createAgencyRecord(@Valid @RequestBody AgencyRecordSaveReqVO reqVO) {
        // 转换为DTO
        AgencyRecordSaveReqDTO reqDTO = AgencyRecordConvert.INSTANCE.convert(reqVO);
        
        // 调用Service
        AgencyRecordRespDTO respDTO = agencyRecordService.createAgencyRecord(reqDTO);
        
        // 转换为VO
        AgencyRecordRespVO respVO = AgencyRecordConvert.INSTANCE.convert(respDTO);
        
        return success(respVO);
    }

    @PostMapping("/update")
    @Operation(summary = "更新激励/处罚记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency-record:update')")
    public CommonResult<Boolean> updateAgencyRecord(@Valid @RequestBody AgencyRecordSaveReqVO reqVO) {
        // 转换为DTO
        AgencyRecordSaveReqDTO reqDTO = AgencyRecordConvert.INSTANCE.convert(reqVO);
        
        // 调用Service
        agencyRecordService.updateAgencyRecord(reqDTO);
        
        return success(true);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取记录详情")
    @Parameter(name = "id", description = "记录ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency-record:query')")
    public CommonResult<AgencyRecordRespVO> getAgencyRecordDetail(@PathVariable("id") Long id) {
        // 调用Service
        AgencyRecordRespDTO respDTO = agencyRecordService.getAgencyRecordDetail(id);
        
        // 转换为VO
        AgencyRecordRespVO respVO = AgencyRecordConvert.INSTANCE.convert(respDTO);
        
        return success(respVO);
    }

    @GetMapping("/followUpList/{recordId}")
    @Operation(summary = "获取跟进记录列表")
    @Parameter(name = "recordId", description = "记录ID", required = true, example = "1")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency-record:query')")
    public CommonResult<List<AgencyRecordFollowUpRespVO>> getFollowUpList(@PathVariable("recordId") Long recordId) {
        // 调用Service
        List<AgencyRecordFollowUpRespDTO> respDTOList = agencyRecordService.getFollowUpList(recordId);
        
        // 转换为VO
        List<AgencyRecordFollowUpRespVO> respVOList = AgencyRecordConvert.INSTANCE.convertFollowUpVOList(respDTOList);
        
        return success(respVOList);
    }

    @PostMapping("/followUp/create")
    @Operation(summary = "新增跟进记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency-record:create')")
    public CommonResult<AgencyRecordFollowUpRespVO> createFollowUp(@Valid @RequestBody AgencyRecordFollowUpSaveReqVO reqVO) {
        // 转换为DTO
        AgencyRecordFollowUpSaveReqDTO reqDTO = AgencyRecordConvert.INSTANCE.convert(reqVO);
        
        // 调用Service
        AgencyRecordFollowUpRespDTO respDTO = agencyRecordService.createFollowUp(reqDTO);
        
        // 转换为VO
        AgencyRecordFollowUpRespVO respVO = AgencyRecordConvert.INSTANCE.convert(respDTO);
        
        return success(respVO);
    }

    @PostMapping("/communicationcreate")
    @Operation(summary = "新增沟通日志记录")
    @PreAuthorize("@ss.hasPermission('publicbiz:agency-record:create')")
    public CommonResult<AgencyRecordRespVO> createCommunication(@Valid @RequestBody AgencyRecordCommunicationSaveReqVO reqVO) {
        // 转换为DTO
        AgencyRecordCommunicationSaveReqDTO reqDTO = AgencyRecordConvert.INSTANCE.convert(reqVO);
        
        // 调用Service
        AgencyRecordRespDTO respDTO = agencyRecordService.createCommunication(reqDTO);
        
        // 转换为VO
        AgencyRecordRespVO respVO = AgencyRecordConvert.INSTANCE.convert(respDTO);
        
        return success(respVO);
    }
} 