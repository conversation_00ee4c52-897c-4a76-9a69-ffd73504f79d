package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户信息响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户信息响应 VO")
@Data
public class UserInfoRespVO {

    @Schema(description = "用户ID", example = "user_123456")
    private String id;

    @Schema(description = "微信openid", example = "wx_openid_123456")
    private String openid;

    @Schema(description = "用户昵称", example = "用户昵称")
    private String nickname;

    @Schema(description = "用户头像", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "手机号码", example = "13800138000")
    private String mobile;

    @Schema(description = "用户角色", example = "employer")
    private String role;

    @Schema(description = "用户状态", example = "active")
    private String status;

    @Schema(description = "创建时间", example = "2024-01-01 12:00:00")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", example = "2024-01-01 12:00:00")
    private LocalDateTime updateTime;

}