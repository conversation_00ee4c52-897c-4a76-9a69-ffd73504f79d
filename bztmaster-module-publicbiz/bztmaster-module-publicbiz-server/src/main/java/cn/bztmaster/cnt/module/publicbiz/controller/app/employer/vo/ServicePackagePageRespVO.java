package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 服务套餐分页响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端 - 服务套餐分页响应 VO")
@Data
public class ServicePackagePageRespVO {

    @Schema(description = "套餐列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ServicePackageRespVO> list;

    @Schema(description = "总数", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Long total;

    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer page;

    @Schema(description = "每页数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "20")
    private Integer pageSize;

}