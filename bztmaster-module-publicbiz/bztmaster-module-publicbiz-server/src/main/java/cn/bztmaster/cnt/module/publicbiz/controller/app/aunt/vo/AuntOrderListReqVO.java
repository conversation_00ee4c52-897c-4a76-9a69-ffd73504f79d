package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 阿姨订单列表 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AuntOrderListReqVO extends PageParam {

    @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "550e8400-e29b-41d4-a716-446655440000")
    @NotNull(message = "阿姨OneID不能为空")
    private String auntOneId;

    @Schema(description = "订单状态", example = "pending")
    private String orderStatus;

    @Schema(description = "客户姓名", example = "张先生")
    private String customerName;

    @Schema(description = "服务地址", example = "北京市朝阳区")
    private String serviceAddress;

} 