package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 高校实践订单合同上传请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单合同上传请求")
@Data
public class UniversityPracticeOrderContractUploadReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "HP202406001")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "合同类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "electronic")
    @NotBlank(message = "合同类型不能为空")
    private String contractType;

    @Schema(description = "备注", example = "纸质合同扫描件")
    private String remark;

}
