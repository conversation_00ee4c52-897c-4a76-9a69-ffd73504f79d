package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 家政服务任务生成请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务任务生成请求 VO")
@Data
public class DomesticTaskGenerateReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "是否强制重新生成任务（如果已存在任务）", example = "false")
    private Boolean forceRegenerate = false;

}
