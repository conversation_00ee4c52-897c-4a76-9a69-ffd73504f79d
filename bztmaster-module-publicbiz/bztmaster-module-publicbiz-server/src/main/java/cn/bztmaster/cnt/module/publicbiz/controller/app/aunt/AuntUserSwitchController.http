### 检查用户是否注册了家政人员信息（通过openId）
POST {{appApi}}/publicbiz/aunt/user-switch/check-registration
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}
Content-Type: application/json

{
  "openId": "o1234567890abcdef"
}

### 检查用户是否注册了家政人员信息（通过手机号）
POST {{appApi}}/publicbiz/aunt/user-switch/check-registration
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}
Content-Type: application/json

{
  "mobile": "13800138000"
}

### 检查用户是否注册了家政人员信息（同时提供openId和手机号）
POST {{appApi}}/publicbiz/aunt/user-switch/check-registration
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}
Content-Type: application/json

{
  "openId": "o1234567890abcdef",
  "mobile": "13800138000"
}

### 检查用户是否注册了家政人员信息（无参数，应该返回未注册）
POST {{appApi}}/publicbiz/aunt/user-switch/check-registration
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}
Content-Type: application/json

{
}
