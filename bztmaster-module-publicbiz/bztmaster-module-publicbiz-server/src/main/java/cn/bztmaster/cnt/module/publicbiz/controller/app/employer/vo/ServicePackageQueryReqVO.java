package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 服务套餐查询请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端 - 服务套餐查询请求 VO")
@Data
public class ServicePackageQueryReqVO {

    @Schema(description = "分类ID", example = "2")
    private Long categoryId;

    @Schema(description = "套餐名称关键词", example = "保洁")
    private String keyword;

    @Schema(description = "排序类型：distance-距离优先/price-价格优先/satisfaction-满意度优先/comprehensive-综合", example = "distance")
    private String sortType = "distance";

    @Schema(description = "用户当前经度", requiredMode = Schema.RequiredMode.REQUIRED, example = "116.397128")
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;

    @Schema(description = "用户当前纬度", requiredMode = Schema.RequiredMode.REQUIRED, example = "39.916527")
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;

    @Schema(description = "页码", example = "1")
    private Integer page = 1;

    @Schema(description = "每页数量", example = "20")
    private Integer pageSize = 20;

    @Schema(description = "偏移量", hidden = true)
    private Integer offset;

}