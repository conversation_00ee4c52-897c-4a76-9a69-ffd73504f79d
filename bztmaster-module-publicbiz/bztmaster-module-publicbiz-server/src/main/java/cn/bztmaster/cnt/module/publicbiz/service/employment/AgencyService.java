package cn.bztmaster.cnt.module.publicbiz.service.employment;

import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyCreateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyListReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyUpdateReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.*;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyStatisticsReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyStatisticsRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyTrendReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo.AgencyTrendRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyAuntRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyPackageRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo.AgencyReviewStatsRespVO;
import cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment.AgencyDO;

import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * 机构 Service 接口
 *
 * <AUTHOR>
 */
public interface AgencyService {

    /**
     * 分页查询机构列表
     *
     * @param reqVO 查询条件
     * @return 机构列表
     */
    PageResult<AgencyRespVO> pageAgency(AgencyPageReqVO reqVO);

    /**
     * 查询机构列表（不分页）
     *
     * @param reqVO 查询条件
     * @return 机构列表
     */
    List<AgencyRespVO> listAgency(AgencyListReqVO reqVO);

    /**
     * 根据ID获取机构详情
     *
     * @param id 机构ID
     * @return 机构详情
     */
    AgencyRespVO getAgency(Long id);

    /**
     * 更新机构审核状态
     *
     * @param reqVO 更新请求
     */
    void updateAgency(@Valid AgencyUpdateReqVO reqVO);

    /**
     * 根据ID获取机构DO
     *
     * @param id 机构ID
     * @return 机构DO
     */
    AgencyDO getAgencyDO(Long id);

    /**
     * 根据机构编号获取机构DO
     *
     * @param agencyNo 机构编号
     * @return 机构DO
     */
    AgencyDO getAgencyDOByAgencyNo(String agencyNo);

    /**
     * 新增机构
     *
     * @param reqVO 新增请求
     * @return 机构ID
     */
    Long createAgency(@Valid AgencyCreateReqVO reqVO);

    /**
     * 删除机构
     *
     * @param id 机构ID
     */
    void deleteAgency(Long id);

    // ========== 机构详情模块方法 ==========

    /**
     * 获取机构详情
     *
     * @param id 机构ID
     * @return 机构详情
     */
    AgencyDetailRespVO getAgencyDetail(Long id);

    /**
     * 获取机构阿姨列表
     *
     * @param agencyId 机构ID
     * @param page     页码
     * @param size     每页数量
     * @return 阿姨列表
     */
    PageResult<AgencyAuntRespVO> getAgencyAunts(Long agencyId, Integer page, Integer size);

    /**
     * 获取机构服务套餐列表
     *
     * @param agencyId 机构ID
     * @param page     页码
     * @param size     每页数量
     * @return 套餐列表
     */
    PageResult<AgencyPackageRespVO> getAgencyPackages(Long agencyId, Integer page, Integer size);

    /**
     * 获取机构评价统计
     *
     * @param agencyId 机构ID
     * @return 评价统计
     */
    AgencyReviewStatsRespVO getAgencyReviewStats(Long agencyId);

    /**
     * 统计机构业务数据
     *
     * @param reqVO 统计请求
     * @return 统计结果
     */
    AgencyStatisticsRespVO getAgencyStatistics(@Valid AgencyStatisticsReqVO reqVO);

    /**
     * 获取机构业务趋势数据
     *
     * @param id 机构ID
     * @return 趋势数据结果
     */
    AgencyTrendRespVO getAgencyTrendData(Long id);

    // ========== 机构注册相关方法 ==========

    /**
     * 机构注册申请
     *
     * @param request 注册请求
     * @return 注册响应
     */
    AgencyRegisterResponseVO registerAgency(AgencyRegisterRequestVO request);

    /**
     * 上传机构证照文件
     *
     * @param file 文件
     * @param fileType 文件类型
     * @param fileCategory 文件分类
     * @param agencyId 机构ID（注册时可为空）
     * @param sessionId 临时会话ID（注册时使用）
     * @return 文件上传响应
     */
    FileUploadResponseVO uploadAgencyFile(MultipartFile file, String fileType, String fileCategory, Long agencyId, String sessionId);

    /**
     * 查询机构注册申请状态
     *
     * @param applicationId 申请编号
     * @return 注册状态响应
     */
    AgencyRegisterStatusRespVO getRegisterStatus(String applicationId);

    /**
     * 检查统一社会信用代码是否已存在
     *
     * @param creditCode 统一社会信用代码
     * @return 是否存在
     */
    boolean existsByCreditCode(String creditCode);
}