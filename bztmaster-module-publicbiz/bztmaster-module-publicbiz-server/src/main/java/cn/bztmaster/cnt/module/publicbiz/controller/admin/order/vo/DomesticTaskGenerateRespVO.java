package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 家政服务任务生成响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 家政服务任务生成响应 VO")
@Data
public class DomesticTaskGenerateRespVO {

    @Schema(description = "订单ID", example = "1024")
    private Long orderId;

    @Schema(description = "订单号", example = "JZ20250814001")
    private String orderNo;

    @Schema(description = "套餐类型", example = "long-term")
    private String packageType;

    @Schema(description = "套餐名称", example = "30天每日保洁服务")
    private String packageName;

    @Schema(description = "生成的任务数量", example = "30")
    private Integer taskCount;

    @Schema(description = "生成的任务ID列表")
    private List<Long> taskIds;

    @Schema(description = "任务生成详情")
    private TaskGenerateDetail detail;

    @Schema(description = "任务生成详情")
    @Data
    public static class TaskGenerateDetail {

        @Schema(description = "服务周期（天数）", example = "30")
        private Integer servicePeriod;

        @Schema(description = "服务频次", example = "每日")
        private String serviceFrequency;

        @Schema(description = "服务次数", example = "30")
        private Integer serviceTimes;

        @Schema(description = "任务生成规则说明", example = "根据30天每日服务规则，生成30个任务")
        private String generateRule;

        @Schema(description = "任务开始日期", example = "2025-08-14")
        private String startDate;

        @Schema(description = "任务结束日期", example = "2025-09-13")
        private String endDate;
    }
}
