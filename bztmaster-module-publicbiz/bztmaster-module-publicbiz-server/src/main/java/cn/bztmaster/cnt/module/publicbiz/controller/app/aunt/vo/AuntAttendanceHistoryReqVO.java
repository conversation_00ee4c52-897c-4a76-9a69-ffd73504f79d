package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Schema(description = "用户 APP - 阿姨历史请假/调休申请查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AuntAttendanceHistoryReqVO extends PageParam {

    @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "550e8400-e29b-41d4-a716-446655440000")
    @NotNull(message = "阿姨OneID不能为空")
    private String oneId;

    @Schema(description = "申请类型筛选", example = "LEAVE", allowableValues = {"LEAVE", "ADJUST", "ALL"})
    @Pattern(regexp = "^(LEAVE|ADJUST|ALL)$", message = "申请类型只能是LEAVE、ADJUST或ALL")
    private String applyType = "ALL";

    @Schema(description = "状态筛选", example = "PENDING", allowableValues = {"PENDING", "APPROVED", "REJECTED", "ALL"})
    @Pattern(regexp = "^(PENDING|APPROVED|REJECTED|ALL)$", message = "状态只能是PENDING、APPROVED、REJECTED或ALL")
    private String status = "ALL";
}
