package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "用户 APP - 阿姨历史请假/调休申请查询 Response VO")
@Data
public class AuntAttendanceHistoryRespVO {

    @Schema(description = "申请单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "apply_20250701001")
    private String applyId;

    @Schema(description = "申请类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "LEAVE", allowableValues = {"LEAVE", "ADJUST"})
    private String applyType;

    @Schema(description = "申请类型文本", requiredMode = Schema.RequiredMode.REQUIRED, example = "请假申请")
    private String applyTypeText;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-07-01")
    private String startDate;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "09:00")
    private String startTime;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-07-03")
    private String endDate;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "18:00")
    private String endTime;

    @Schema(description = "申请时长（天）", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    private Integer duration;

    @Schema(description = "申请理由", requiredMode = Schema.RequiredMode.REQUIRED, example = "家中有急事需要处理")
    private String reason;

    @Schema(description = "申请状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "PENDING")
    private String status;

    @Schema(description = "状态文本", requiredMode = Schema.RequiredMode.REQUIRED, example = "审批中")
    private String statusText;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-07-01 10:00:00")
    private LocalDateTime createTime;

    @Schema(description = "审批时间", example = "2025-07-01 14:00:00")
    private LocalDateTime approveTime;

    @Schema(description = "审批备注", example = "同意申请")
    private String approveRemark;
}
