package cn.bztmaster.cnt.module.publicbiz.controller.app.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 雇主端登录响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端登录响应 VO")
@Data
public class EmployerAuthLoginRespVO {

    @Schema(description = "用户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long userId;

    @Schema(description = "访问令牌", requiredMode = Schema.RequiredMode.REQUIRED, example = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...")
    private String accessToken;

    @Schema(description = "刷新令牌", requiredMode = Schema.RequiredMode.REQUIRED, example = "refresh_token_123456")
    private String refreshToken;

    @Schema(description = "过期时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime expiresTime;

    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    @Schema(description = "用户头像地址", example = "https://example.com/avatar.jpg")
    private String headImageUrl;

    @Schema(description = "用户手机号", example = "13800138000")
    private String mobile;

    @Schema(description = "微信openid", example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    private String openid;

    @Schema(description = "用户角色", example = "employer")
    private String role;

}