package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "用户 APP - 阿姨打卡记录 Response VO")
@Data
public class AuntPunchRecordRespVO {

    @Schema(description = "打卡记录列表")
    private List<PunchRecord> punchRecords;

    @Schema(description = "打卡记录")
    @Data
    public static class PunchRecord {

        @Schema(description = "记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Long id;

        @Schema(description = "排班ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private String scheduleId;

        @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "550e8400-e29b-41d4-a716-446655440000")
        private String auntOneId;

        @Schema(description = "阿姨姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李阿姨")
        private String auntName;

        @Schema(description = "打卡类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer punchType; // 1-开始打卡,2-完成打卡

        @Schema(description = "打卡时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-12-01 10:00:00")
        private LocalDateTime punchTime;

        @Schema(description = "打卡位置", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京市朝阳区建国路88号")
        private String punchLocation;

        @Schema(description = "打卡纬度", requiredMode = Schema.RequiredMode.REQUIRED, example = "39.908823")
        private BigDecimal punchLatitude;

        @Schema(description = "打卡经度", requiredMode = Schema.RequiredMode.REQUIRED, example = "116.397470")
        private BigDecimal punchLongitude;

        @Schema(description = "照片数量", example = "2")
        private Integer photoCount;

        @Schema(description = "照片URL列表", example = "https://example.com/photo1.jpg,https://example.com/photo2.jpg")
        private String photoUrls;

        @Schema(description = "打卡备注", example = "按时到达服务地点")
        private String remark;

    }

} 