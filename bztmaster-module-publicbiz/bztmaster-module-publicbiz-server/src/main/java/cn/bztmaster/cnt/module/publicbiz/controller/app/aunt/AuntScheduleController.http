### 阿姨排班管理接口测试

### 1. 获取阿姨排班信息
GET {{baseUrl}}/publicbiz/aunt/schedule/info?oneId=aunt_001&startDate=2025-07-01&endDate=2025-07-07
Content-Type: application/json
Authorization: Bearer {{token}}

### 2. 更新排班状态
POST {{baseUrl}}/publicbiz/aunt/schedule/update
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "scheduleId": "schedule_001",
  "oneId": "aunt_001",
  "action": "CONFIRM",
  "remark": "确认排班"
}
