package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

@Schema(description = "用户 APP - 阿姨排班状态更新 Request VO")
@Data
public class AuntScheduleUpdateReqVO {

    @Schema(description = "排班ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "schedule_001")
    @NotNull(message = "排班ID不能为空")
    private String scheduleId;

    @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "aunt_001")
    @NotBlank(message = "阿姨OneID不能为空")
    private String oneId;

    @Schema(description = "操作", requiredMode = Schema.RequiredMode.REQUIRED, example = "CONFIRM", allowableValues = {"CONFIRM", "CANCEL"})
    @NotNull(message = "操作不能为空")
    @Pattern(regexp = "^(CONFIRM|CANCEL)$", message = "操作只能是CONFIRM或CANCEL")
    private String action;

    @Schema(description = "备注信息", example = "确认排班")
    private String remark;
}
