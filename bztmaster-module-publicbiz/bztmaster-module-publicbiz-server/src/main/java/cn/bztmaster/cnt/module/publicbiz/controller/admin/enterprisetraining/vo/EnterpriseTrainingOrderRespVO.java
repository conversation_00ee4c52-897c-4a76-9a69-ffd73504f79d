package cn.bztmaster.cnt.module.publicbiz.controller.admin.enterprisetraining.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 企业培训订单 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 企业培训订单 Response VO")
@Data
public class EnterpriseTrainingOrderRespVO {

    @Schema(description = "订单ID", example = "1")
    private Long id;

    @Schema(description = "订单号", example = "ET202406001")
    private String orderNo;

    @Schema(description = "订单类型", example = "training")
    private String orderType;

    @Schema(description = "业务线", example = "企业培训")
    private String businessLine;

    @Schema(description = "关联商机ID", example = "OPP001")
    private String opportunityId;

    @Schema(description = "关联线索ID", example = "LEAD001")
    private String leadId;

    @Schema(description = "项目名称", example = "数字化转型管理培训")
    private String projectName;

    @Schema(description = "项目描述", example = "为企业提供数字化转型管理培训服务")
    private String projectDescription;

    @Schema(description = "开始日期", example = "2024-07-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-07-15")
    private LocalDate endDate;

    @Schema(description = "订单总金额", example = "125000.00")
    private BigDecimal totalAmount;

    @Schema(description = "已支付金额", example = "0.00")
    private BigDecimal paidAmount;

    @Schema(description = "退款金额", example = "0.00")
    private BigDecimal refundAmount;

    @Schema(description = "支付状态", example = "pending")
    private String paymentStatus;

    @Schema(description = "订单状态", example = "pending_approval")
    private String orderStatus;

    @Schema(description = "负责人ID", example = "1001")
    private Long managerId;

    @Schema(description = "负责人姓名", example = "李四")
    private String managerName;

    @Schema(description = "负责人电话", example = "13800138000")
    private String managerPhone;

    @Schema(description = "合同类型", example = "electronic")
    private String contractType;

    @Schema(description = "合同文件URL", example = "https://example.com/contracts/ET-202406001.pdf")
    private String contractFileUrl;

    @Schema(description = "合同状态", example = "unsigned")
    private String contractStatus;

    @Schema(description = "备注", example = "企业培训项目")
    private String remark;

    @Schema(description = "结算状态", example = "pending")
    private String settlementStatus;

    @Schema(description = "结算时间")
    private LocalDateTime settlementTime;

    @Schema(description = "结算方式", example = "银行转账")
    private String settlementMethod;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    // ========== 企业培训订单特有字段 ==========

    @Schema(description = "企业名称", example = "ABC科技有限公司")
    private String enterpriseName;

    @Schema(description = "企业联系人", example = "张经理")
    private String enterpriseContact;

    @Schema(description = "企业联系电话", example = "010-12345678")
    private String enterprisePhone;

    @Schema(description = "企业联系邮箱", example = "<EMAIL>")
    private String enterpriseEmail;

    @Schema(description = "企业地址", example = "北京市朝阳区xxx街道xxx号")
    private String enterpriseAddress;

    @Schema(description = "培训项目名称", example = "数字化转型管理培训")
    private String trainingProject;

    @Schema(description = "培训项目描述", example = "为企业管理层提供数字化转型管理培训")
    private String trainingDescription;

    @Schema(description = "培训人数", example = "25")
    private Integer participantsCount;

    @Schema(description = "培训周期", example = "2024.07.01 - 2024.07.15")
    private String trainingDuration;

    @Schema(description = "培训地点", example = "北京")
    private String trainingLocation;

    @Schema(description = "培训类型", example = "技能培训")
    private String trainingType;

    @Schema(description = "人均培训费", example = "5000.00")
    private BigDecimal perPersonFee;

    @Schema(description = "总培训费", example = "125000.00")
    private BigDecimal totalFee;

    @Schema(description = "教材费", example = "5000.00")
    private BigDecimal materialFee;

    @Schema(description = "认证费", example = "10000.00")
    private BigDecimal certificationFee;
}
