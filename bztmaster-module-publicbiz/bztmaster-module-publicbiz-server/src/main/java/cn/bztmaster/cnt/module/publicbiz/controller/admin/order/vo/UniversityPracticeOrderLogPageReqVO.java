package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 高校实践订单日志分页 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单日志分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UniversityPracticeOrderLogPageReqVO extends PageParam {

    @Schema(description = "订单号", required = true, example = "HP202406001")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "日志类型筛选", example = "订单创建")
    private String logType;

    @Schema(description = "操作人ID", example = "1")
    private Long operatorId;

    @Schema(description = "开始日期", example = "2024-06-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startDate;

    @Schema(description = "结束日期", example = "2024-06-30 23:59:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endDate;

    @Schema(description = "创建时间范围", example = "2024-06-01 00:00:00")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime[] createTime;

    // 添加对前端传递的page和size参数的支持
    @Schema(description = "页码，从1开始", example = "1")
    private Integer page;

    @Schema(description = "每页条数，最大值为100", example = "10")
    private Integer size;

    /**
     * 重写getPageNo方法，优先使用page参数
     */
    @Override
    public Integer getPageNo() {
        if (page != null) {
            return page;
        }
        return super.getPageNo();
    }

    /**
     * 重写getPageSize方法，优先使用size参数
     */
    @Override
    public Integer getPageSize() {
        if (size != null) {
            return size;
        }
        return super.getPageSize();
    }
}





