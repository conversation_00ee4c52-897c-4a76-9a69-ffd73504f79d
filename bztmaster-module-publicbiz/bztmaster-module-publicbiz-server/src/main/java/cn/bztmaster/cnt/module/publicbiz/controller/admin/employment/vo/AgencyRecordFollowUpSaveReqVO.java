package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 机构记录跟进保存 Request VO")
@Data
public class AgencyRecordFollowUpSaveReqVO {

    @Schema(description = "记录ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "记录ID不能为空")
    private Long recordId;

    @Schema(description = "跟进描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "已与机构负责人电话沟通，了解具体情况，机构表示会积极配合整改。")
    @NotBlank(message = "跟进描述不能为空")
    private String description;

    @Schema(description = "跟进标题", example = "首次跟进 - 了解情况")
    private String title;

    @Schema(description = "操作人ID", example = "1001")
    private Long operatorId;

    @Schema(description = "操作人姓名", example = "张三")
    private String operatorName;
} 