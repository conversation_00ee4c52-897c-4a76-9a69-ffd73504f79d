package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 管理后台 - 高校实践订单删除 Request VO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "管理后台 - 高校实践订单删除 Request VO")
public class UniversityPracticeOrderDeleteReqVO {

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单编号不能为空")
    private Long id;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "HP202406001")
    @NotNull(message = "订单号不能为空")
    private String orderNo;

}
