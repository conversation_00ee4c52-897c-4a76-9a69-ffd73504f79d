package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 机构列表查询请求 VO（不分页）
 *
 * <AUTHOR>
 */
@Schema(description = "机构列表查询请求 VO（不分页）")
@Data
public class AgencyListReqVO {

    @Schema(description = "关键词（机构名称或机构ID）", example = "测试")
    private String keyword;

    @Schema(description = "合作状态", example = "cooperating")
    private String cooperationStatus;

    @Schema(description = "审核状态", example = "pending")
    private String reviewStatus;

    @Schema(description = "所属区县", example = "高新区")
    private String district;

    @Schema(description = "机构编码", example = "AG001")
    private String agencyNo;
}
