package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 服务套餐评价响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端 - 服务套餐评价响应 VO")
@Data
public class ServicePackageReviewRespVO {

    @Schema(description = "总记录数", example = "50")
    private Long total;

    @Schema(description = "总页数", example = "5")
    private Long pages;

    @Schema(description = "当前页码", example = "1")
    private Long current;

    @Schema(description = "每页数量", example = "10")
    private Long size;

    @Schema(description = "评价记录列表")
    private List<ReviewRecord> records;

    /**
     * 评价记录
     */
    @Schema(description = "评价记录")
    @Data
    public static class ReviewRecord {

        @Schema(description = "评价ID", example = "1")
        private Long id;

        @Schema(description = "订单ID", example = "1001")
        private Long orderId;

        @Schema(description = "阿姨ID", example = "aunt-001")
        private String auntId;

        @Schema(description = "评价人ID", example = "2001")
        private Long reviewerId;

        @Schema(description = "评价人姓名", example = "张女士")
        private String reviewerName;

        @Schema(description = "评价人头像", example = "https://example.com/avatar1.png")
        private String reviewerAvatar;

        @Schema(description = "评分", example = "5.0")
        private BigDecimal rating;

        @Schema(description = "评价标签", example = "[\"专业\",\"细心\",\"守时\"]")
        private List<String> reviewTags;

        @Schema(description = "评价内容", example = "服务很专业，月嫂阿姨很有经验，对宝宝照顾得很周到，推荐！")
        private String reviewContent;

        @Schema(description = "评价图片", example = "[\"https://example.com/review1.png\",\"https://example.com/review2.png\"]")
        private List<String> reviewImages;

        @Schema(description = "评价类型", example = "service")
        private String reviewType;

        @Schema(description = "是否匿名", example = "0")
        private Integer isAnonymous;

        @Schema(description = "是否推荐", example = "1")
        private Integer isRecommend;

        @Schema(description = "点赞数", example = "12")
        private Integer likeCount;

        @Schema(description = "回复内容", example = "感谢您的评价，我们会继续努力提供更好的服务！")
        private String replyContent;

        @Schema(description = "回复时间", example = "2024-01-16 10:00:00")
        private LocalDateTime replyTime;

        @Schema(description = "状态", example = "1")
        private Integer status;

        @Schema(description = "机构ID", example = "1001")
        private Long agencyId;

        @Schema(description = "服务套餐ID", example = "1")
        private Long servicePackageId;

        @Schema(description = "创建时间", example = "2024-01-15 15:30:00")
        private LocalDateTime createTime;

        // 前端展示所需字段
        @Schema(description = "评价人姓名", example = "张女士")
        private String name;

        @Schema(description = "评价人头像", example = "https://example.com/avatar1.png")
        private String avatar;

        @Schema(description = "评价日期", example = "2024-01-15")
        private String date;

        @Schema(description = "评价内容", example = "服务很专业，月嫂阿姨很有经验，对宝宝照顾得很周到，推荐！")
        private String content;

    }

}