package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;

@Schema(description = "用户 APP - 阿姨考勤统计查询 Request VO")
@Data
public class AuntAttendanceSummaryReqVO {

    @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "550e8400-e29b-41d4-a716-446655440000")
    @NotNull(message = "阿姨OneID不能为空")
    private String oneId;

    @Schema(description = "查询年份", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024")
    @NotNull(message = "查询年份不能为空")
    @Min(value = 2020, message = "年份不能小于2020")
    @Max(value = 2030, message = "年份不能大于2030")
    private Integer year;

    @Schema(description = "查询月份", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "查询月份不能为空")
    @Min(value = 1, message = "月份不能小于1")
    @Max(value = 12, message = "月份不能大于12")
    private Integer month;
}
