package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntUserSwitchReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntUserSwitchRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntUserSwitchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 阿姨用户切换 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 阿姨用户切换")
@RestController
@RequestMapping("/publicbiz/aunt/user-switch")
@Validated
public class AuntUserSwitchController {

    @Resource
    private AuntUserSwitchService auntUserSwitchService;

    @PostMapping("/check-registration")
    @Operation(summary = "检查用户是否注册了家政人员信息")
    @PermitAll
    public CommonResult<AuntUserSwitchRespVO> checkPractitionerRegistration(@Valid @RequestBody AuntUserSwitchReqVO reqVO) {
        AuntUserSwitchRespVO result = auntUserSwitchService.checkPractitionerRegistration(reqVO);
        return success(result);
    }

}
