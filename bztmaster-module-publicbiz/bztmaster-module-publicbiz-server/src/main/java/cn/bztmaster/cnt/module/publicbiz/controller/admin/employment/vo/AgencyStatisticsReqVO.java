package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 机构统计请求参数 VO
 */
@Schema(description = "机构统计请求参数")
@Data
public class AgencyStatisticsReqVO {

    @Schema(description = "机构ID", required = true, example = "1")
    @NotNull(message = "机构ID不能为空")
    private Long agencyId;

    @Schema(description = "统计数量范围类型", required = false, example = "month", 
            allowableValues = {"30", "90", "year", "all"})
    private String rangeType;
} 