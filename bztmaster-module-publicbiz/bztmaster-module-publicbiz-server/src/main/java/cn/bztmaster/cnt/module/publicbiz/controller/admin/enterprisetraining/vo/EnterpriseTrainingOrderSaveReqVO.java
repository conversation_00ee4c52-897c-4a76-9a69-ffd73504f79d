package cn.bztmaster.cnt.module.publicbiz.controller.admin.enterprisetraining.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 企业培训订单 Save Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 企业培训订单 Save Request VO")
@Data
public class EnterpriseTrainingOrderSaveReqVO {

    @Schema(description = "订单ID（更新时必填）", example = "1")
    private Long id;

    @Schema(description = "租户ID", example = "1", required = true)
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "关联商机ID", example = "OPP001")
    private String opportunityId;

    @Schema(description = "关联线索ID", example = "LEAD001")
    private String leadId;

    @Schema(description = "企业名称", example = "ABC科技有限公司", required = true)
    @NotBlank(message = "企业名称不能为空")
    @Size(max = 200, message = "企业名称长度不能超过200个字符")
    private String enterpriseName;

    @Schema(description = "企业联系人", example = "张经理")
    @Size(max = 50, message = "企业联系人长度不能超过50个字符")
    private String enterpriseContact;

    @Schema(description = "企业联系电话", example = "010-12345678")
    @Pattern(regexp = "^[0-9\\-\\+\\s\\(\\)]{7,20}$", message = "企业联系电话格式不正确")
    private String enterprisePhone;

    @Schema(description = "企业联系邮箱", example = "<EMAIL>")
    @Email(message = "企业联系邮箱格式不正确")
    @Size(max = 100, message = "企业联系邮箱长度不能超过100个字符")
    private String enterpriseEmail;

    @Schema(description = "企业地址", example = "北京市朝阳区xxx街道xxx号")
    @Size(max = 500, message = "企业地址长度不能超过500个字符")
    private String enterpriseAddress;

    @Schema(description = "培训项目名称", example = "数字化转型管理培训", required = true)
    @NotBlank(message = "培训项目名称不能为空")
    @Size(max = 200, message = "培训项目名称长度不能超过200个字符")
    private String trainingProject;

    @Schema(description = "培训项目描述", example = "为企业管理层提供数字化转型管理培训")
    private String trainingDescription;

    @Schema(description = "培训人数", example = "25", required = true)
    @NotNull(message = "培训人数不能为空")
    @Min(value = 1, message = "培训人数必须大于0")
    @Max(value = 1000, message = "培训人数不能超过1000")
    private Integer participantsCount;

    @Schema(description = "培训周期", example = "2024.07.01 - 2024.07.15", required = true)
    @NotBlank(message = "培训周期不能为空")
    @Size(max = 50, message = "培训周期长度不能超过50个字符")
    private String trainingDuration;

    @Schema(description = "培训地点", example = "北京")
    @Size(max = 500, message = "培训地点长度不能超过500个字符")
    private String trainingLocation;

    @Schema(description = "培训类型", example = "技能培训")
    @Size(max = 50, message = "培训类型长度不能超过50个字符")
    private String trainingType;

    @Schema(description = "人均培训费", example = "5000.00")
    @DecimalMin(value = "0.00", message = "人均培训费不能为负数")
    @Digits(integer = 8, fraction = 2, message = "人均培训费格式不正确")
    private BigDecimal perPersonFee;

    @Schema(description = "总培训费", example = "125000.00")
    @DecimalMin(value = "0.00", message = "总培训费不能为负数")
    @Digits(integer = 10, fraction = 2, message = "总培训费格式不正确")
    private BigDecimal totalFee;

    @Schema(description = "教材费", example = "5000.00")
    @DecimalMin(value = "0.00", message = "教材费不能为负数")
    @Digits(integer = 8, fraction = 2, message = "教材费格式不正确")
    private BigDecimal materialFee;

    @Schema(description = "认证费", example = "10000.00")
    @DecimalMin(value = "0.00", message = "认证费不能为负数")
    @Digits(integer = 8, fraction = 2, message = "认证费格式不正确")
    private BigDecimal certificationFee;

    @Schema(description = "负责人ID", example = "1001", required = true)
    @NotNull(message = "负责人ID不能为空")
    private Long managerId;

    @Schema(description = "负责人姓名", example = "李四", required = true)
    @NotBlank(message = "负责人姓名不能为空")
    @Size(max = 50, message = "负责人姓名长度不能超过50个字符")
    private String managerName;

    @Schema(description = "负责人电话", example = "13800138000")
    @Pattern(regexp = "^[0-9\\-\\+\\s\\(\\)]{7,20}$", message = "负责人电话格式不正确")
    private String managerPhone;

    @Schema(description = "备注", example = "企业培训项目")
    private String remark;

    @Schema(description = "合同文件URL", example = "https://example.com/contract.pdf")
    private String contractFileUrl;

    // ========== 收款信息字段 ==========
    @Schema(description = "支付状态（pending/paid/refunded/cancelled）", example = "pending")
    private String paymentStatus;

    @Schema(description = "收款金额（当paymentStatus为paid时必填）", example = "125000.00")
    @DecimalMin(value = "0.00", message = "收款金额不能为负数")
    private BigDecimal collectionAmount;

    @Schema(description = "收款方式（当paymentStatus为paid时必填）", example = "bank_transfer")
    private String collectionMethod;

    @Schema(description = "收款日期（当paymentStatus为paid时必填）", example = "2024-06-21")
    private LocalDate collectionDate;

    @Schema(description = "操作人（当paymentStatus为paid时必填）", example = "李四")
    private String operatorName;

    @Schema(description = "收款备注", example = "银行转账收款，已确认到账")
    private String collectionRemark;

    // ========== 支付信息字段 ==========

    @Schema(description = "支付类型", example = "cash", allowableValues = {"cash", "wechat", "alipay", "bank_transfer", "pos", "other"})
    private String paymentType;

    @Schema(description = "支付金额", example = "500.00")
    @DecimalMin(value = "0.00", message = "支付金额不能为负数")
    @Digits(integer = 10, fraction = 2, message = "支付金额格式不正确")
    private BigDecimal paymentAmount;

    @Schema(description = "支付时间", example = "2025-01-15 10:30:00")
    private String paymentTime;

    @Schema(description = "支付备注", example = "现金收款")
    private String paymentRemark;

    @Schema(description = "第三方交易号", example = "TXN123456789")
    private String transactionId;

    @Schema(description = "操作人ID", example = "1001")
    private Long operatorId;

    // ========== 项目信息字段 ==========

    @Schema(description = "开始日期", example = "2024-07-01")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2024-07-15")
    private LocalDate endDate;
}
