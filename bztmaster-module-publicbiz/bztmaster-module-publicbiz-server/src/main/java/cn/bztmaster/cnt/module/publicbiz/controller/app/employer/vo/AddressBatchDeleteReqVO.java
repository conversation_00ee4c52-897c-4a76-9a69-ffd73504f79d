package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 地址批量删除请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 地址批量删除请求 VO")
@Data
public class AddressBatchDeleteReqVO {

    @Schema(description = "地址ID数组", requiredMode = Schema.RequiredMode.REQUIRED, example = "[1, 2, 3]")
    @NotEmpty(message = "地址ID数组不能为空")
    @Size(max = 20, message = "一次最多删除20个地址")
    private List<Long> ids;
}
