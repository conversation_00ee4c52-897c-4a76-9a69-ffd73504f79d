package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 切换身份请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "切换身份请求 VO")
@Data
public class SwitchIdentityReqVO {

    @Schema(description = "微信openid", requiredMode = Schema.RequiredMode.REQUIRED, example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    @NotNull(message = "微信openid不能为空")
    private String openid;

    @Schema(description = "身份类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "EMPLOYER")
    @NotNull(message = "身份类型不能为空")
    private String identityType;

}