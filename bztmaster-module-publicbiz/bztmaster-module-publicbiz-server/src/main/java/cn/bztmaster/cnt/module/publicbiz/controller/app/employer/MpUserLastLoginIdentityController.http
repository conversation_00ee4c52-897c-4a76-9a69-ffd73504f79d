### 创建小程序用户最后登录身份记录
POST {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/create
Content-Type: application/json

{
  "userId": 1024,
  "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "unionid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "identityType": "EMPLOYER",
  "identityId": 1024,
  "identityName": "张先生",
  "lastLoginTime": "2024-01-01T12:00:00",
  "lastLoginIp": "***********",
  "deviceInfo": "iPhone 14 Pro",
  "loginStatus": 1,
  "sessionKey": "session_key_example",
  "accessToken": "access_token_example",
  "refreshToken": "refresh_token_example",
  "tokenExpireTime": "2024-01-02T12:00:00"
}

### 更新小程序用户最后登录身份记录
PUT {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/update
Content-Type: application/json

{
  "id": 1,
  "userId": 1024,
  "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "unionid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "identityType": "EMPLOYER",
  "identityId": 1024,
  "identityName": "张先生",
  "lastLoginTime": "2024-01-01T12:00:00",
  "lastLoginIp": "***********",
  "deviceInfo": "iPhone 14 Pro",
  "loginStatus": 1,
  "sessionKey": "session_key_example",
  "accessToken": "access_token_example",
  "refreshToken": "refresh_token_example",
  "tokenExpireTime": "2024-01-02T12:00:00"
}

### 删除小程序用户最后登录身份记录
DELETE {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/delete?id=1

### 获得小程序用户最后登录身份记录
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get?id=1

### 根据用户ID和openid获得小程序用户最后登录身份记录
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get-by-user-id-and-openid?userId=1024&openid=oH_Tu5EtrrF6n7u8v9w0x1y2z3

### 根据openid获得小程序用户最后登录身份记录
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get-by-openid?openid=oH_Tu5EtrrF6n7u8v9w0x1y2z3

### 根据用户ID获得小程序用户最后登录身份记录
GET {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/get-by-user-id?userId=1024

### 保存或更新小程序用户最后登录身份记录
POST {{baseUrl}}/publicbiz/app/employer/mp-user-last-login-identity/save-or-update
Content-Type: application/json

{
  "userId": 1024,
  "openid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "unionid": "oH_Tu5EtrrF6n7u8v9w0x1y2z3",
  "identityType": "EMPLOYER",
  "identityId": 1024,
  "identityName": "张先生",
  "lastLoginTime": "2024-01-01T12:00:00",
  "lastLoginIp": "***********",
  "deviceInfo": "iPhone 14 Pro",
  "loginStatus": 1,
  "sessionKey": "session_key_example",
  "accessToken": "access_token_example",
  "refreshToken": "refresh_token_example",
  "tokenExpireTime": "2024-01-02T12:00:00"
}

### 获取用户角色
POST {{baseUrl}}/publicbiz/employer/mpuser/getUserRole
Content-Type: application/json

{
  "code": "wx_login_code_123456"
} 