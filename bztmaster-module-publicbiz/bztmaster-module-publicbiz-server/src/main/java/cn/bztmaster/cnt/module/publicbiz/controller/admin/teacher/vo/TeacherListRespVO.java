package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;

@Data
@Schema(description = "师资库 - 讲师列表 Response VO")
public class TeacherListRespVO {
    @Schema(description = "讲师ID")
    private Long id;
    
    @Schema(description = "头像")
    private String avatar;
    
    @Schema(description = "姓名")
    private String name;
    
    @Schema(description = "描述")
    private String description;
    
    @Schema(description = "类型")
    private String type;
    
    @Schema(description = "业务")
    private String biz;
    
    @Schema(description = "组织")
    private String org;
    
    @Schema(description = "领域")
    private String field;
    
    @Schema(description = "电话")
    private String phone;
    
    @Schema(description = "邮箱")
    private String email;
    
    @Schema(description = "状态")
    private String status;
    
    @Schema(description = "签约状态")
    private String signStatus;
    
    @Schema(description = "签约日期")
    private Date signDate;
    
    @Schema(description = "合同类型")
    private String contractType;
}
