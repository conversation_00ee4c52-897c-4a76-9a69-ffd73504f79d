package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Schema(description = "管理后台 - 高校实践订单 Excel 导出 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UniversityPracticeOrderExportReqVO extends PageParam {

    @Schema(description = "订单号", example = "HP202406001")
    private String orderNo;

    @Schema(description = "项目名称", example = "校企合作实践项目")
    private String projectName;

    @Schema(description = "合作高校", example = "清华大学")
    private String universityName;

    @Schema(description = "合作企业", example = "阿里巴巴")
    private String enterpriseName;

    @Schema(description = "项目负责人", example = "张三")
    private String projectManager;

    @Schema(description = "订单状态", example = "draft")
    private String orderStatus;

    @Schema(description = "支付状态", example = "unpaid")
    private String paymentStatus;

    @Schema(description = "审批状态", example = "pending")
    private String approvalStatus;

    @Schema(description = "合同状态", example = "unsigned")
    private String contractStatus;

    @Schema(description = "开始日期")
    private LocalDate startDate;

    @Schema(description = "结束日期")
    private LocalDate endDate;

    @Schema(description = "关键词搜索", example = "项目")
    private String keyword;
}


