package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 机构资质文件响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构资质文件响应 VO")
@Data
public class AgencyQualificationRespVO {

    @Schema(description = "机构ID", example = "1")
    private Long agencyId;

    @Schema(description = "机构名称", example = "某某家政服务有限公司")
    private String agencyName;

    @Schema(description = "资质文件列表")
    private List<QualificationFile> qualifications;

    @Schema(description = "资质文件信息")
    @Data
    public static class QualificationFile {

        @Schema(description = "文件ID", example = "1")
        private Long id;

        @Schema(description = "文件类型", example = "business_license")
        private String fileType;

        @Schema(description = "文件类型名称", example = "营业执照")
        private String fileTypeName;

        @Schema(description = "文件名", example = "营业执照.jpg")
        private String fileName;

        @Schema(description = "文件URL", example = "https://example.com/files/business_license.jpg")
        private String fileUrl;

        @Schema(description = "文件大小（字节）", example = "1024000")
        private Long fileSize;

        @Schema(description = "文件扩展名", example = "jpg")
        private String fileExtension;

        @Schema(description = "排序", example = "1")
        private Integer sortOrder;

        @Schema(description = "状态", example = "1")
        private Integer status;

        @Schema(description = "状态名称", example = "有效")
        private String statusName;

        @Schema(description = "文件分类", example = "business_license")
        private String fileCategory;

        @Schema(description = "文件描述", example = "营业执照")
        private String fileDescription;
    }
}
