package cn.bztmaster.cnt.module.publicbiz.controller.admin.partner;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.partner.PartnerService;
import cn.bztmaster.cnt.module.publicbiz.api.partner.dto.PartnerRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.partner.dto.PartnerPageRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.partner.dto.PartnerStatRespDTO;
import cn.bztmaster.cnt.module.publicbiz.api.partner.dto.PartnerSaveReqDTO;
import cn.bztmaster.cnt.module.publicbiz.api.partner.dto.PartnerUpdateReqDTO;
import cn.bztmaster.cnt.module.publicbiz.convert.partner.PartnerConvert;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.partner.vo.PartnerPageReqVO;

@RestController
@RequestMapping("/publicbiz/partner")
@Tag(name = "资源中心-合作伙伴管理")
public class PartnerController {

    @Resource
    private PartnerService partnerService;

    @Resource
    private PartnerConvert partnerConvert;

    @GetMapping("/page")
    @Operation(summary = "合作伙伴分页列表")
    public CommonResult<PartnerPageRespDTO> page(PartnerPageReqVO reqVO) {
        PageResult<PartnerRespVO> pageResult = partnerService.getPartnerPage(reqVO);
        PartnerPageRespDTO dto = partnerConvert.convertToPageDTO(pageResult);
        return CommonResult.success(dto);
    }

    @PostMapping("/create")
    @Operation(summary = "新增合作伙伴")
    public CommonResult<Long> create(@RequestBody PartnerSaveReqDTO reqDTO) {
        PartnerSaveReqVO reqVO = partnerConvert.convertToSaveVO(reqDTO);
        Long partnerId = partnerService.createPartner(reqVO);
        return CommonResult.success(partnerId);
    }

    @PostMapping("/update")
    @Operation(summary = "编辑合作伙伴")
    public CommonResult<Boolean> update(@RequestBody PartnerUpdateReqDTO reqDTO) {
        PartnerUpdateReqVO reqVO = partnerConvert.convertToUpdateVO(reqDTO);
        partnerService.updatePartner(reqVO);
        return CommonResult.success(true);
    }

    @GetMapping("/detail")
    @Operation(summary = "合作伙伴详情")
    public CommonResult<PartnerRespDTO> detail(@RequestParam("id") Long id) {
        PartnerRespVO partner = partnerService.getPartnerDetail(id);
        if (partner == null) {
            return CommonResult.success(null);
        }
        PartnerRespDTO dto = partnerConvert.convertToDTO(partner);
        return CommonResult.success(dto);
    }

    @PostMapping("/delete")
    @Operation(summary = "逻辑删除合作伙伴")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        partnerService.deletePartner(id);
        return CommonResult.success(true);
    }

    @GetMapping("/stat")
    @Operation(summary = "合作伙伴统计卡片数据")
    public CommonResult<PartnerStatRespDTO> stat() {
        PartnerStatRespVO stat = partnerService.getPartnerStat();
        PartnerStatRespDTO dto = partnerConvert.convertToStatDTO(stat);
        return CommonResult.success(dto);
    }

    @GetMapping("/list/active")
    @Operation(summary = "获取有效状态合作伙伴列表（下拉框数据）")
    public CommonResult<PartnerActiveListRespVO> listActive(
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "type", required = false) String type,
            @RequestParam(value = "biz", required = false) String biz,
            @RequestParam(value = "keyword", required = false) String keyword) {
        PartnerActiveListRespVO respVO = partnerService.getActivePartnerList(status, type, biz, keyword);
        return CommonResult.success(respVO);
    }

    @GetMapping("/getByName")
    @Operation(summary = "根据合作伙伴名称查询合作伙伴信息")
    public CommonResult<PartnerRespDTO> getByName(@RequestParam("name") String name) {
        PartnerRespVO partner = partnerService.getPartnerByName(name);
        if (partner == null) {
            return CommonResult.success(null);
        }
        PartnerRespDTO dto = partnerConvert.convertToDTO(partner);
        return CommonResult.success(dto);
    }
}
