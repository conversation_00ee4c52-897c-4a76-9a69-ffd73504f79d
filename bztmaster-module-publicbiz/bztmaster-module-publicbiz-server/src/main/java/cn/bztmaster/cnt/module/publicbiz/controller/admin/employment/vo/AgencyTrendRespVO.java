package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 机构业务趋势数据响应 VO
 */
@Schema(description = "机构业务趋势数据响应")
@Data
public class AgencyTrendRespVO {

    @Schema(description = "订单趋势数组")
    private List<OrderTrendItem> orderTrends;

    @Schema(description = "服务分类数组")
    private List<ServiceCategoryItem> serviceCategories;

    @Schema(description = "服务质量数组")
    private List<ServiceQualityItem> serviceQualities;

    /**
     * 订单趋势项
     */
    @Schema(description = "订单趋势项")
    @Data
    public static class OrderTrendItem {
        @Schema(description = "月份", example = "2024-01")
        private String month;

        @Schema(description = "数量", example = "150")
        private Integer count;
    }

    /**
     * 服务分类项
     */
    @Schema(description = "服务分类项")
    @Data
    public static class ServiceCategoryItem {
        @Schema(description = "服务类型", example = "家政服务")
        private String serviceType;

        @Schema(description = "占比", example = "45.5")
        private BigDecimal percentage;
    }

    /**
     * 服务质量项
     */
    @Schema(description = "服务质量项")
    @Data
    public static class ServiceQualityItem {
        @Schema(description = "月份", example = "2024-01")
        private String month;

        @Schema(description = "评分", example = "4.8")
        private BigDecimal score;
    }
} 