package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Schema(description = "管理后台 - 机构记录 Response VO")
@Data
public class AgencyRecordRespVO {

    @Schema(description = "记录ID", example = "1")
    private Long id;

    @Schema(description = "记录编号", example = "REC001")
    private String recordId;

    @Schema(description = "机构ID", example = "1001")
    private Long agencyId;

    @Schema(description = "记录类型", example = "incentive")
    private String recordType;

    @Schema(description = "记录日期", example = "2024-01-15")
    private LocalDate recordDate;

    @Schema(description = "记录标题", example = "平台奖励 - 月度优秀机构")
    private String title;

    @Schema(description = "记录详细描述", example = "因其卓越的服务质量和客户满意度,被评为1月度优秀合作机构。")
    private String description;

    @Schema(description = "信用分影响", example = "10")
    private Integer creditImpact;

    @Schema(description = "金额影响", example = "50.00")
    private BigDecimal amountImpact;

    @Schema(description = "其他影响", example = "暂停接单")
    private String otherImpact;

    @Schema(description = "关联信息", example = "订单号：20240110021")
    private String relatedInfo;

    @Schema(description = "处理状态", example = "effective")
    private String status;

    @Schema(description = "跟进日期", example = "2024-01-16")
    private LocalDate followUpDate;

    @Schema(description = "跟进事项", example = "联系机构负责人了解情况")
    private String followUpItem;

    @Schema(description = "备注说明", example = "月度优秀机构奖励")
    private String remarks;

    @Schema(description = "生效时间")
    private LocalDateTime effectiveTime;

    @Schema(description = "记录人姓名", example = "系统")
    private String recorderName;

    @Schema(description = "记录人ID", example = "1")
    private Long recorderId;

    @Schema(description = "记录时间")
    private LocalDateTime recordTime;

    @Schema(description = "沟通方式", example = "call")
    private String communicationType;

    @Schema(description = "沟通标题", example = "电话沟通客户投诉")
    private String communicationTitle;

    @Schema(description = "沟通内容", example = "已与客户电话沟通，了解具体投诉内容")
    private String communicationContent;

    @Schema(description = "参与人", example = "张三,李四,王五")
    private String participants;

    @Schema(description = "创建人", example = "系统")
    private String creator;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新人", example = "系统")
    private String updater;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "附件列表")
    private List<AgencyRecordAttachmentRespVO> attachments;
} 