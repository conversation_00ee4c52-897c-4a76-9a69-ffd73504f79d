package cn.bztmaster.cnt.module.publicbiz.controller.admin.order;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageResult;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageReqVO;
import cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo.UniversityPracticeOrderLogPageRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.order.OrderLogQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 管理后台 - 订单操作日志
 * 支持查询所有类型订单的操作日志
 *
 * <AUTHOR>
 */
@Tag(name = "管理后台 - 订单操作日志")
@RestController
@RequestMapping("/publicbiz/order-log")
@Validated
public class OrderLogController {

    @Resource
    private OrderLogQueryService orderLogQueryService;

    @GetMapping("/page")
    @Operation(summary = "分页查询订单操作日志")
    @PreAuthorize("@ss.hasPermission('publicbiz:order-log:query')")
    public CommonResult<PageResult<UniversityPracticeOrderLogPageRespVO>> getOrderLogPage(@Valid UniversityPracticeOrderLogPageReqVO reqVO) {
        PageResult<UniversityPracticeOrderLogPageRespVO> pageResult = orderLogQueryService.getOrderLogPage(reqVO);
        return success(pageResult);
    }

    @GetMapping("/list-by-order-no")
    @Operation(summary = "通过订单号查询操作日志")
    @Parameter(name = "orderNo", description = "订单号", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:order-log:query')")
    public CommonResult<List<UniversityPracticeOrderLogPageRespVO>> getOrderLogListByOrderNo(@RequestParam("orderNo") String orderNo) {
        List<UniversityPracticeOrderLogPageRespVO> logList = orderLogQueryService.getOrderLogListByOrderNo(orderNo);
        return success(logList);
    }

    @GetMapping("/list-by-order-no-and-type")
    @Operation(summary = "通过订单号和日志类型查询操作日志")
    @Parameter(name = "orderNo", description = "订单号", required = true)
    @Parameter(name = "logType", description = "日志类型", required = true)
    @PreAuthorize("@ss.hasPermission('publicbiz:order-log:query')")
    public CommonResult<List<UniversityPracticeOrderLogPageRespVO>> getOrderLogListByOrderNoAndType(
            @RequestParam("orderNo") String orderNo,
            @RequestParam("logType") String logType) {
        List<UniversityPracticeOrderLogPageRespVO> logList = orderLogQueryService.getOrderLogListByOrderNoAndType(orderNo, logType);
        return success(logList);
    }

    @GetMapping("/list-by-order-type")
    @Operation(summary = "通过订单类型查询操作日志")
    @Parameter(name = "orderType", description = "订单类型", required = true)
    @Parameter(name = "limit", description = "限制条数", required = false)
    @PreAuthorize("@ss.hasPermission('publicbiz:order-log:query')")
    public CommonResult<List<UniversityPracticeOrderLogPageRespVO>> getOrderLogListByOrderType(
            @RequestParam("orderType") String orderType,
            @RequestParam(value = "limit", required = false) Integer limit) {
        List<UniversityPracticeOrderLogPageRespVO> logList = orderLogQueryService.getOrderLogListByOrderType(orderType, limit);
        return success(logList);
    }

    @GetMapping("/list-by-operator")
    @Operation(summary = "通过操作人查询操作日志")
    @Parameter(name = "operatorId", description = "操作人ID", required = true)
    @Parameter(name = "limit", description = "限制条数", required = false)
    @PreAuthorize("@ss.hasPermission('publicbiz:order-log:query')")
    public CommonResult<List<UniversityPracticeOrderLogPageRespVO>> getOrderLogListByOperator(
            @RequestParam("operatorId") Long operatorId,
            @RequestParam(value = "limit", required = false) Integer limit) {
        List<UniversityPracticeOrderLogPageRespVO> logList = orderLogQueryService.getOrderLogListByOperator(operatorId, limit);
        return success(logList);
    }
}



