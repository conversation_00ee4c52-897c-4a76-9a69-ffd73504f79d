package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 机构注册请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构注册请求 VO")
@Data
public class AgencyRegisterRequestVO {

    @Schema(description = "机构信息", required = true)
    @Valid
    @NotNull(message = "机构信息不能为空")
    private AgencyInfoVO agencyInfo;

    @Schema(description = "证照文件路径列表", required = true)
    @Valid
    @NotNull(message = "证照文件不能为空")
    private List<DocumentFileVO> documentFiles;

    @Schema(description = "临时会话ID（用于关联临时上传的文件）", example = "temp_session_123456")
    private String sessionId;

    @Schema(description = "机构信息")
    @Data
    public static class AgencyInfoVO {

        @Schema(description = "机构名称", required = true, example = "XX家政服务有限公司")
        @NotBlank(message = "机构名称不能为空")
        @Size(max = 200, message = "机构名称长度不能超过200个字符")
        private String agencyName;

        @Schema(description = "统一社会信用代码", required = true, example = "91110000123456789X")
        @NotBlank(message = "统一社会信用代码不能为空")
        @Pattern(regexp = "^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$", 
                message = "统一社会信用代码格式不正确")
        private String creditCode;

        @Schema(description = "机构类型", required = true, example = "有限责任公司")
        @NotBlank(message = "机构类型不能为空")
        private String agencyType;

        @Schema(description = "成立时间", required = true, example = "2020-01-01")
        @NotBlank(message = "成立时间不能为空")
        private String establishDate;

        @Schema(description = "注册资本（万元）", required = true, example = "100.00")
        @NotNull(message = "注册资本不能为空")
        private BigDecimal registeredCapital;

        @Schema(description = "经营范围", required = true, example = "家政服务、保洁服务等")
        @NotBlank(message = "经营范围不能为空")
        @Size(max = 500, message = "经营范围长度不能超过500个字符")
        private String businessScope;

        @Schema(description = "联系人姓名", required = true, example = "张经理")
        @NotBlank(message = "联系人姓名不能为空")
        @Size(max = 50, message = "联系人姓名长度不能超过50个字符")
        private String contactName;

        @Schema(description = "联系电话", required = true, example = "***********")
        @NotBlank(message = "联系电话不能为空")
        @Pattern(regexp = "^1[3-9]\\d{9}$", message = "联系电话格式不正确")
        private String contactPhone;

        @Schema(description = "电子邮箱", example = "<EMAIL>")
        @Pattern(regexp = "^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$", message = "邮箱格式不正确")
        private String email;

        @Schema(description = "联系地址", required = true, example = "北京市朝阳区xxx街道xxx号")
        @NotBlank(message = "联系地址不能为空")
        @Size(max = 500, message = "联系地址长度不能超过500个字符")
        private String address;

        @Schema(description = "省份code", example = "110000")
        private String provinceCode;

        @Schema(description = "省份", example = "北京市")
        private String province;

        @Schema(description = "城市code", example = "110100")
        private String cityCode;

        @Schema(description = "城市", example = "北京市")
        private String city;

        @Schema(description = "区县code", example = "110105")
        private String districtCode;

        @Schema(description = "区县", example = "朝阳区")
        private String district;

        @Schema(description = "街道code", example = "110105001")
        private String streetCode;

        @Schema(description = "街道", example = "xxx街道")
        private String street;

        @Schema(description = "详细地址", example = "xxx号")
        @Size(max = 200, message = "详细地址长度不能超过200个字符")
        private String detailAddress;

        @Schema(description = "经度", example = "116.1234567")
        private BigDecimal longitude;

        @Schema(description = "纬度", example = "39.1234567")
        private BigDecimal latitude;

        @Schema(description = "开票名称", required = true, example = "XX家政服务有限公司")
        @NotBlank(message = "开票名称不能为空")
        @Size(max = 200, message = "开票名称长度不能超过200个字符")
        private String invoiceName;

        @Schema(description = "纳税人识别号", required = true, example = "91110000123456789X")
        @NotBlank(message = "纳税人识别号不能为空")
        @Size(max = 50, message = "纳税人识别号长度不能超过50个字符")
        private String taxNumber;

        @Schema(description = "开户银行", example = "中国银行")
        private String bankName;

        @Schema(description = "银行账号", example = "1234567890123456789")
        private String bankAccount;

        @Schema(description = "服务资质等级", example = "A级")
        private String qualificationLevel;

        @Schema(description = "员工人数", example = "50")
        private Integer employeeCount;

        @Schema(description = "服务区域", example = "北京市朝阳区、海淀区")
        private String serviceArea;
    }

    @Schema(description = "证照文件信息")
    @Data
    public static class DocumentFileVO {

        @Schema(description = "文件路径", required = true, example = "https://example.com/business_license.jpg")
        @NotBlank(message = "文件路径不能为空")
        private String fileUrl;

        @Schema(description = "文件类型", required = true, example = "business_license")
        @NotBlank(message = "文件类型不能为空")
        private String fileType;

        @Schema(description = "文件分类", required = true, example = "business_license")
        @NotBlank(message = "文件分类不能为空")
        private String fileCategory;

        @Schema(description = "文件描述", example = "营业执照")
        private String fileDescription;

        @Schema(description = "排序", example = "0")
        private Integer sortOrder = 0;
    }
}
