package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 雇主创建订单响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主创建订单响应 VO")
@Data
public class EmployerOrderCreateRespVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "202501150001")
    private String orderId;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "SP202501150001")
    private String orderNo;

    @Schema(description = "订单状态：pending_payment(待支付)、paid(已支付)、cancelled(已取消)", requiredMode = Schema.RequiredMode.REQUIRED, example = "pending_payment")
    private String status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01-15 10:30:00")
    private String createTime;

    @Schema(description = "支付链接（可选）", example = "https://pay.example.com/order/123456")
    private String payUrl;
}
