package cn.bztmaster.cnt.module.publicbiz.framework.common.exception;

import cn.bztmaster.cnt.framework.common.exception.ErrorCode;

/**
 * 公共业务模块错误码常量
 *
 * <AUTHOR>
 */
public interface ErrorCodeConstants {

    // 订单相关错误码
    ErrorCode ORDER_NOT_EXISTS = new ErrorCode(1001001, "订单不存在");
    ErrorCode ORDER_STATUS_NOT_ALLOW_COLLECTION = new ErrorCode(1001002, "订单状态不允许收款");
    ErrorCode COLLECTION_AMOUNT_EXCEEDS_TOTAL = new ErrorCode(1001003, "收款金额超过订单总金额");
    ErrorCode PAYMENT_RECORD_NOT_EXISTS = new ErrorCode(1001004, "支付记录不存在");
    ErrorCode ORDER_CONTRACT_INFO_FAILED = new ErrorCode(1001005, "获取合同信息失败");
    ErrorCode ORDER_COLLECTION_FAILED = new ErrorCode(1001006, "确认收款失败");
    ErrorCode ORDER_UPDATE_COLLECTION_FAILED = new ErrorCode(1001007, "更新收款信息失败");
    ErrorCode ORDER_UPDATE_FAILED = new ErrorCode(1001008, "订单更新失败");
    
    // 支付记录相关错误码
    ErrorCode PAYMENT_RECORD_CREATE_FAILED = new ErrorCode(1001009, "支付记录创建失败");
    
    // 订单创建相关错误码
    ErrorCode ORDER_CREATE_FAILED = new ErrorCode(1001010, "订单创建失败");
    ErrorCode ORDER_DETAIL_CREATE_FAILED = new ErrorCode(1001011, "订单详情创建失败");
    
    // 参数验证相关错误码
    ErrorCode PROJECT_NAME_EMPTY = new ErrorCode(1001012, "项目名称不能为空");
    ErrorCode UNIVERSITY_NAME_EMPTY = new ErrorCode(1001013, "高校名称不能为空");
    ErrorCode ENTERPRISE_NAME_EMPTY = new ErrorCode(1001014, "企业名称不能为空");
    ErrorCode START_DATE_EMPTY = new ErrorCode(1001015, "开始日期不能为空");
    ErrorCode END_DATE_EMPTY = new ErrorCode(1001016, "结束日期不能为空");
    ErrorCode DATE_RANGE_INVALID = new ErrorCode(1001017, "日期范围无效");
    ErrorCode TOTAL_AMOUNT_INVALID = new ErrorCode(1001018, "总金额无效");
    ErrorCode MANAGER_ID_INVALID = new ErrorCode(1001019, "负责人ID无效");
    ErrorCode MANAGER_NAME_EMPTY = new ErrorCode(1001020, "负责人姓名不能为空");
    ErrorCode COLLECTION_AMOUNT_INVALID = new ErrorCode(1001021, "收款金额无效");
    ErrorCode COLLECTION_METHOD_EMPTY = new ErrorCode(1001022, "收款方式不能为空");
    ErrorCode COLLECTION_DATE_EMPTY = new ErrorCode(1001023, "收款日期不能为空");
    ErrorCode OPERATOR_NAME_EMPTY = new ErrorCode(1001024, "操作人姓名不能为空");
    
    // 审批相关错误码
    ErrorCode ORDER_STATUS_NOT_ALLOW_APPROVAL = new ErrorCode(1001025, "订单状态不允许审批");

}
