package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Schema(description = "用户 APP - 阿姨订单确认 Request VO")
@Data
public class AuntOrderConfirmReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "fa2182d0-768a-11f0-ae0c-00163e1f6ba5")
    @NotNull(message = "阿姨OneID不能为空")
    private String auntOneId;

} 