package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 高校实践订单审批请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单审批请求")
@Data
public class UniversityPracticeOrderApprovalReqVO {

    @Schema(description = "订单ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    @Schema(description = "订单号", requiredMode = Schema.RequiredMode.REQUIRED, example = "HP202406001")
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(description = "审批类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "order_approval")
    @NotBlank(message = "审批类型不能为空")
    private String approvalType;

    @Schema(description = "优先级", example = "normal")
    private String priority;

    @Schema(description = "审批人ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "审批人ID列表不能为空")
    private List<Long> approverIds;

    @Schema(description = "审批说明", example = "请审批该高校实践订单")
    private String comments;

}
