package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 阿姨用户切换请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 阿姨用户切换请求 VO")
@Data
public class AuntUserSwitchReqVO {

    @Schema(description = "微信用户openId", example = "o1234567890abcdef")
    private String openId;

    @Schema(description = "手机号", example = "13800138000")
    private String mobile;

}
