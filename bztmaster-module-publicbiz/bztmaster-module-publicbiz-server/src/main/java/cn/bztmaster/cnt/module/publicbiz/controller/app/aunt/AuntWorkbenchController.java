package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.AuntWorkbenchInfoRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntWorkbenchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 阿姨工作台")
@RestController
@RequestMapping("/publicbiz/aunt/workbench")
@Validated
public class AuntWorkbenchController {

    @Resource
    private AuntWorkbenchService auntWorkbenchService;

    @GetMapping("/info")
    @Operation(summary = "获得阿姨工作台信息")
    @PermitAll
    public CommonResult<AuntWorkbenchInfoRespVO> getWorkbenchInfo(
            @RequestParam("oneId") String oneId) {
        
        // 验证阿姨信息是否存在
        if (oneId == null || oneId.trim().isEmpty()) {
            return CommonResult.error(400, "阿姨OneID不能为空");
        }

        // 获取工作台信息
        AuntWorkbenchInfoRespVO workbenchInfo = auntWorkbenchService.getWorkbenchInfo(oneId);
        
        if (workbenchInfo == null) {
            return CommonResult.error(404, "阿姨信息不存在");
        }
        
        return success(workbenchInfo);
    }

} 