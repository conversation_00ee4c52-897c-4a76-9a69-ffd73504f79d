package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Min;

@Schema(description = "用户 APP - 阿姨请假/调休申请 Request VO")
@Data
public class AuntAttendanceApplyReqVO {

    @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "550e8400-e29b-41d4-a716-446655440000")
    @NotNull(message = "阿姨OneID不能为空")
    private String oneId;

    @Schema(description = "申请类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "LEAVE", allowableValues = {"LEAVE", "ADJUST"})
    @NotNull(message = "申请类型不能为空")
    @Pattern(regexp = "^(LEAVE|ADJUST)$", message = "申请类型只能是LEAVE或ADJUST")
    private String applyType;

    @Schema(description = "开始日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-07-01")
    @NotNull(message = "开始日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "开始日期格式不正确，应为yyyy-MM-dd")
    private String startDate;

    @Schema(description = "开始时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "09:00")
    @NotNull(message = "开始时间不能为空")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "开始时间格式不正确，应为HH:mm")
    private String startTime;

    @Schema(description = "结束日期", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-07-03")
    @NotNull(message = "结束日期不能为空")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "结束日期格式不正确，应为yyyy-MM-dd")
    private String endDate;

    @Schema(description = "结束时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "18:00")
    @NotNull(message = "结束时间不能为空")
    @Pattern(regexp = "^([01]?[0-9]|2[0-3]):[0-5][0-9]$", message = "结束时间格式不正确，应为HH:mm")
    private String endTime;

    @Schema(description = "申请事由", requiredMode = Schema.RequiredMode.REQUIRED, example = "家中有急事需要处理")
    @NotBlank(message = "申请事由不能为空")
    private String reason;

    @Schema(description = "申请时长（天）", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
    @NotNull(message = "申请时长不能为空")
    @Min(value = 1, message = "申请时长不能小于1天")
    private Integer duration;
}
