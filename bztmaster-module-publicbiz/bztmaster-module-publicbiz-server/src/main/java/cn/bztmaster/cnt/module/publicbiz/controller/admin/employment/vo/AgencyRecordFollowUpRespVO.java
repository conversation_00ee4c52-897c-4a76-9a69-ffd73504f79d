package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 机构记录跟进 Response VO")
@Data
public class AgencyRecordFollowUpRespVO {

    @Schema(description = "跟进记录ID", example = "1")
    private Long id;

    @Schema(description = "跟进标题", example = "首次跟进 - 了解情况")
    private String title;

    @Schema(description = "跟进描述", example = "已与机构负责人电话沟通，了解具体情况，机构表示会积极配合整改。")
    private String description;

    @Schema(description = "跟进日期", example = "2024-01-16")
    private LocalDate followUpDate;

    @Schema(description = "操作人ID", example = "1001")
    private Long operatorId;

    @Schema(description = "操作人姓名", example = "张三")
    private String operatorName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
} 