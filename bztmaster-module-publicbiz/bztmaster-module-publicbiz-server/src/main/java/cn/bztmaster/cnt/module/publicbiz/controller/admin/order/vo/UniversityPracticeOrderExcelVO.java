package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 高校实践订单 Excel 导出 VO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class UniversityPracticeOrderExcelVO {

    @ExcelProperty("订单号")
    private String orderNo;

    @ExcelProperty("项目名称")
    private String projectName;

    @ExcelProperty("合作高校")
    private String universityName;

    @ExcelProperty("合作企业")
    private String enterpriseName;

    @ExcelProperty("项目负责人")
    private String projectManager;

    @ExcelProperty("项目开始日期")
    private LocalDate startDate;

    @ExcelProperty("项目结束日期")
    private LocalDate endDate;

    @ExcelProperty("项目周期")
    private String projectDuration;

    @ExcelProperty("参与学生人数")
    private Integer studentCount;

    @ExcelProperty("订单总金额")
    private BigDecimal totalAmount;

    @ExcelProperty("订单状态")
    private String orderStatus;

    @ExcelProperty("支付状态")
    private String paymentStatus;

    @ExcelProperty("审批状态")
    private String approvalStatus;

    @ExcelProperty("合同状态")
    private String contractStatus;

    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @ExcelProperty("备注")
    private String remark;
}

