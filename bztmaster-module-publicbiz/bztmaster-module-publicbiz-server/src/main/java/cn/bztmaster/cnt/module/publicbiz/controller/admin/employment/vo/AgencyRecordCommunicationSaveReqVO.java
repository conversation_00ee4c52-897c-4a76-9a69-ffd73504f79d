package cn.bztmaster.cnt.module.publicbiz.controller.admin.employment.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Schema(description = "管理后台 - 机构记录沟通日志保存 Request VO")
@Data
public class AgencyRecordCommunicationSaveReqVO {

    @Schema(description = "沟通方式：call-通话记录，message-消息记录，email-邮件记录", requiredMode = Schema.RequiredMode.REQUIRED, example = "call")
    @NotBlank(message = "沟通方式不能为空")
    private String communicationType;

    @Schema(description = "沟通标题", requiredMode = Schema.RequiredMode.REQUIRED, example = "客户投诉电话沟通")
    @NotBlank(message = "沟通标题不能为空")
    private String communicationTitle;

    @Schema(description = "沟通内容", requiredMode = Schema.RequiredMode.REQUIRED, example = "客户致电投诉阿姨服务态度问题，已详细记录客户反馈，承诺24小时内给出处理方案。")
    @NotBlank(message = "沟通内容不能为空")
    private String communicationContent;

    @Schema(description = "参与人，多个参与人用逗号分隔", example = "张三,李四")
    private String participants;

    @Schema(description = "机构ID", example = "1001")
    private Long agencyId;

    @Schema(description = "跟进日期", example = "2024-01-17")
    private LocalDate followUpDate;

    @Schema(description = "跟进事项", example = "联系机构负责人了解情况")
    private String followUpItem;

    @Schema(description = "附件数组")
    private List<AgencyRecordAttachmentSaveReqVO> attachments;
} 