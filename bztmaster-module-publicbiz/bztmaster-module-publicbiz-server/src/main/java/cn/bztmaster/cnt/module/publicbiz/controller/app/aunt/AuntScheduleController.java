package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.security.core.util.SecurityFrameworkUtils;
import cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo.*;
import cn.bztmaster.cnt.module.publicbiz.service.aunt.AuntScheduleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

@Tag(name = "用户 APP - 阿姨排班管理")
@RestController
@RequestMapping("/publicbiz/aunt/schedule")
@Validated
public class AuntScheduleController {

    @Resource
    private AuntScheduleService auntScheduleService;

    @GetMapping("/info")
    @Operation(summary = "获得阿姨排班信息")
    @PermitAll
    public CommonResult<AuntScheduleInfoRespVO> getScheduleInfo(@Valid AuntScheduleInfoReqVO reqVO) {
        // 验证阿姨信息是否存在
        if (reqVO.getOneId() == null || reqVO.getOneId().trim().isEmpty()) {
            return CommonResult.error(400, "阿姨OneID不能为空");
        }
        
        // 获取排班信息
        AuntScheduleInfoRespVO result = auntScheduleService.getScheduleInfo(reqVO);
        
        if (result == null) {
            return CommonResult.error(404, "阿姨信息不存在");
        }
        
        return success(result);
    }

    @PostMapping("/update")
    @Operation(summary = "更新排班状态")
    @PermitAll
    public CommonResult<Boolean> updateScheduleStatus(@Valid @RequestBody AuntScheduleUpdateReqVO reqVO) {
        // 验证阿姨信息是否存在
        if (reqVO.getOneId() == null || reqVO.getOneId().trim().isEmpty()) {
            return CommonResult.error(400, "阿姨OneID不能为空");
        }
        
        // 更新排班状态
        Boolean result = auntScheduleService.updateScheduleStatus(reqVO);
        
        if (result == null) {
            return CommonResult.error(404, "阿姨信息不存在");
        }
        
        return success(result);
    }
}
