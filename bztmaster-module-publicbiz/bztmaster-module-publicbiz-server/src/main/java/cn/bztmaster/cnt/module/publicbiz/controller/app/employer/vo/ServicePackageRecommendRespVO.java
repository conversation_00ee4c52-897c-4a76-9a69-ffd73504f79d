package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 服务套餐推荐响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端 - 服务套餐推荐响应 VO")
@Data
public class ServicePackageRecommendRespVO {

    @Schema(description = "总记录数", example = "20")
    private Long total;

    @Schema(description = "总页数", example = "2")
    private Long pages;

    @Schema(description = "当前页码", example = "1")
    private Long current;

    @Schema(description = "每页数量", example = "10")
    private Long size;

    @Schema(description = "推荐套餐记录列表")
    private List<RecommendRecord> records;

    /**
     * 推荐套餐记录
     */
    @Schema(description = "推荐套餐记录")
    @Data
    public static class RecommendRecord {

        @Schema(description = "套餐ID", example = "2")
        private Long id;

        @Schema(description = "套餐名称", example = "金牌月嫂服务")
        private String name;

        @Schema(description = "服务分类", example = "月嫂服务")
        private String category;

        @Schema(description = "套餐主图URL", example = "https://example.com/package2.png")
        private String thumbnail;

        @Schema(description = "套餐价格", example = "8800.00")
        private BigDecimal price;

        @Schema(description = "原价", example = "9800.00")
        private BigDecimal originalPrice;

        @Schema(description = "价格单位", example = "月")
        private String unit;

        @Schema(description = "服务时长", example = "26天")
        private String serviceDuration;

        @Schema(description = "套餐类型", example = "long-term")
        private String packageType;

        @Schema(description = "状态", example = "active")
        private String status;

        @Schema(description = "审核状态", example = "approved")
        private String auditStatus;

        @Schema(description = "机构ID", example = "1001")
        private Long agencyId;

        @Schema(description = "机构名称", example = "金牌家政服务有限公司")
        private String agencyName;

        @Schema(description = "创建时间", example = "2024-01-01 10:00:00")
        private LocalDateTime createTime;

        // 前端展示所需字段
        @Schema(description = "标题", example = "金牌月嫂服务")
        private String title;

        @Schema(description = "价格", example = "8,800")
        private String priceDisplay;

        @Schema(description = "图片URL", example = "https://example.com/package2.png")
        private String image;

    }

}