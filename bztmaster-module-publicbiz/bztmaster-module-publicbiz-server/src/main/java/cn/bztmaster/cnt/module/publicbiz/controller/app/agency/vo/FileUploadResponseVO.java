package cn.bztmaster.cnt.module.publicbiz.controller.app.agency.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 文件上传响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "文件上传响应 VO")
@Data
public class FileUploadResponseVO {

    @Schema(description = "文件URL", example = "https://example.com/uploads/business_license_123.jpg")
    private String fileUrl;

    @Schema(description = "文件名", example = "business_license_123.jpg")
    private String fileName;

    @Schema(description = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    @Schema(description = "文件ID", example = "456")
    private Long fileId;
}
