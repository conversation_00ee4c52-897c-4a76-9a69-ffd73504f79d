package cn.bztmaster.cnt.module.publicbiz.controller.app.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 雇主端微信小程序一键授权登录请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "雇主端微信小程序一键授权登录请求 VO")
@Data
public class EmployerAuthWeixinMiniAppLoginReqVO {

    @Schema(description = "微信登录临时code，小程序通过 wx.login 方法获得", requiredMode = Schema.RequiredMode.REQUIRED, example = "001frTkl21JUf94VGxol2hSlff1frTkR")
    @NotEmpty(message = "微信登录临时code不能为空")
    private String code;

    @Schema(description = "手机号授权code，小程序通过 wx.getPhoneNumber 方法获得", example = "phone_code_123456")
    private String phoneCode;

    @Schema(description = "用户昵称", example = "张三")
    private String nickname;

    @Schema(description = "用户头像地址", example = "https://example.com/avatar.jpg")
    private String headImageUrl;

    @Schema(description = "用户手机号", example = "13800138000")
    private String mobile;

}