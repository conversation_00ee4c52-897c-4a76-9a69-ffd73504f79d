package cn.bztmaster.cnt.module.publicbiz.framework.security.config;

import cn.bztmaster.cnt.framework.security.config.AuthorizeRequestsCustomizer;
import cn.bztmaster.cnt.module.system.enums.ApiConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AuthorizeHttpRequestsConfigurer;

/**
 * Publicbiz 模块的 Security 配置
 */
@Configuration("publicbizSecurityConfiguration")
public class SecurityConfiguration {

    @Bean("publicbizAuthorizeRequestsCustomizer")
    public AuthorizeRequestsCustomizer authorizeRequestsCustomizer() {
        return new AuthorizeRequestsCustomizer() {
            @Override
            public void customize(
                    AuthorizeHttpRequestsConfigurer<HttpSecurity>.AuthorizationManagerRequestMatcherRegistry registry) {
                // Swagger 接口文档
                registry.requestMatchers("/v3/api-docs/**").permitAll()
                        .requestMatchers("/webjars/**").permitAll()
                        .requestMatchers("/swagger-ui").permitAll()
                        .requestMatchers("/swagger-ui/**").permitAll();
                // Spring Boot Actuator 的安全配置
                registry.requestMatchers("/actuator").permitAll()
                        .requestMatchers("/actuator/**").permitAll();
                // Druid 监控
                registry.requestMatchers("/druid/**").permitAll();
                // RPC 服务的安全配置（如有需要可补充）
                registry.requestMatchers(ApiConstants.PREFIX + "/**").permitAll();
                // 小程序接口 - 所有 publicbiz 相关接口
                registry.requestMatchers("/publicbiz/**").permitAll();
            }
        };
    }
}