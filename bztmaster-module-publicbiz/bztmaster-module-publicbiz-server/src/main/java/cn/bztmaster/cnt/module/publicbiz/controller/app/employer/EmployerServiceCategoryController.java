package cn.bztmaster.cnt.module.publicbiz.controller.app.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.ServiceCategoryListRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.EmployerServiceCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 雇主端服务分类 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 服务分类")
@RestController
@RequestMapping("/publicbiz/employer/service-category")
@Validated
@Slf4j
public class EmployerServiceCategoryController {

    @Resource
    private EmployerServiceCategoryService serviceCategoryService;

    @GetMapping("/list")
    @Operation(summary = "获取服务分类列表")
    @PermitAll
    public CommonResult<ServiceCategoryListRespVO> getServiceCategoryList(
            @Parameter(description = "父分类编号，0表示获取顶级分类", example = "0") @RequestParam(value = "parentId", required = false, defaultValue = "0") Long parentId,
            @Parameter(description = "页码，从1开始", example = "1") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页数量，最大50", example = "10") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @Parameter(description = "状态筛选：1-启用，0-禁用，不传则查询所有", example = "1") @RequestParam(value = "status", required = false) Integer status) {

        // 参数验证
        if (parentId == null) {
            parentId = 0L;
        }

        if (status != null && status != 0 && status != 1) {
            return CommonResult.error(400, "状态值只能是0或1");
        }

        ServiceCategoryListRespVO result = serviceCategoryService.getServiceCategoryList(parentId, page, pageSize,
                status);
        return success(result);
    }

    @GetMapping("/tree")
    @Operation(summary = "获取服务分类树形结构")
    @PermitAll
    public CommonResult<ServiceCategoryListRespVO> getServiceCategoryTree(
            @Parameter(description = "状态筛选：1-启用，0-禁用，不传则查询所有", example = "1") @RequestParam(value = "status", required = false) Integer status) {

        // 参数验证
        if (status != null && status != 0 && status != 1) {
            return CommonResult.error(400, "状态值只能是0或1");
        }

        ServiceCategoryListRespVO result = serviceCategoryService.getServiceCategoryTree(status);
        return success(result);
    }

}