package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 高校实践订单保存 Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单保存 Request VO")
@Data
public class UniversityPracticeOrderSaveReqVO {

    @Schema(description = "订单ID，更新时必填", example = "1")
    private Long id;

    @Schema(description = "订单号，更新时必填", example = "HP202406001")
    private String orderNo;

    @Schema(description = "关联线索ID", example = "LEAD001")
    private String leadId;

    @Schema(description = "关联商机ID", example = "OPP001")
    private String opportunityId;

    @Schema(description = "项目名称", example = "2024年暑期社会实践项目")
    @NotBlank(message = "项目名称不能为空")
    private String projectName;

    @Schema(description = "合作高校名称", example = "XX大学经济管理学院")
    @NotBlank(message = "合作高校名称不能为空")
    private String universityName;

    @Schema(description = "高校联系人", example = "李老师")
    private String universityContact;

    @Schema(description = "高校联系电话", example = "010-12345678")
    private String universityPhone;

    @Schema(description = "高校联系邮箱", example = "<EMAIL>")
    private String universityEmail;

    @Schema(description = "合作企业名称", example = "ABC科技有限公司")
    @NotBlank(message = "合作企业名称不能为空")
    private String enterpriseName;

    @Schema(description = "企业联系人", example = "王经理")
    private String enterpriseContact;

    @Schema(description = "企业联系电话", example = "010-87654321")
    private String enterprisePhone;

    @Schema(description = "企业联系邮箱", example = "<EMAIL>")
    private String enterpriseEmail;

    @Schema(description = "开始日期", example = "2024-07-01")
    @NotNull(message = "开始日期不能为空")
    private LocalDate startDate;

    @Schema(description = "结束日期", example = "2028-08-17")
    @NotNull(message = "结束日期不能为空")
    private LocalDate endDate;

    @Schema(description = "参与学生人数", example = "50")
    private Integer studentCount;

    @Schema(description = "实践时长", example = "2个月")
    private String practiceDuration;

    @Schema(description = "实践地点", example = "北京市朝阳区")
    private String practiceLocation;

    @Schema(description = "服务费", example = "500000.00")
    @DecimalMin(value = "0.00", message = "服务费不能为负数")
    private BigDecimal serviceFee;

    @Schema(description = "管理费", example = "80000.00")
    @DecimalMin(value = "0.00", message = "管理费不能为负数")
    private BigDecimal managementFee;

    @Schema(description = "其他费用", example = "0.00")
    @DecimalMin(value = "0.00", message = "其他费用不能为负数")
    private BigDecimal otherFee;

    @Schema(description = "订单总金额", example = "580000.00")
    @NotNull(message = "订单总金额不能为空")
    @DecimalMin(value = "0.01", message = "订单总金额必须大于0")
    private BigDecimal totalAmount;

    @Schema(description = "负责人ID", example = "1001")
    @NotNull(message = "负责人ID不能为空")
    private Long managerId;

    @Schema(description = "负责人姓名", example = "张三")
    @NotBlank(message = "负责人姓名不能为空")
    private String managerName;

    @Schema(description = "负责人电话", example = "13800138000")
    private String managerPhone;

    @Schema(description = "项目描述", example = "为XX大学经济管理学院学生提供暑期社会实践机会")
    private String projectDescription;

    @Schema(description = "合同类型", example = "electronic")
    private String contractType;

    @Schema(description = "合同文件URL", example = "https://example.com/contracts/contract123.pdf")
    private String contractFileUrl;

    @Schema(description = "订单备注", example = "高校实践项目订单")
    private String remark;

    // 收款信息字段
    @Schema(description = "支付状态（pending/paid/refunded/cancelled）", example = "pending")
    private String paymentStatus;

    @Schema(description = "收款金额（当paymentStatus为paid时必填）", example = "580000.00")
    @DecimalMin(value = "0.00", message = "收款金额不能为负数")
    private BigDecimal collectionAmount;

    @Schema(description = "收款方式（当paymentStatus为paid时必填）", example = "bank_transfer")
    private String collectionMethod;

    @Schema(description = "收款日期（当paymentStatus为paid时必填）", example = "2024-06-21")
    private LocalDate collectionDate;

    @Schema(description = "操作人（当paymentStatus为paid时必填）", example = "李四")
    private String operatorName;

    @Schema(description = "收款备注", example = "银行转账收款，已确认到账")
    private String collectionRemark;

}




