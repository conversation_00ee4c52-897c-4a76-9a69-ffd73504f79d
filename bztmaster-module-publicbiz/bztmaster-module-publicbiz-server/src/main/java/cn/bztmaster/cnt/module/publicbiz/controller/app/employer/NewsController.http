### 获取首页资讯列表（前4条）
GET {{baseUrl}}/publicbiz/employer/news/home?limit=4
Content-Type: application/json

### 获取首页资讯列表（前6条）
GET {{baseUrl}}/publicbiz/employer/news/home?limit=6
Content-Type: application/json

### 获取资讯列表（分页）
GET {{baseUrl}}/publicbiz/employer/news/list?page=1&pageSize=10
Content-Type: application/json

### 获取资讯列表（带分类筛选）
GET {{baseUrl}}/publicbiz/employer/news/list?page=1&pageSize=10&categoryId=1
Content-Type: application/json

### 获取资讯列表（带关键词搜索）
GET {{baseUrl}}/publicbiz/employer/news/list?page=1&pageSize=10&keyword=家政
Content-Type: application/json

### 获取资讯列表（带状态筛选）
GET {{baseUrl}}/publicbiz/employer/news/list?page=1&pageSize=10&status=published
Content-Type: application/json

### 获取资讯详情
GET {{baseUrl}}/publicbiz/employer/news/detail/1
Content-Type: application/json

### 增加资讯浏览次数
POST {{baseUrl}}/publicbiz/employer/news/incrementView/1
Content-Type: application/json

### 测试错误情况：无效的资讯ID
GET {{baseUrl}}/publicbiz/employer/news/detail/999999
Content-Type: application/json

### 测试错误情况：无效的分页参数
GET {{baseUrl}}/publicbiz/employer/news/list?page=0&pageSize=100
Content-Type: application/json

### 测试错误情况：无效的状态值
GET {{baseUrl}}/publicbiz/employer/news/list?status=invalid
Content-Type: application/json 