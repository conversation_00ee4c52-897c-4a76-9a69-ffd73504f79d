package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 管理后台 - 高校实践订单纸质合同上传响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 高校实践订单纸质合同上传响应")
@Data
public class UniversityPracticeOrderPaperContractUploadRespVO {

    @Schema(description = "订单ID", example = "1024")
    private Long orderId;

    @Schema(description = "订单号", example = "HP202406001")
    private String orderNo;

    @Schema(description = "操作是否成功", example = "true")
    private Boolean success;

    @Schema(description = "文件访问URL", example = "https://example.com/files/contract_123.pdf")
    private String fileUrl;

    @Schema(description = "文件名", example = "contract_123.pdf")
    private String fileName;

    @Schema(description = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    @Schema(description = "合同编号", example = "HT001")
    private String contractNumber;

    @Schema(description = "合同名称", example = "测试合同")
    private String contractName;

    @Schema(description = "签署日期", example = "2024-01-15")
    private LocalDate signDate;

    @Schema(description = "合同金额", example = "1000.00")
    private BigDecimal contractAmount;
}
