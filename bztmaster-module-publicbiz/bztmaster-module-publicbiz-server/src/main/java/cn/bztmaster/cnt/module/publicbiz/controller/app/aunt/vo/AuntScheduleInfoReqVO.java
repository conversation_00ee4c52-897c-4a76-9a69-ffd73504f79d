package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@Schema(description = "用户 APP - 阿姨排班信息查询 Request VO")
@Data
public class AuntScheduleInfoReqVO {

    @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "550e8400-e29b-41d4-a716-446655440000")
    @NotNull(message = "阿姨OneID不能为空")
    private String oneId;

    @Schema(description = "开始日期", example = "2025-07-01")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "开始日期格式不正确，应为yyyy-MM-dd")
    private String startDate;

    @Schema(description = "结束日期", example = "2025-07-07")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "结束日期格式不正确，应为yyyy-MM-dd")
    private String endDate;
}
