### 获得阿姨订单列表
GET {{appApi}}/publicbiz/aunt/order/list?pageNo=1&pageSize=10
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### 获得阿姨订单列表（按状态筛选）
GET {{appApi}}/publicbiz/aunt/order/list?pageNo=1&pageSize=10&orderStatus=pending
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### 获得阿姨订单详情
GET {{appApi}}/publicbiz/aunt/order/detail?orderId=1
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}

### 确认订单
POST {{appApi}}/publicbiz/aunt/order/confirm
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}
Content-Type: application/json

{
  "orderId": 1
}

### 拒绝订单
POST {{appApi}}/publicbiz/aunt/order/reject
Authorization: Bearer {{appToken}}
tenant-id: {{appTenantId}}
Content-Type: application/json

{
  "orderId": 1,
  "rejectReason": "时间冲突，无法提供服务"
} 