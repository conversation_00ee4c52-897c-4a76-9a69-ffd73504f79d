package cn.bztmaster.cnt.module.publicbiz.controller.admin.teacher.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Schema(description = "师资库 - 讲师 Response VO")
public class TeacherRespVO {
    private Long id;
    private String avatar;
    private String name;
    private String description;
    private String type;
    private String biz;
    private String org;
    private String field;
    private String phone;
    private String email;
    private String status;
    private String signStatus;
    private Date signDate;
    private String contractType;
    private String contractTemplate;
    private String contractNo;
    private String contractName;
    private Date contractPeriodStart;
    private Date contractPeriodEnd;
    private BigDecimal contractAmount;
    private String contractFileName;
    private String contractFileUrl;
    private List<TeacherCertRespVO> certFiles;
    private Date createTime;
    private Date updateTime;
} 