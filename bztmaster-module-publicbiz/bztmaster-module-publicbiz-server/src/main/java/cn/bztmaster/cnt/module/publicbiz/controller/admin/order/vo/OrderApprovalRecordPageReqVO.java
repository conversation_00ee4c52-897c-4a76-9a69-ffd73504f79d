package cn.bztmaster.cnt.module.publicbiz.controller.admin.order.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;

import static cn.bztmaster.cnt.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY;

/**
 * 审批记录分页查询请求 VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 审批记录分页查询")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderApprovalRecordPageReqVO extends PageParam {

    @Schema(description = "订单ID", example = "123")
    private Long orderId;

    @Schema(description = "订单编号", example = "HT001")
    private String orderNo;

    @Schema(description = "审批类型", example = "order_approval")
    private String approvalType;

    @Schema(description = "审批状态", example = "approved")
    private String status;

    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate startDate;

    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY)
    private LocalDate endDate;
}
