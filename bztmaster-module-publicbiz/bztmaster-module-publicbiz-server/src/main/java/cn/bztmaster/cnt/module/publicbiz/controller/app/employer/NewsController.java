package cn.bztmaster.cnt.module.publicbiz.controller.app.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsDetailRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsHomeRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsIncrementViewRespVO;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.NewsListRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.NewsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 资讯 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 资讯")
@RestController
@RequestMapping("/publicbiz/employer/news")
@Validated
@Slf4j
public class NewsController {

    @Resource
    private NewsService newsService;

    @GetMapping("/home")
    @Operation(summary = "获取首页资讯列表（前N条）")
    @PermitAll
    public CommonResult<NewsHomeRespVO> getHomeNewsList(
            @Parameter(description = "返回数量，最大10", example = "4") @RequestParam(value = "limit", required = false, defaultValue = "4") Integer limit) {

        // 参数验证
        if (limit != null && (limit <= 0 || limit > 10)) {
            return CommonResult.error(400, "返回数量必须在1-10之间");
        }

        NewsHomeRespVO result = newsService.getHomeNewsList(limit);
        return success(result);
    }

    @GetMapping("/list")
    @Operation(summary = "获取资讯列表（分页）")
    @PermitAll
    public CommonResult<NewsListRespVO> getNewsList(
            @Parameter(description = "页码，从1开始", example = "1") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页数量，最大50", example = "10") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @Parameter(description = "分类ID筛选", example = "1") @RequestParam(value = "categoryId", required = false) Long categoryId,
            @Parameter(description = "标题关键词搜索", example = "家政") @RequestParam(value = "keyword", required = false) String keyword,
            @Parameter(description = "状态筛选：draft-草稿/published-已发布/offline-已下架", example = "published") @RequestParam(value = "status", required = false, defaultValue = "published") String status) {

        // 参数验证
        if (page != null && page < 1) {
            return CommonResult.error(400, "页码必须大于等于1");
        }
        if (pageSize != null && (pageSize < 1 || pageSize > 50)) {
            return CommonResult.error(400, "每页数量必须在1-50之间");
        }
        if (status != null && !status.matches("^(draft|published|offline)$")) {
            return CommonResult.error(400, "状态值只能是draft、published或offline");
        }

        // 构建分页参数
        PageParam pageParam = new PageParam();
        pageParam.setPageNo(page != null ? page : 1);
        pageParam.setPageSize(pageSize != null ? pageSize : 10);

        NewsListRespVO result = newsService.getNewsPage(pageParam, categoryId, keyword, status);
        return success(result);
    }

    @GetMapping("/detail/{id}")
    @Operation(summary = "获取资讯详情")
    @PermitAll
    public CommonResult<NewsDetailRespVO> getNewsDetail(
            @Parameter(description = "资讯ID", required = true, example = "1") @PathVariable("id") Long id) {

        // 参数验证
        if (id == null || id <= 0) {
            return CommonResult.error(400, "资讯ID不能为空且必须大于0");
        }

        try {
            NewsDetailRespVO result = newsService.getNewsDetail(id);
            return success(result);
        } catch (RuntimeException e) {
            return CommonResult.error(404, e.getMessage());
        }
    }

    @PostMapping("/incrementView/{id}")
    @Operation(summary = "增加资讯浏览次数")
    @PermitAll
    public CommonResult<NewsIncrementViewRespVO> incrementViewCount(
            @Parameter(description = "资讯ID", required = true, example = "1") @PathVariable("id") Long id) {

        // 参数验证
        if (id == null || id <= 0) {
            return CommonResult.error(400, "资讯ID不能为空且必须大于0");
        }

        try {
            NewsIncrementViewRespVO result = newsService.incrementViewCount(id);
            return success(result);
        } catch (RuntimeException e) {
            return CommonResult.error(404, e.getMessage());
        }
    }

}