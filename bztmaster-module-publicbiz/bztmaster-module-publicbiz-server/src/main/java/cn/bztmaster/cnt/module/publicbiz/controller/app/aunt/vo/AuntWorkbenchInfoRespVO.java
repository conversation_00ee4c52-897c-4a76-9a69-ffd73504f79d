package cn.bztmaster.cnt.module.publicbiz.controller.app.aunt.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "用户 APP - 阿姨工作台信息 Response VO")
@Data
public class AuntWorkbenchInfoRespVO {

    @Schema(description = "阿姨信息")
    private AuntInfo auntInfo;

    @Schema(description = "KPI数据")
    private KPIData kpiData;

    @Schema(description = "订单统计")
    private OrderStats orderStats;

    @Schema(description = "阿姨信息")
    @Data
    public static class AuntInfo {

        @Schema(description = "阿姨OneID", requiredMode = Schema.RequiredMode.REQUIRED, example = "550e8400-e29b-41d4-a716-446655440000")
        private String oneId;

        @Schema(description = "阿姨姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李阿姨")
        private String name;

        @Schema(description = "手机号", requiredMode = Schema.RequiredMode.REQUIRED, example = "13800138000")
        private String phone;

        @Schema(description = "头像URL", example = "https://example.com/avatar.jpg")
        private String avatar;

        @Schema(description = "服务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "保洁")
        private String serviceType;

        @Schema(description = "从业年限", requiredMode = Schema.RequiredMode.REQUIRED, example = "5")
        private Integer experienceYears;

        @Schema(description = "评级", requiredMode = Schema.RequiredMode.REQUIRED, example = "4.8")
        private BigDecimal rating;

        @Schema(description = "当前状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "空闲中")
        private String currentStatus;

        @Schema(description = "所属机构", example = "传能家政")
        private String agencyName;
    }

    @Schema(description = "KPI数据")
    @Data
    public static class KPIData {

        @Schema(description = "今日排班数", requiredMode = Schema.RequiredMode.REQUIRED, example = "3")
        private Integer todaySchedule;

        @Schema(description = "本月预估收入", requiredMode = Schema.RequiredMode.REQUIRED, example = "8500.00")
        private BigDecimal monthlyIncome;

        @Schema(description = "本月服务时长(小时)", requiredMode = Schema.RequiredMode.REQUIRED, example = "88")
        private Integer monthlyServiceHours;
    }

    @Schema(description = "订单统计")
    @Data
    public static class OrderStats {

        @Schema(description = "待确认订单数", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
        private Integer pending;

        @Schema(description = "进行中订单数", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
        private Integer inProgress;

        @Schema(description = "已完成订单数", requiredMode = Schema.RequiredMode.REQUIRED, example = "28")
        private Integer completed;

        @Schema(description = "已取消订单数", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
        private Integer cancelled;
    }

} 