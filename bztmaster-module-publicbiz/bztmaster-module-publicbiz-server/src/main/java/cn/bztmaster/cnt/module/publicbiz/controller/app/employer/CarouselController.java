package cn.bztmaster.cnt.module.publicbiz.controller.app.employer;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo.CarouselListRespVO;
import cn.bztmaster.cnt.module.publicbiz.service.employer.CarouselService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;

import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;

/**
 * 轮播图 Controller
 *
 * <AUTHOR>
 */
@Tag(name = "用户 APP - 轮播图")
@RestController
@RequestMapping("/publicbiz/employer/carousel")
@Validated
@Slf4j
public class CarouselController {

    @Resource
    private CarouselService carouselService;

    @GetMapping("/list")
    @Operation(summary = "获取轮播图列表")
    @PermitAll
    public CommonResult<CarouselListRespVO> getCarouselList(
            @Parameter(description = "平台类型：employer-雇主端，aunt-阿姨端", required = true, example = "employer") @RequestParam("platform") String platform,
            @Parameter(description = "页码，从1开始", example = "1") @RequestParam(value = "page", required = false, defaultValue = "1") Integer page,
            @Parameter(description = "每页数量，最大50", example = "10") @RequestParam(value = "pageSize", required = false, defaultValue = "10") Integer pageSize,
            @Parameter(description = "状态筛选：1-启用，0-禁用，不传则查询所有", example = "1") @RequestParam(value = "status", required = false) Integer status) {

        // 参数验证
        if (platform == null || platform.trim().isEmpty()) {
            return CommonResult.error(400, "平台类型不能为空");
        }

        if (!"employer".equals(platform) && !"aunt".equals(platform)) {
            return CommonResult.error(400, "平台类型只能是employer或aunt");
        }

        if (status != null && status != 0 && status != 1) {
            return CommonResult.error(400, "状态值只能是0或1");
        }

        CarouselListRespVO result = carouselService.getCarouselList(platform, page, pageSize, status);
        return success(result);
    }

}