package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 地址列表响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "用户 APP - 地址列表响应 VO")
@Data
public class AddressListRespVO {

    @Schema(description = "总数", example = "3")
    private Long total;

    @Schema(description = "地址列表")
    private List<AddressRespVO> records;

    @Schema(description = "地址信息")
    @Data
    public static class AddressRespVO {
        @Schema(description = "地址ID", example = "1")
        private Long id;

        @Schema(description = "用户ID", example = "1")
        private Long userId;

        @Schema(description = "地址标签", example = "家庭地址")
        private String label;

        @Schema(description = "收货人姓名", example = "李四")
        private String receiverName;

        @Schema(description = "收货人手机号", example = "13900139001")
        private String receiverPhone;

        @Schema(description = "省份编码", example = "510000")
        private String provinceCode;

        @Schema(description = "省份名称", example = "四川省")
        private String provinceName;

        @Schema(description = "城市编码", example = "510100")
        private String cityCode;

        @Schema(description = "城市名称", example = "成都市")
        private String cityName;

        @Schema(description = "区县编码", example = "510104")
        private String districtCode;

        @Schema(description = "区县名称", example = "锦江区")
        private String districtName;

        @Schema(description = "省市区组合", example = "四川省成都市锦江区")
        private String region;

        @Schema(description = "详细地址", example = "春熙路1号太古里购物中心2楼201室")
        private String address;

        @Schema(description = "完整地址", example = "四川省成都市锦江区春熙路1号太古里购物中心2楼201室")
        private String fullAddress;

        @Schema(description = "经度", example = "104.0800000")
        private BigDecimal longitude;

        @Schema(description = "纬度", example = "30.6500000")
        private BigDecimal latitude;

        @Schema(description = "是否默认地址", example = "true")
        private Boolean isDefault;

        @Schema(description = "状态", example = "true")
        private Boolean status;

        @Schema(description = "排序", example = "1")
        private Integer sort;

        @Schema(description = "备注")
        private String remark;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;

        @Schema(description = "更新时间")
        private LocalDateTime updateTime;
    }
}
