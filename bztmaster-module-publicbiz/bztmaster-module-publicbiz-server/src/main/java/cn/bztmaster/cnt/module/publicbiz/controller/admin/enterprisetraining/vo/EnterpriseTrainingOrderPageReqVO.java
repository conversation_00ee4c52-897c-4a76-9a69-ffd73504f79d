package cn.bztmaster.cnt.module.publicbiz.controller.admin.enterprisetraining.vo;

import cn.bztmaster.cnt.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 企业培训订单 Page Request VO
 *
 * <AUTHOR>
 */
@Schema(description = "管理后台 - 企业培训订单 Page Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
public class EnterpriseTrainingOrderPageReqVO extends PageParam {

    @Schema(description = "租户ID", example = "1", required = true)
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "订单状态", example = "pending_approval")
    private String orderStatus;

    @Schema(description = "支付状态", example = "unpaid")
    private String paymentStatus;

    @Schema(description = "搜索关键词（企业名称、培训项目）", example = "数字化转型")
    private String keyword;

    @Schema(description = "开始日期（YYYY-MM-DD）", example = "2024-06-01")
    private String startDate;

    @Schema(description = "结束日期（YYYY-MM-DD）", example = "2024-06-30")
    private String endDate;

    @Schema(description = "培训类型", example = "技能培训")
    private String trainingType;

    @Schema(description = "负责人ID", example = "1001")
    private Long managerId;
}
