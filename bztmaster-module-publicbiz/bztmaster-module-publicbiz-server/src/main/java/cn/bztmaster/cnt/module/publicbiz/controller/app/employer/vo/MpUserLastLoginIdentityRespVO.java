package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 小程序用户最后登录身份记录 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "小程序用户最后登录身份记录 Response VO")
@Data
public class MpUserLastLoginIdentityRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "用户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long userId;

    @Schema(description = "微信openid", requiredMode = Schema.RequiredMode.REQUIRED, example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    private String openid;

    @Schema(description = "微信unionid", example = "oH_Tu5EtrrF6n7u8v9w0x1y2z3")
    private String unionid;

    @Schema(description = "身份类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "EMPLOYER")
    private String identityType;

    @Schema(description = "身份类型描述", example = "雇主")
    private String identityTypeDesc;

    @Schema(description = "身份关联ID", example = "1024")
    private Long identityId;

    @Schema(description = "身份名称", example = "张先生")
    private String identityName;

    @Schema(description = "最后登录时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime lastLoginTime;

    @Schema(description = "最后登录IP", example = "***********")
    private String lastLoginIp;

    @Schema(description = "设备信息", example = "iPhone 14 Pro")
    private String deviceInfo;

    @Schema(description = "登录状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer loginStatus;

    @Schema(description = "登录状态描述", example = "正常")
    private String loginStatusDesc;

    @Schema(description = "微信session_key", example = "session_key_example")
    private String sessionKey;

    @Schema(description = "访问令牌", example = "access_token_example")
    private String accessToken;

    @Schema(description = "刷新令牌", example = "refresh_token_example")
    private String refreshToken;

    @Schema(description = "令牌过期时间")
    private LocalDateTime tokenExpireTime;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}