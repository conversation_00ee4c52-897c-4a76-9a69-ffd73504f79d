package cn.bztmaster.cnt.module.publicbiz.controller.app.employer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 首页阿姨列表 Response VO
 *
 * <AUTHOR>
 */
@Schema(description = "首页阿姨列表 Response VO")
@Data
public class AuntHomeListRespVO {

    @Schema(description = "金牌阿姨列表")
    private List<AuntInfo> goldAunts;

    @Schema(description = "金牌月嫂列表")
    private List<AuntInfo> maternityAunts;

    @Schema(description = "阿姨信息")
    @Data
    public static class AuntInfo {

        @Schema(description = "阿姨ID", example = "1")
        private Long id;

        @Schema(description = "阿姨姓名", example = "王阿姨")
        private String name;

        @Schema(description = "头像URL", example = "https://example.com/avatar1.jpg")
        private String avatar;

        @Schema(description = "所属机构简称", example = "金牌家政")
        private String agencyName;

        @Schema(description = "服务类型", example = "保洁")
        private String serviceType;

        @Schema(description = "评级", example = "4.9")
        private BigDecimal rating;

        @Schema(description = "从业年限", example = "5")
        private Integer experienceYears;
    }
}