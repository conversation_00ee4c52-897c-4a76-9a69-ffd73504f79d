package cn.bztmaster.cnt.module.publicbiz.dal.dataobject.employment;

import cn.bztmaster.cnt.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 机构资质文件 DO
 *
 * <AUTHOR>
 */
@TableName("publicbiz_agency_qualification")
@KeySequence("publicbiz_agency_qualification_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgencyQualificationDO extends BaseDO {

    /**
     * 资质文件ID
     */
    @TableId
    private Long id;

    /**
     * 机构ID
     */
    private Long agencyId;

    /**
     * 文件类型：business_license-营业执照,qualification_cert-资质证书,contract-合同文件,human_resources人力资源服务许可证,
     * opening_permit 开户许可证,door_photo 门头照,organizational_structure 组织机构代码证书,id_card: 法人身份证,other: 其他附件
     */
    private String fileType;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件扩展名
     */
    private String fileExtension;

    /**
     * 排序，数字越小越靠前
     */
    private Integer sortOrder;

    /**
     * 状态：1-有效，0-无效
     */
    private Integer status;

    // 新增字段
    /**
     * 文件分类：business_license-营业执照/account_permit-开户许可证/id_card_front-身份证正面/id_card_back-身份证反面/org_code-组织机构代码证
     */
    private String fileCategory;

    /**
     * 文件描述
     */
    private String fileDescription;
} 