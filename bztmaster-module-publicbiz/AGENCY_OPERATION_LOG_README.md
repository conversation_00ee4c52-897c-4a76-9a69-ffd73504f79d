# 机构管理操作日志功能实现说明

## 概述

本文档描述了机构管理模块的操作日志功能实现，包括新增、更新、删除机构时的日志记录。

## 功能特性

### 1. 新增机构操作日志
- **记录字段**: title、agency_name
- **日志模板**: "新增了机构【{{#agency.agencyName}}】"
- **触发时机**: 调用 `createAgency` 方法时

### 2. 更新机构操作日志
- **记录字段**: title、review_status、reviewer、review_time、review_remark
- **日志模板**: "更新了机构【{{#agency.agencyName}}】: {_DIFF{#updateReqVO}}"
- **触发时机**: 调用 `updateAgency` 方法时
- **字段差异记录**: 支持审核状态和审核备注的差异对比

### 3. 删除机构操作日志
- **记录字段**: title、agency_name
- **日志模板**: "删除了机构【{{#agency.agencyName}}】"
- **触发时机**: 调用 `deleteAgency` 方法时

## 技术实现

### 1. 操作日志常量定义
文件路径: `src/main/java/cn/bztmaster/cnt/module/publicbiz/enums/LogRecordConstants.java`

```java
// ======================= AGENCY 机构管理 =======================
String AGENCY_TYPE = "AGENCY 机构管理";
String AGENCY_CREATE_SUB_TYPE = "新增机构";
String AGENCY_CREATE_SUCCESS = "新增了机构【{{#agency.agencyName}}】";
String AGENCY_UPDATE_SUB_TYPE = "更新机构";
String AGENCY_UPDATE_SUCCESS = "更新了机构【{{#agency.agencyName}}】: {_DIFF{#updateReqVO}}";
String AGENCY_DELETE_SUB_TYPE = "删除机构";
String AGENCY_DELETE_SUCCESS = "删除了机构【{{#agency.agencyName}}】";
```

### 2. 操作日志注解使用
在 Service 方法上使用 `@LogRecord` 注解：

```java
@Override
@Transactional(rollbackFor = Exception.class)
@LogRecord(type = LogRecordConstants.AGENCY_TYPE, subType = LogRecordConstants.AGENCY_CREATE_SUB_TYPE, 
            bizNo = "{{#agency.id}}", success = LogRecordConstants.AGENCY_CREATE_SUCCESS)
public Long createAgency(AgencyCreateReqVO reqVO) {
    // 业务逻辑实现
    // ...
    
    // 设置操作日志上下文
    LogRecordContext.putVariable("agency", agency);
    
    return agency.getId();
}
```

### 3. 字段差异记录
使用 `@DiffLogField` 注解标记需要记录差异的字段：

```java
@Schema(description = "审核状态", example = "approved")
@NotNull(message = "审核状态不能为空")
@DiffLogField(name = "审核状态", function = "getAgencyReviewStatus")
private String reviewStatus;

@Schema(description = "审核备注", example = "审核通过，资料齐全")
@DiffLogField(name = "审核备注")
private String reviewRemark;
```

### 4. 自定义解析函数
创建 `AgencyReviewStatusParseFunction` 类来解析审核状态：

```java
@Component
@Slf4j
public class AgencyReviewStatusParseFunction implements IParseFunction {
    
    public static final String NAME = "getAgencyReviewStatus";
    
    @Override
    public String apply(Object value) {
        if (StrUtil.isEmptyIfStr(value)) {
            return "";
        }
        
        String status = value.toString();
        switch (status) {
            case "pending":
                return "待审核";
            case "approved":
                return "已通过";
            case "rejected":
                return "已拒绝";
            default:
                return status;
        }
    }
}
```

## 数据库表结构

操作日志记录到 `system_operate_log` 表，包含以下关键字段：
- `type`: 操作模块类型 (AGENCY 机构管理)
- `sub_type`: 操作名 (新增机构、更新机构、删除机构)
- `biz_id`: 操作数据模块编号 (机构ID)
- `action`: 操作内容 (具体的日志模板内容)
- `success`: 操作结果
- `extra`: 拓展字段

## 使用示例

### 新增机构
```java
POST /publicbiz/agency/create
{
    "agencyName": "测试机构",
    "agencyNo": "AG001",
    "agencyType": "cooperation"
    // ... 其他字段
}
```

**操作日志记录**:
```
新增了机构【测试机构】
```

### 更新机构审核状态
```java
PUT /publicbiz/agency/update
{
    "id": 1,
    "reviewStatus": "approved",
    "reviewRemark": "审核通过，资料齐全"
}
```

**操作日志记录**:
```
更新了机构【测试机构】: 【审核状态】从【待审核】修改为【已通过】；【审核备注】从【】修改为【审核通过，资料齐全】
```

### 删除机构
```java
DELETE /publicbiz/agency/delete/1
```

**操作日志记录**:
```
删除了机构【测试机构】
```

## 注意事项

1. **事务管理**: 所有操作都使用 `@Transactional` 注解确保数据一致性
2. **权限控制**: 所有接口都使用 `@PreAuthorize` 注解进行权限验证
3. **参数校验**: 使用 JSR-303 注解进行参数校验
4. **日志上下文**: 在方法返回前设置 `LogRecordContext.putVariable` 确保日志记录完整
5. **错误处理**: 完善的异常处理和错误码定义

## 扩展说明

如需添加新的操作日志类型，请按以下步骤操作：

1. 在 `LogRecordConstants` 中添加新的常量定义
2. 在对应的 Service 方法上添加 `@LogRecord` 注解
3. 在方法中设置必要的日志上下文变量
4. 如需字段差异记录，在 VO 类中添加 `@DiffLogField` 注解
5. 如需自定义解析函数，创建新的 `ParseFunction` 实现类

## 测试

运行 `AgencyServiceLogTest` 测试类来验证操作日志功能：

```bash
mvn test -Dtest=AgencyServiceLogTest
```

该测试类包含新增、更新、删除机构的测试用例，可以验证操作日志是否正确记录。 