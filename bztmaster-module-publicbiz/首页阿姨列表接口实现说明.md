# 首页阿姨列表接口实现说明

## 接口概述

根据 `PromptDocument/小程序/雇主端/首页阿姨列表接口.md` 接口文档，已在 `bztmaster-module-publicbiz` 模块下成功实现了首页阿姨列表接口。

## 实现文件列表

### 1. Controller层
- **文件**: `EmployerAuntController.java`
- **路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/`
- **功能**: 提供RESTful API接口，处理HTTP请求

### 2. Service层
- **接口**: `EmployerAuntService.java`
- **实现**: `EmployerAuntServiceImpl.java`
- **路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/service/employer/`
- **功能**: 实现业务逻辑，包括数据查询、处理和转换

### 3. VO层
- **文件**: `AuntHomeListRespVO.java`
- **路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/vo/`
- **功能**: 定义响应数据结构

### 4. Mapper层
- **接口**: `PractitionerMapper.java` (已扩展)
- **XML**: `PractitionerMapper.xml` (已扩展)
- **功能**: 数据访问层，执行SQL查询

### 5. 测试文件
- **文件**: `EmployerAuntController.http`
- **路径**: `bztmaster-module-publicbiz-server/src/main/java/cn/bztmaster/cnt/module/publicbiz/controller/app/employer/`
- **功能**: HTTP接口测试用例

## 接口详情

### 接口信息
- **接口名称**: 获取首页金牌阿姨列表
- **请求方式**: GET
- **接口路径**: `/publicbiz/employer/aunt/home-list`
- **接口描述**: 获取首页金牌阿姨和金牌月嫂数据，按评级排序获取前8名，前4名分配给金牌阿姨，后4名分配给金牌月嫂

### 请求参数
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | int | 否 | 8 | 返回数据条数，最大8条 |

### 响应结构
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "goldAunts": [
      {
        "id": 1,
        "name": "王阿姨",
        "avatar": "https://example.com/avatar1.jpg",
        "agencyName": "金牌家政",
        "serviceType": "保洁",
        "rating": 4.9,
        "experienceYears": 5
      }
    ],
    "maternityAunts": [
      {
        "id": 2,
        "name": "李阿姨",
        "avatar": "https://example.com/avatar2.jpg",
        "agencyName": "专业月嫂",
        "serviceType": "月嫂",
        "rating": 4.8,
        "experienceYears": 8
      }
    ]
  }
}
```

## 业务逻辑

### 数据筛选条件
1. **基础筛选条件**：
   - `platform_status = 'cooperating'` (合作中)
   - `deleted = 0` (未删除)
   - `status = 'active'` (正常状态)

2. **数据获取逻辑**：
   - 按 `rating` 降序排序获取前8名阿姨
   - 前4名分配给金牌阿姨列表
   - 后4名分配给金牌月嫂列表
   - 不按 `service_type` 进行过滤

3. **机构名称处理**：
   - 优先使用机构表的 `agency_short_name` 字段（简称）
   - 如果简称为空，则使用 `agency_name` 字段（全称）

## 技术特点

1. **分层架构**: 严格按照Controller-Service-Mapper分层架构设计
2. **RESTful API**: 遵循RESTful设计规范
3. **参数校验**: 使用@Valid注解进行参数校验
4. **权限控制**: 使用@PermitAll注解允许所有用户访问
5. **Swagger文档**: 使用@Tag、@Operation等注解生成API文档
6. **日志记录**: 使用@Slf4j进行日志记录
7. **数据转换**: 使用Stream API进行数据转换和处理
8. **性能优化**: 批量查询机构信息，减少数据库访问次数

## 数据库查询

### 主要SQL查询
```sql
SELECT id, aunt_oneid, name, phone, id_card, hometown, age, gender, avatar, 
       service_type, experience_years, platform_status, rating, agency_id, 
       agency_name, status, current_status, current_order_id, total_orders, 
       total_income, customer_satisfaction, create_time, update_time, creator, 
       updater, deleted, tenant_id
FROM publicbiz_practitioner
WHERE platform_status = 'cooperating'
  AND deleted = 0
  AND status = 'active'
ORDER BY rating DESC
LIMIT #{limit}
```

## 测试用例

### 1. 正常请求（默认参数）
```http
GET /publicbiz/employer/aunt/home-list
```

### 2. 自定义条数
```http
GET /publicbiz/employer/aunt/home-list?limit=6
```

### 3. 最大条数
```http
GET /publicbiz/employer/aunt/home-list?limit=8
```

## 注意事项

1. **机构名称显示**: 接口返回的机构名称优先使用机构表的简称信息，对应表 `publicbiz_agency` 的 `agency_short_name` 字段
2. **数据分配**: 前4名分配给金牌阿姨，后4名分配给金牌月嫂，不按服务类型过滤
3. **参数限制**: limit参数最大值为8，超过会自动调整为8
4. **权限控制**: 接口使用@PermitAll注解，允许所有用户访问，无需登录验证 