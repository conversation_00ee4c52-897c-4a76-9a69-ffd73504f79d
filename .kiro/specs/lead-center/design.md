# 线索中心功能设计文档

## 概述

线索中心是一个基于Spring Boot和MyBatis的企业级线索管理系统，采用分层架构设计，支持多租户模式。系统提供完整的线索生命周期管理功能，包括线索录入、分配、跟进、转化等核心业务流程。

## 架构

### 整体架构
系统采用经典的三层架构模式：
- **Controller层**：处理HTTP请求，参数校验，响应格式化
- **Service层**：业务逻辑处理，事务管理
- **Data Access层**：数据持久化，包括Mapper接口和DO对象

### 模块结构
```
bztmaster-module-publicbiz/
├── bztmaster-module-publicbiz-api/          # API接口定义
│   └── src/main/java/cn/bztmaster/cnt/module/publicbiz/api/
│       ├── leads/                           # 线索相关API
│       │   ├── LeadsApi.java               # Feign接口
│       │   └── dto/                        # 数据传输对象
│       └── enums/                          # 枚举定义
└── bztmaster-module-publicbiz-server/       # 服务实现
    └── src/main/java/cn/bztmaster/cnt/module/publicbiz/
        ├── controller/admin/leads/          # 控制器
        ├── service/leads/                   # 业务服务
        ├── dal/                            # 数据访问层
        │   ├── dataobject/leads/           # 数据对象
        │   └── mysql/leads/                # Mapper接口
        └── convert/leads/                  # 对象转换
```

## 组件和接口

### 核心组件

#### 1. 线索管理组件 (LeadsController)
- **职责**：处理线索的CRUD操作
- **主要接口**：
  - `POST /api/publicbiz/leads/page` - 分页查询线索
  - `POST /api/publicbiz/leads/add` - 新增线索
  - `POST /api/publicbiz/leads/update` - 更新线索
  - `POST /api/publicbiz/leads/delete` - 删除线索
  - `POST /api/publicbiz/leads/assign` - 分配线索

#### 2. 跟进记录组件 (LeadsFollowUpController)
- **职责**：管理线索跟进记录
- **主要接口**：
  - `POST /api/publicbiz/leads/follow-up/page` - 查询跟进记录
  - `POST /api/publicbiz/leads/follow-up/add` - 添加跟进记录

#### 3. 业务服务组件 (LeadsService)
- **职责**：核心业务逻辑处理
- **主要方法**：
  - `pageLeads()` - 分页查询业务逻辑
  - `createLead()` - 创建线索业务逻辑
  - `updateLead()` - 更新线索业务逻辑
  - `deleteLead()` - 删除线索业务逻辑
  - `assignLead()` - 分配线索业务逻辑

### 接口设计

#### RESTful API设计原则
- 统一使用POST方法，采用动词风格的URL路径
- 统一的请求响应格式
- 完整的参数校验和异常处理
- Swagger文档注解支持

#### 数据传输对象设计
- **ReqVO**：请求参数对象，包含校验注解
- **RespVO**：响应数据对象
- **DTO**：服务间数据传输对象
- **DO**：数据库实体对象

## 数据模型

### 核心实体

#### 1. 线索信息表 (lead_info)
```sql
CREATE TABLE `lead_info` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `lead_id` VARCHAR(32) NOT NULL COMMENT '线索ID，系统生成的唯一标识',
  `customer_name` VARCHAR(100) NOT NULL COMMENT '客户姓名',
  `customer_phone` VARCHAR(20) NOT NULL COMMENT '联系电话，11位手机号',
  `lead_source` VARCHAR(50) NOT NULL DEFAULT '其他' COMMENT '线索来源',
  `business_module` VARCHAR(50) NOT NULL COMMENT '业务模块',
  `lead_status` VARCHAR(20) NOT NULL DEFAULT '未处理' COMMENT '线索状态',
  `create_method` VARCHAR(20) NOT NULL DEFAULT '手动创建' COMMENT '创建方式',
  `current_owner` VARCHAR(64) COMMENT '当前跟进人',
  `remark` TEXT COMMENT '备注信息',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除'
);
```

#### 2. 线索跟进记录表 (lead_follow_up_log)
```sql
CREATE TABLE `lead_follow_up_log` (
  `id` BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键，自增',
  `lead_id` VARCHAR(32) NOT NULL COMMENT '关联的线索ID',
  `follow_up_content` TEXT NOT NULL COMMENT '跟进内容详情',
  `tenant_id` BIGINT COMMENT '租户ID',
  `creator` VARCHAR(64) COMMENT '创建人',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` VARCHAR(64) COMMENT '更新人',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` BIT(1) NOT NULL DEFAULT b'0' COMMENT '是否删除'
);
```

### 枚举设计

#### 线索来源枚举 (LeadSourceEnum)
- 官网注册、市场活动、公众号文章、视频号、抖音等

#### 业务模块枚举 (BusinessModuleEnum)  
- 高校业务、家政业务、培训业务、认证业务

#### 线索状态枚举 (LeadStatusEnum)
- 未处理、跟进中、已转化、无意向

#### 创建方式枚举 (CreateMethodEnum)
- 手动创建、系统导入、API接入

## 错误处理

### 异常处理策略
- 使用全局异常处理器统一处理异常
- 自定义业务异常类型
- 标准化错误码和错误信息
- 详细的日志记录

### 错误码设计
- 0：成功
- 400：请求参数错误
- 401：未授权/未登录
- 403：无权限
- 404：资源不存在
- 500：服务器内部错误

## 测试策略

### 单元测试
- Service层业务逻辑测试
- Mapper层数据访问测试
- Convert层对象转换测试

### 集成测试
- Controller层接口测试
- 数据库集成测试
- 完整业务流程测试

### 测试覆盖率
- 目标覆盖率：80%以上
- 重点测试核心业务逻辑
- 边界条件和异常场景测试

## 性能考虑

### 数据库优化
- 合理的索引设计
- 分页查询优化
- 避免N+1查询问题

### 缓存策略
- 字典数据缓存
- 热点数据缓存
- 缓存失效策略

### 并发处理
- 乐观锁处理并发更新
- 事务隔离级别设置
- 连接池配置优化

## 安全考虑

### 数据安全
- 多租户数据隔离
- 敏感信息脱敏
- SQL注入防护

### 接口安全
- 参数校验
- 权限控制
- 接口限流

### 审计日志
- 操作日志记录
- 数据变更追踪
- 用户行为审计