-- 为system_notify_message表添加one_id字段
-- 用于存储对应的用户ID信息（小程序用户标识）

ALTER TABLE `system_notify_message` 
ADD COLUMN `one_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户唯一标识ID（小程序用户标识）' AFTER `user_id`;

-- 添加索引以提高查询性能
ALTER TABLE `system_notify_message` 
ADD INDEX `idx_one_id` (`one_id`) USING BTREE;

-- 添加注释说明
COMMENT ON COLUMN system_notify_message.one_id IS '用户唯一标识ID（小程序用户标识）'; 