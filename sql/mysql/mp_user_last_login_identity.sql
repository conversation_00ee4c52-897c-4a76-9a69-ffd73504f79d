-- 小程序用户最后登录身份记录表
CREATE TABLE `mp_user_last_login_identity` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `openid` varchar(64) NOT NULL COMMENT '微信openid',
  `unionid` varchar(64) DEFAULT NULL COMMENT '微信unionid',
  `identity_type` varchar(20) NOT NULL COMMENT '身份类型：EMPLOYER-雇主，AUNT-阿姨，AGENCY-机构',
  `identity_id` bigint DEFAULT NULL COMMENT '身份关联ID（如雇主ID、阿姨ID等）',
  `identity_name` varchar(100) DEFAULT NULL COMMENT '身份名称（如雇主姓名、阿姨姓名等）',
  `last_login_time` datetime NOT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `device_info` varchar(200) DEFAULT NULL COMMENT '设备信息',
  `login_status` tinyint NOT NULL DEFAULT '1' COMMENT '登录状态：1-正常，0-异常',
  `session_key` varchar(100) DEFAULT NULL COMMENT '微信session_key',
  `access_token` varchar(500) DEFAULT NULL COMMENT '访问令牌',
  `refresh_token` varchar(500) DEFAULT NULL COMMENT '刷新令牌',
  `token_expire_time` datetime DEFAULT NULL COMMENT '令牌过期时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_openid` (`openid`),
  KEY `idx_identity_type` (`identity_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小程序用户最后登录身份记录表'; 