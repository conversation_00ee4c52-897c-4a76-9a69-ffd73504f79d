-- 插入测试数据
INSERT INTO `mp_user_last_login_identity` (
    `user_id`, `openid`, `unionid`, `identity_type`, `identity_id`, `identity_name`, 
    `last_login_time`, `last_login_ip`, `device_info`, `login_status`, 
    `session_key`, `access_token`, `refresh_token`, `token_expire_time`,
    `create_time`, `update_time`, `creator`, `updater`, `deleted`, `tenant_id`
) VALUES 
(1001, 'og6v060mW01i3VuMAzoTxL2Jjmvw', 'unionid_test_001', 'EMPLOYER', 2001, '张先生', 
 NOW(), '*************', 'iPhone 14 Pro', 1, 
 'bwZJGZdtE2HLDs2mqwlm0Q==', 'access_token_test_001', 'refresh_token_test_001', DATE_ADD(NOW(), INTERVAL 2 HOUR),
 NOW(), NOW(), 'system', 'system', 0, 1),

(1002, 'test_openid_002', 'unionid_test_002', 'AUNT', 2002, '李阿姨', 
 NOW(), '*************', 'Android Phone', 1, 
 'session_key_test_002', 'access_token_test_002', 'refresh_token_test_002', DATE_ADD(NOW(), INTERVAL 2 HOUR),
 NOW(), NOW(), 'system', 'system', 0, 1); 