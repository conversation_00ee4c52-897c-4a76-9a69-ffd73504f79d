-- 测试站内信oneId功能的SQL脚本

-- 1. 插入测试数据（包含oneId字段）
INSERT INTO `system_notify_message` (
    `user_id`, `one_id`, `user_type`, `template_id`, `template_code`, 
    `template_nickname`, `template_content`, `template_type`, `template_params`, 
    `read_status`, `creator`, `create_time`, `updater`, `update_time`, `deleted`, `tenant_id`
) VALUES 
(1, 'wx_test_user_001', 2, 1, 'test_template', '系统消息', '欢迎使用站内信功能', 1, '{"name":"测试用户","content":"欢迎消息"}', 0, '1', NOW(), '1', NOW(), 0, 1),
(2, 'wx_test_user_002', 2, 1, 'test_template', '系统消息', '您的订单已确认', 1, '{"orderId":"12345","status":"已确认"}', 0, '1', NOW(), '1', NOW(), 0, 1),
(3, 'wx_test_user_001', 2, 2, 'order_notify', '订单通知', '订单发货通知', 2, '{"orderId":"12345","trackingNo":"SF1234567890"}', 0, '1', NOW(), '1', NOW(), 0, 1);

-- 2. 查询测试数据
SELECT * FROM `system_notify_message` WHERE `one_id` IS NOT NULL;

-- 3. 测试基于oneId的查询
SELECT * FROM `system_notify_message` WHERE `one_id` = 'wx_test_user_001';

-- 4. 测试基于oneId的未读消息统计
SELECT COUNT(*) as unread_count FROM `system_notify_message` 
WHERE `one_id` = 'wx_test_user_001' AND `read_status` = 0;

-- 5. 测试基于oneId的已读消息更新
UPDATE `system_notify_message` 
SET `read_status` = 1, `read_time` = NOW(), `updater` = '1', `update_time` = NOW()
WHERE `one_id` = 'wx_test_user_001' AND `id` = 1;

-- 6. 验证更新结果
SELECT * FROM `system_notify_message` WHERE `one_id` = 'wx_test_user_001'; 