# 传能家政小程序机构注册后端接口开发提示词

## 项目背景

基于传能家政小程序机构注册页面需求，需要开发对应的后端接口。前端页面提供完整的机构注册表单，包括机构基本信息、联系人信息、开票信息、资质信息和证照上传等功能。

## 核心需求分析

### 1. 机构注册模块核心需求
- **机构信息录入**: 收集机构的基本信息、联系人信息、开票信息等
- **证照文件上传**: 支持营业执照、开户许可证、法人身份证、组织机构代码证等文件上传
- **数据验证**: 对必填字段和格式进行严格验证
- **审核流程**: 提交后进入审核流程，支持状态跟踪

### 2. 表单字段分析
根据前端页面分析，包含以下字段：

#### 机构基本信息
- 机构名称 * (必填)
- 统一社会信用代码 * (必填，18位)
- 机构类型 * (必填，下拉选择)
- 成立时间 * (必填，日期格式)
- 注册资本 * (必填，数字格式，单位：万元)
- 经营范围 * (必填，文本域)

#### 联系人信息
- 联系人姓名 * (必填)
- 联系电话 * (必填，11位手机号)
- 电子邮箱 (选填)
- 联系地址 * (必填，文本域)

#### 开票信息
- 开票名称 * (必填)
- 纳税人识别号 * (必填)
- 开户银行 (选填)
- 银行账号 (选填)

#### 资质信息
- 服务资质等级 (选填，下拉选择)
- 员工人数 (选填，数字格式)
- 服务区域 (选填，文本域)

#### 证照上传
- 营业执照 * (必填，最多5张)
- 开户许可证 (选填，1张)
- 法人身份证正面 * (必填，1张)
- 法人身份证反面 * (必填，1张)
- 组织机构代码证 (选填，1张)

## 数据库表结构分析

### 核心表关系
1. **机构主表** (`publicbiz_agency`)
   - 主键: `id`
   - 机构编号: `agency_no`
   - 审核状态: `review_status` (pending-待审核/approved-已通过/rejected-已拒绝)

2. **机构资质文件表** (`publicbiz_agency_qualification`)
   - 关联机构: `agency_id`
   - 文件类型: `file_type`, `file_category`
   - 文件URL: `file_url`

## 具体接口设计

### 1. 机构注册申请接口

**接口路径**: `/app-api/agency/register`
**请求方式**: POST
**功能描述**: 提交机构注册申请

**请求参数**:
```json
{
  "agencyInfo": {
    "agencyName": "XX家政服务有限公司",
    "creditCode": "91110000123456789X",
    "agencyType": "有限责任公司",
    "establishDate": "2020-01-01",
    "registeredCapital": 100.00,
    "businessScope": "家政服务、保洁服务等",
    "contactName": "张经理",
    "contactPhone": "***********",
    "email": "<EMAIL>",
    "address": "北京市朝阳区xxx街道xxx号",
    "invoiceName": "XX家政服务有限公司",
    "taxNumber": "91110000123456789X",
    "bankName": "中国银行",
    "bankAccount": "1234567890123456789",
    "qualificationLevel": "A级",
    "employeeCount": 50,
    "serviceArea": "北京市朝阳区、海淀区"
  },
  "documents": {
    "businessLicenses": [
      "https://example.com/business_license_1.jpg",
      "https://example.com/business_license_2.jpg"
    ],
    "accountPermit": "https://example.com/account_permit.jpg",
    "legalPersonIdFront": "https://example.com/id_front.jpg",
    "legalPersonIdBack": "https://example.com/id_back.jpg",
    "orgCodeCert": "https://example.com/org_code.jpg"
  }
}
```

**响应数据结构**:
```json
{
  "code": 200,
  "message": "注册申请提交成功",
  "data": {
    "applicationId": "AG202401150001",
    "agencyId": 123,
    "reviewStatus": "pending",
    "submitTime": "2024-01-15 10:30:00",
    "estimatedReviewTime": "3-5个工作日"
  }
}
```

**实现逻辑**:
```java
@PostMapping("/agency/register")
public CommonResult<AgencyRegisterResponseVO> registerAgency(@RequestBody AgencyRegisterRequestVO request) {
    // 1. 参数验证
    if (!validateAgencyInfo(request.getAgencyInfo())) {
        return error("机构信息验证失败");
    }
    
    if (!validateDocuments(request.getDocuments())) {
        return error("证照文件验证失败");
    }
    
    // 2. 检查统一社会信用代码是否已存在
    if (agencyService.existsByCreditCode(request.getAgencyInfo().getCreditCode())) {
        return error("该统一社会信用代码已注册");
    }
    
    // 3. 生成机构编号
    String agencyNo = generateAgencyNo();
    
    // 4. 保存机构基本信息
    AgencyDO agency = new AgencyDO();
    BeanUtils.copyProperties(request.getAgencyInfo(), agency);
    agency.setAgencyNo(agencyNo);
    agency.setReviewStatus("pending");
    agency.setStatus("pending");
    agency.setApplicationTime(new Date());
    
    Long agencyId = agencyService.save(agency);
    
    // 5. 保存证照文件信息
    saveAgencyDocuments(agencyId, request.getDocuments());
    
    // 6. 生成申请编号
    String applicationId = generateApplicationId(agencyId);
    
    // 7. 发送审核通知
    sendReviewNotification(agencyId);
    
    // 8. 组装返回数据
    AgencyRegisterResponseVO response = new AgencyRegisterResponseVO();
    response.setApplicationId(applicationId);
    response.setAgencyId(agencyId);
    response.setReviewStatus("pending");
    response.setSubmitTime(new Date());
    response.setEstimatedReviewTime("3-5个工作日");
    
    return success(response);
}
```

### 2. 文件上传接口

**接口路径**: `/app-api/agency/upload`
**请求方式**: POST
**功能描述**: 上传机构证照文件

**请求参数**:
```json
{
  "fileType": "business_license",
  "fileCategory": "business_license",
  "agencyId": 123
}
```

**响应数据结构**:
```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "fileUrl": "https://example.com/uploads/business_license_123.jpg",
    "fileName": "business_license_123.jpg",
    "fileSize": 1024000,
    "fileId": 456
  }
}
```

### 3. 查询注册状态接口

**接口路径**: `/app-api/agency/status/{applicationId}`
**请求方式**: GET
**功能描述**: 查询机构注册申请状态

**响应数据结构**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "applicationId": "AG202401150001",
    "agencyId": 123,
    "reviewStatus": "pending",
    "submitTime": "2024-01-15 10:30:00",
    "reviewTime": null,
    "reviewRemark": null,
    "estimatedReviewTime": "3-5个工作日"
  }
}
```

## 数据验证规则

### 1. 机构基本信息验证
```java
private boolean validateAgencyInfo(AgencyInfoVO agencyInfo) {
    // 机构名称验证
    if (StringUtils.isEmpty(agencyInfo.getAgencyName()) || 
        agencyInfo.getAgencyName().length() > 200) {
        return false;
    }
    
    // 统一社会信用代码验证（18位）
    if (!Pattern.matches("^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$", 
                        agencyInfo.getCreditCode())) {
        return false;
    }
    
    // 机构类型验证
    if (StringUtils.isEmpty(agencyInfo.getAgencyType())) {
        return false;
    }
    
    // 成立时间验证
    if (StringUtils.isEmpty(agencyInfo.getEstablishDate())) {
        return false;
    }
    
    // 注册资本验证
    if (agencyInfo.getRegisteredCapital() == null || 
        agencyInfo.getRegisteredCapital() <= 0) {
        return false;
    }
    
    // 经营范围验证
    if (StringUtils.isEmpty(agencyInfo.getBusinessScope()) || 
        agencyInfo.getBusinessScope().length() > 500) {
        return false;
    }
    
    return true;
}
```

### 2. 联系人信息验证
```java
private boolean validateContactInfo(AgencyInfoVO agencyInfo) {
    // 联系人姓名验证
    if (StringUtils.isEmpty(agencyInfo.getContactName()) || 
        agencyInfo.getContactName().length() > 50) {
        return false;
    }
    
    // 联系电话验证（11位手机号）
    if (!Pattern.matches("^1[3-9]\\d{9}$", agencyInfo.getContactPhone())) {
        return false;
    }
    
    // 邮箱验证（选填）
    if (StringUtils.isNotEmpty(agencyInfo.getEmail()) && 
        !Pattern.matches("^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$", agencyInfo.getEmail())) {
        return false;
    }
    
    // 联系地址验证
    if (StringUtils.isEmpty(agencyInfo.getAddress()) || 
        agencyInfo.getAddress().length() > 500) {
        return false;
    }
    
    return true;
}
```

### 3. 开票信息验证
```java
private boolean validateInvoiceInfo(AgencyInfoVO agencyInfo) {
    // 开票名称验证
    if (StringUtils.isEmpty(agencyInfo.getInvoiceName()) || 
        agencyInfo.getInvoiceName().length() > 200) {
        return false;
    }
    
    // 纳税人识别号验证
    if (StringUtils.isEmpty(agencyInfo.getTaxNumber()) || 
        agencyInfo.getTaxNumber().length() > 50) {
        return false;
    }
    
    return true;
}
```

### 4. 证照文件验证
```java
private boolean validateDocuments(DocumentsVO documents) {
    // 营业执照验证（必填，最多5张）
    if (documents.getBusinessLicenses() == null || 
        documents.getBusinessLicenses().isEmpty() || 
        documents.getBusinessLicenses().size() > 5) {
        return false;
    }
    
    // 法人身份证验证（必填）
    if (StringUtils.isEmpty(documents.getLegalPersonIdFront()) || 
        StringUtils.isEmpty(documents.getLegalPersonIdBack())) {
        return false;
    }
    
    return true;
}
```

## 数据模型定义

### 1. 机构注册请求VO
```java
@Data
public class AgencyRegisterRequestVO {
    private AgencyInfoVO agencyInfo;      // 机构信息
    private DocumentsVO documents;        // 证照文件
}

@Data
public class AgencyInfoVO {
    // 基本信息
    private String agencyName;            // 机构名称
    private String creditCode;            // 统一社会信用代码
    private String agencyType;            // 机构类型
    private String establishDate;         // 成立时间
    private BigDecimal registeredCapital; // 注册资本
    private String businessScope;         // 经营范围
    
    // 联系人信息
    private String contactName;           // 联系人姓名
    private String contactPhone;          // 联系电话
    private String email;                 // 电子邮箱
    private String address;               // 联系地址
    
    // 开票信息
    private String invoiceName;           // 开票名称
    private String taxNumber;             // 纳税人识别号
    private String bankName;              // 开户银行
    private String bankAccount;           // 银行账号
    
    // 资质信息
    private String qualificationLevel;    // 服务资质等级
    private Integer employeeCount;        // 员工人数
    private String serviceArea;           // 服务区域
}

@Data
public class DocumentsVO {
    private List<String> businessLicenses;    // 营业执照列表
    private String accountPermit;             // 开户许可证
    private String legalPersonIdFront;        // 法人身份证正面
    private String legalPersonIdBack;         // 法人身份证反面
    private String orgCodeCert;               // 组织机构代码证
}
```

### 2. 机构注册响应VO
```java
@Data
public class AgencyRegisterResponseVO {
    private String applicationId;         // 申请编号
    private Long agencyId;                // 机构ID
    private String reviewStatus;          // 审核状态
    private Date submitTime;              // 提交时间
    private String estimatedReviewTime;   // 预计审核时间
}
```

## 业务逻辑处理

### 1. 生成机构编号
```java
private String generateAgencyNo() {
    // 生成格式：AG + 年月日 + 4位序号
    String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
    String sequence = String.format("%04d", getNextSequence("agency_no"));
    return "AG" + dateStr + sequence;
}
```

### 2. 生成申请编号
```java
private String generateApplicationId(Long agencyId) {
    // 生成格式：AG + 年月日 + 4位序号
    String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
    String sequence = String.format("%04d", agencyId % 10000);
    return "AG" + dateStr + sequence;
}
```

### 3. 保存证照文件
```java
private void saveAgencyDocuments(Long agencyId, DocumentsVO documents) {
    // 保存营业执照
    if (documents.getBusinessLicenses() != null) {
        for (int i = 0; i < documents.getBusinessLicenses().size(); i++) {
            AgencyQualificationDO qualification = new AgencyQualificationDO();
            qualification.setAgencyId(agencyId);
            qualification.setFileType("business_license");
            qualification.setFileCategory("business_license");
            qualification.setFileUrl(documents.getBusinessLicenses().get(i));
            qualification.setFileName("business_license_" + (i + 1) + ".jpg");
            qualification.setSortOrder(i);
            qualification.setIsRequired(1);
            qualification.setFileDescription("营业执照");
            agencyQualificationService.save(qualification);
        }
    }
    
    // 保存其他证照文件
    saveDocument(agencyId, "account_permit", documents.getAccountPermit(), "开户许可证");
    saveDocument(agencyId, "id_card_front", documents.getLegalPersonIdFront(), "法人身份证正面");
    saveDocument(agencyId, "id_card_back", documents.getLegalPersonIdBack(), "法人身份证反面");
    saveDocument(agencyId, "org_code", documents.getOrgCodeCert(), "组织机构代码证");
}

private void saveDocument(Long agencyId, String fileCategory, String fileUrl, String description) {
    if (StringUtils.isNotEmpty(fileUrl)) {
        AgencyQualificationDO qualification = new AgencyQualificationDO();
        qualification.setAgencyId(agencyId);
        qualification.setFileType("qualification_cert");
        qualification.setFileCategory(fileCategory);
        qualification.setFileUrl(fileUrl);
        qualification.setFileName(fileCategory + ".jpg");
        qualification.setSortOrder(0);
        qualification.setIsRequired(0);
        qualification.setFileDescription(description);
        agencyQualificationService.save(qualification);
    }
}
```

## 安全考虑

1. **文件上传安全**: 验证文件类型、大小，防止恶意文件上传
2. **数据验证**: 严格验证所有输入数据，防止SQL注入和XSS攻击
3. **权限控制**: 确保只有授权用户可以提交注册申请
4. **敏感信息保护**: 对身份证号等敏感信息进行脱敏处理
5. **日志记录**: 记录所有注册操作，便于审计和问题追踪

## 错误处理

1. **参数验证错误**: 返回具体的字段验证错误信息
2. **文件上传错误**: 处理文件大小超限、格式不支持等错误
3. **业务逻辑错误**: 处理重复注册、状态异常等业务错误
4. **系统错误**: 处理数据库连接、文件存储等系统级错误

## 接口调用流程

1. 前端收集用户填写的机构信息
2. 调用文件上传接口上传证照文件
3. 调用机构注册接口提交完整申请
4. 后端验证数据并保存到数据库
5. 返回申请编号和审核状态
6. 前端展示提交成功页面

## 注意事项

1. 确保所有必填字段的验证逻辑完整
2. 文件上传需要支持多种格式（jpg、png、pdf等）
3. 考虑并发注册的情况，避免重复数据
4. 提供注册状态查询接口，方便用户跟踪进度
5. 考虑审核流程的后续扩展，预留状态字段
