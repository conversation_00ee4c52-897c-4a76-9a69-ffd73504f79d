# 传能家政小程序机构端后端接口开发提示词

## 项目背景

基于传能家政小程序机构端功能需求，当用户在"我的"页面点击"我是机构"选项时，需要根据当前登录用户的手机号获取对应的机构员工信息，实现用户身份切换功能。

## 核心需求分析

### 1. 机构身份切换模块核心需求
- **用户身份识别**: 根据当前登录用户的手机号识别是否为机构员工
- **机构信息获取**: 获取机构员工的基本信息，包括姓名、职位、所属机构等
- **权限验证**: 验证用户是否具有机构端访问权限
- **身份切换**: 从普通用户身份切换到机构员工身份

### 2. 数据展示需求
- **基本信息展示**: 显示员工姓名、手机号、职位、所属机构
- **机构信息展示**: 显示机构名称、机构类型等
- **权限信息**: 显示员工在机构中的权限范围

## 数据库表结构分析

### 核心表关系
1. **用户信息表** (`system_users`)
   - 主键: `id`
   - 账户类型: `account_type` (1-内部员工，2-企业用户)
   - 关键字段: `mobile`, `nickname`, `dept_id`, `post_ids`, `agency_id`, `agency_name`
   - 关联字段: `partner_id`, `partner_name`

## 具体接口设计

### 1. 根据手机号获取机构用户信息接口

**接口路径**: `/system/user/get-by-mobile`
**请求方式**: GET
**功能描述**: 根据手机号获取机构员工信息，用于身份切换

**请求参数**:
```json
{
  "mobile": "***********"  // 手机号码（必填）
}
```

**响应数据结构**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "userId": 123,
    "accountType": 2,
    "username": "zhang_manager",
    "nickname": "张经理",
    "mobile": "138****5678",
    "avatar": "https://example.com/avatar.jpg",
    "position": "店长",
    "department": "运营部",
    "agencyId": 456,
    "agencyName": "XX家政服务有限公司",
    "partnerId": 789,
    "partnerName": "XX合作伙伴",
    "status": 0,
    "workStatusType": 1,
    "permissions": ["order_manage", "staff_manage"],
    "lastLoginTime": "2024-01-15 10:30:00"
  }
}
```


**关键SQL查询**:
```sql
-- 根据手机号查询用户信息
SELECT id, account_type, username, nickname, mobile, avatar, 
       dept_id, post_ids, agency_id, agency_name, partner_id, partner_name,
       status, work_status_type, login_date
FROM system_users 
WHERE mobile = ? AND deleted = 0;

```

**错误处理**:
```java
// 手机号格式验证
if (!MobileUtil.isMobile(mobile)) {
    return error("手机号格式不正确");
}

// 用户不存在
if (user == null) {
    return error("该手机号未注册机构账号");
}

// 非机构用户
if (user.getAccountType() != 2) {
    return error("该账号不是机构用户，无法切换到机构端");
}

// 账号被停用
if (user.getStatus() != 0) {
    return error("账号已被停用，请联系管理员");
}

// 机构信息缺失
if (user.getAgencyId() == null) {
    return error("用户未关联机构，请联系管理员");
}
```

## 数据模型定义

### 1. 机构用户信息VO
```java
@Data
public class AgencyUserInfoVO {
    private Long userId;           // 用户ID
    private Integer accountType;   // 账户类型
    private String username;       // 用户账号
    private String nickname;       // 用户昵称
    private String mobile;         // 手机号（脱敏）
    private String avatar;         // 头像地址
    private String position;       // 职位
    private String department;     // 部门
    private Long agencyId;         // 机构ID
    private String agencyName;     // 机构名称
    private Long partnerId;        // 合作伙伴ID
    private String partnerName;    // 合作伙伴名称
    private Integer status;        // 账号状态
    private Integer workStatusType; // 工作状态类型
    private List<String> permissions; // 权限列表
    private Date lastLoginTime;    // 最后登录时间
}
```

## 工具方法

### 1. 手机号脱敏
```java
public static String maskMobile(String mobile) {
    if (StringUtils.isEmpty(mobile) || mobile.length() != 11) {
        return mobile;
    }
    return mobile.substring(0, 3) + "****" + mobile.substring(7);
}
```

### 2. 获取用户权限
```java
private List<String> getUserPermissions(Long userId) {
    // 根据用户ID获取权限列表
    // 这里需要根据实际的权限系统实现
    return Arrays.asList("order_manage", "staff_manage", "finance_view");
}
```

## 安全考虑

1. **手机号验证**: 确保手机号格式正确且为当前登录用户
2. **权限验证**: 验证用户是否具有机构端访问权限
3. **数据脱敏**: 敏感信息如手机号需要脱敏处理
4. **状态检查**: 验证用户和机构状态是否正常
5. **日志记录**: 记录身份切换操作日志

## 接口调用流程

1. 前端在"我的"页面点击"我是机构"
2. 调用`/agency/user/info`接口，传入当前用户手机号
3. 后端验证用户身份和权限
4. 返回机构用户信息
5. 前端根据返回信息更新页面显示
6. 切换到机构端功能模块

## 注意事项

1. 确保接口返回的数据格式与前端页面显示需求一致
2. 手机号脱敏处理，保护用户隐私
3. 权限验证要严格，防止越权访问
4. 错误信息要友好，便于用户理解
5. 考虑并发访问情况，确保数据一致性
