# 雇主端订单列表接口文档

## 1. 接口概述

本文档描述了雇主端查看订单列表相关的接口，包括订单基本信息、状态筛选、分页查询等功能。

## 2. 接口列表

### 2.1 获取订单列表

**接口地址：** `/publicbiz/employer/order/list`

**请求方式：** GET

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| customerOpenId | string | 是 | 客户OpenId |
| status | string | 否 | 订单状态筛选：all-全部/pending_payment-待付款/executing-进行中/completed-已完成/cancelled-已取消 |
| page | integer | 否 | 页码，默认1 |
| size | integer | 否 | 每页数量，默认10，最大50 |

**请求示例：**
```
GET /publicbiz/employer/order/list?customerOpenId=12345678-1234-1234-1234-123456789012&status=all&page=1&size=10
```

**响应参数：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 25,
    "pages": 3,
    "current": 1,
    "size": 10,
    "records": [
      {
        "id": 1,
        "orderNo": "DO202412010001",
        "orderType": "domestic",
        "businessLine": "家政服务",
        "totalAmount": "340.00",
        "paidAmount": "0.00",
        "refundAmount": "0.00",
        "paymentStatus": "pending",
        "orderStatus": "pending_payment",
        "createTime": "2024-12-01 10:30:00",
        "updateTime": "2024-12-01 10:30:00",
        "domesticOrder": {
          "id": 1,
          "customerName": "张女士",
          "customerPhone": "138****8888",
          "customerAddress": "北京市朝阳区",
          "serviceCategoryName": "日常保洁",
          "servicePackageName": "4次日常保洁 (3小时)",
          "servicePackageThumbnail": "https://example.com/image1.jpg",
          "servicePackagePrice": "340.00",
          "servicePackageOriginalPrice": "400.00",
          "servicePackageUnit": "次",
          "servicePackageDuration": "3小时",
          "servicePackageType": "count-card",
          "serviceTimes": 4,
          "unitPrice": "85.00",
          "actualAmount": "340.00",
          "serviceAddress": "北京市朝阳区某某小区",
          "serviceAddressDetail": "1号楼1单元101室",
          "practitionerName": "王阿姨",
          "practitionerPhone": "139****9999",
          "agencyName": "金牌家政",
          "taskCount": 4,
          "completedTaskCount": 0,
          "taskProgress": "0.00",
          "serviceStartDate": "2024-12-02",
          "serviceEndDate": "2024-12-31"
        }
      }
    ],
    "statusCounts": {
      "all": 25,
      "pending_payment": 3,
      "executing": 8,
      "completed": 12,
      "cancelled": 2
    }
  }
}
```

## 3. 订单状态说明

### 3.1 支付状态 (paymentStatus)
- pending: 待支付
- paid: 已支付
- refunded: 已退款
- cancelled: 已取消

### 3.2 订单状态 (orderStatus)
- pending_payment: 待支付
- executing: 执行中（进行中）
- completed: 已完成
- cancelled: 已取消

## 4. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 404 | 客户信息不存在 |
| 500 | 服务器内部错误 |
