# 创建服务套餐订单接口文档

## 1. 接口概述

### 1.1 接口说明
创建服务套餐订单，支持长周期套餐和次卡套餐两种类型。

### 1.2 接口地址
```
POST /publicbiz/employer/order/create
```

### 1.3 请求方式
POST

### 1.4 接口描述
- 支持长周期套餐：需要指定服务开始日期、每日服务时间、服务天数
- 支持次卡套餐：需要指定具体的服务时间列表
- 自动计算订单金额和优惠信息
- 支持支付方式选择和支付保障选择

## 2. 请求参数

### 2.1 请求头
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| Authorization | string | 是 | Bearer Token，用户登录凭证 |
| Content-Type | string | 是 | application/json |

### 2.2 请求体参数

#### 2.2.1 基础参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| serviceId | integer | 是 | 服务套餐ID |
| packageType | string | 是 | 套餐类型：long-term(长周期)、count-card(次卡) |
| totalAmount | decimal | 是 | 订单总金额 |
| paymentMethod | string | 是 | 支付方式：wechat(微信支付) |
| protectionType | string | 是 | 支付保障：secure(安心付)、direct(直接付) |
| remark | string | 否 | 备注信息，最大200字符 |

#### 2.2.2 用户信息
| 参数名       | 类型 | 必填 | 说明 |
|-----------|------|------|------|
| userPhone | string | 是 | 提交人手机号（当前授权雇主手机号） |
| addressId | integer | 是 | 服务地址ID |
| openId    | integer | 是 | 雇主openId |
#### 2.2.3 套餐信息
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| serviceInfo | object | 是 | 套餐详细信息 |
| serviceInfo.title | string | 是 | 套餐标题 |
| serviceInfo.description | string | 是 | 套餐描述 |
| serviceInfo.price | decimal | 是 | 套餐价格 |
| serviceInfo.originalPrice | decimal | 是 | 原价 |
| serviceInfo.agencyName | string | 是 | 机构名称 |
| serviceInfo.agencyId | integer | 是 | 机构ID |

#### 2.2.4 长周期套餐参数（packageType=long-term时必填）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| startDate | string | 是 | 服务开始日期，格式：YYYY-MM-DD |
| dailyTime | string | 是 | 每日服务时间，格式：HH:MM-HH:MM |
| serviceDays | integer | 是 | 服务天数 |

#### 2.2.5 次卡套餐参数（packageType=count-card时必填）
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| serviceTimes | array | 是 | 服务时间列表，格式：["YYYY-MM-DD HH:MM"] |
| serviceCount | integer | 是 | 已选择的服务次数 |

### 2.3 请求示例

#### 长周期套餐示例
```json
{
  "serviceId": 1001,
  "packageType": "long-term",
  "totalAmount": 2999.00,
  "paymentMethod": "wechat",
  "protectionType": "secure",
  "remark": "请准时到达，谢谢",
  "userPhone": "13800138000",
  "addressId": 2001,
   "openId": "5dcf5c33d4f64175a02d70c00d96db25",
  "serviceInfo": {
    "title": "26天深度保洁套餐",
    "description": "26天 | 每天1次 | 每次2小时",
    "price": 2999.00,
    "originalPrice": 3999.00,
    "agencyName": "成都保洁服务有限公司",
    "agencyId": 5001
  },
  "startDate": "2025-01-15",
  "dailyTime": "09:00-11:00",
  "serviceDays": 26
}
```

#### 次卡套餐示例
```json
{
  "serviceId": 1002,
  "packageType": "count-card",
  "totalAmount": 599.00,
  "paymentMethod": "wechat",
  "protectionType": "secure",
  "remark": "请提前联系",
  "userPhone": "13800138000",
  "addressId": 2001,
  "serviceInfo": {
    "title": "3次深度保洁套餐",
    "description": "服务次数3次 | 有效期90天",
    "price": 599.00,
    "originalPrice": 799.00,
    "agencyName": "成都保洁服务有限公司",
    "agencyId": 5001
  },
  "serviceTimes": [
    "2025-01-15 09:00",
    "2025-01-20 14:00",
    "2025-01-25 18:00"
  ],
  "serviceCount": 3
}
```

## 3. 响应参数

### 3.1 响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "orderId": "202501150001",
    "orderNo": "SP202501150001",
    "status": "pending_payment",
    "createTime": "2025-01-15 10:30:00",
    "payUrl": "https://pay.example.com/order/123456"
  }
}
```

### 3.2 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | integer | 响应状态码，0表示成功 |
| msg | string | 响应消息 |
| data | object | 响应数据 |
| data.orderId | string | 订单ID |
| data.orderNo | string | 订单编号 |
| data.status | string | 订单状态：pending_payment(待支付)、paid(已支付)、cancelled(已取消) |
| data.createTime | string | 创建时间 |
| data.payUrl | string | 支付链接（可选） |

### 3.3 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权，请重新登录 |
| 403 | 权限不足 |
| 404 | 服务套餐不存在 |
| 409 | 地址信息不存在 |
| 422 | 参数验证失败 |
| 500 | 服务器内部错误 |

## 4. 业务规则

### 4.1 订单创建规则
1. 用户必须已登录且身份为雇主
2. 服务套餐必须存在且状态为有效
3. 服务地址必须存在且属于当前用户
4. 长周期套餐的服务开始日期不能早于当前日期
5. 次卡套餐的服务时间不能早于当前时间
6. 订单金额必须与套餐价格一致

### 4.2 时间验证规则
1. 长周期套餐：
   - 服务开始日期不能早于当前日期
   - 每日服务时间格式必须为 HH:MM-HH:MM
   - 服务天数必须大于0

2. 次卡套餐：
   - 服务时间不能早于当前时间
   - 服务时间格式必须为 YYYY-MM-DD HH:MM
   - 已选择的服务次数必须大于0且不超过套餐总次数

### 4.3 支付规则
1. 支持微信支付方式
2. 支付保障类型：
   - 安心付：平台代管费用，服务完成后支付给服务方
   - 直接付：费用直接付给服务方
3. 订单创建后15分钟内未支付自动取消

### 4.4 地址验证规则
1. 地址必须包含完整的省市区信息
2. 地址必须包含详细的街道门牌号
3. 收货人姓名和手机号不能为空
4. 手机号格式必须正确

## 5. 注意事项

1. 所有接口需要验证用户登录状态
2. 用户手机号必须是当前授权雇主的手机号
3. 订单创建成功后，用户需要在规定时间内完成支付
4. 长周期套餐的服务时间需要根据套餐详情中的serviceTimespan字段进行验证
5. 次卡套餐的服务次数不能超过套餐规定的总次数
6. 订单金额计算需要考虑优惠券、积分等优惠信息
7. 支付成功后需要更新订单状态并发送通知

## 6. 相关接口

- 获取服务套餐详情：`GET /publicbiz/employer/service-package/detail`
- 获取用户地址列表：`GET /publicbiz/employer/address/list`
- 获取用户信息：`GET /publicbiz/employer/mpuser/getUserInfo`
- 订单支付：`POST /publicbiz/employer/order/pay`（详细文档：[订单支付接口文档](./订单支付接口文档.md)）
- 查询订单状态：`GET /publicbiz/employer/order/detail`

