# 服务地址接口文档

## 数据库表结构

### 用户地址表 (mp_user_address)
```sql
-- 用户地址表
-- 用于存储用户的收货地址信息
CREATE TABLE `mp_user_address` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `label` varchar(50) NOT NULL COMMENT '地址标签，如：家庭地址、公司地址等',
  `receiver_name` varchar(50) DEFAULT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(20) DEFAULT NULL COMMENT '收货人手机号',
  `province_code` varchar(20) DEFAULT NULL COMMENT '省份编码',
  `province_name` varchar(50) DEFAULT NULL COMMENT '省份名称',
  `city_code` varchar(20) DEFAULT NULL COMMENT '城市编码',
  `city_name` varchar(50) DEFAULT NULL COMMENT '城市名称',
  `district_code` varchar(20) DEFAULT NULL COMMENT '区县编码',
  `district_name` varchar(50) DEFAULT NULL COMMENT '区县名称',
  `region` varchar(200) DEFAULT NULL COMMENT '省市区组合，如：四川省成都市锦江区',
  `address` varchar(500) NOT NULL COMMENT '详细地址',
  `full_address` varchar(700) DEFAULT NULL COMMENT '完整地址（省市区+详细地址）',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `is_default` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否默认地址：0-否，1-是',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户地址表';
```

### 用户表 (mp_user)
```sql
CREATE TABLE `mp_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `oneid` char(36) CHARACTER SET utf8mb4 NOT NULL COMMENT 'OneID GUID',
  `openid` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户标识',
  `union_id` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信生态唯一标识',
  `subscribe_status` tinyint(4) NOT NULL COMMENT '关注状态',
  `subscribe_time` datetime NOT NULL COMMENT '关注时间',
  `mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号（仅小程序使用）',
  `nickname` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `head_image_url` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像地址',
  `unsubscribe_time` datetime DEFAULT NULL COMMENT '取消关注时间',
  `language` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '语言',
  `country` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国家',
  `province` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '省份',
  `city` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '城市',
  `remark` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `tag_ids` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签编号数组',
  `account_id` bigint(20) NOT NULL COMMENT '微信公众号ID',
  `app_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '微信公众号 appid',
  `creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信用户表（小程序+公众号）';
```

**关联关系：** `mp_user` 表的 `id` 关联 `mp_user_address` 的 `user_id`

## 接口文档

### 1. 获取用户地址列表

**接口地址：** `GET /publicbiz/employer/address/list`

**请求参数：**
```json
{
  "page": 1,           // 页码，默认1
  "size": 10,          // 每页数量，默认10
  "status": 1          // 状态筛选：1-启用，0-禁用，不传则查询所有
}
```

**响应数据：**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "total": 3,
    "records": [
      {
        "id": 1,
        "user_id": 1,
        "label": "家庭地址",
        "receiver_name": "李四",
        "receiver_phone": "13900139001",
        "province_code": "510000",
        "province_name": "四川省",
        "city_code": "510100",
        "city_name": "成都市",
        "district_code": "510104",
        "district_name": "锦江区",
        "region": "四川省成都市锦江区",
        "address": "春熙路1号太古里购物中心2楼201室",
        "full_address": "四川省成都市锦江区春熙路1号太古里购物中心2楼201室",
        "longitude": 104.0800000,
        "latitude": 30.6500000,
        "is_default": 1,
        "status": 1,
        "sort": 1,
        "remark": null,
        "create_time": "2024-01-01 10:00:00",
        "update_time": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

### 2. 获取地址详情

**接口地址：** `GET /publicbiz/employer/address/detail/{id}`

**路径参数：**
- `id`: 地址ID

**响应数据：**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "label": "家庭地址",
    "receiver_name": "李四",
    "receiver_phone": "13900139001",
    "province_code": "510000",
    "province_name": "四川省",
    "city_code": "510100",
    "city_name": "成都市",
    "district_code": "510104",
    "district_name": "锦江区",
    "region": "四川省成都市锦江区",
    "address": "春熙路1号太古里购物中心2楼201室",
    "full_address": "四川省成都市锦江区春熙路1号太古里购物中心2楼201室",
    "longitude": 104.0800000,
    "latitude": 30.6500000,
    "is_default": 1,
    "status": 1,
    "sort": 1,
    "remark": null,
    "create_time": "2024-01-01 10:00:00",
    "update_time": "2024-01-01 10:00:00"
  }
}
```

### 3. 新增地址

**接口地址：** `POST /publicbiz/employer/address/create`

**请求参数：**
```json
{
  "label": "家庭地址",                    // 地址标签，必填
  "receiver_name": "李四",               // 收货人姓名，必填
  "receiver_phone": "13900139001",      // 收货人手机号，必填
  "province_code": "510000",            // 省份编码，必填
  "province_name": "四川省",             // 省份名称，必填
  "city_code": "510100",                // 城市编码，必填
  "city_name": "成都市",                 // 城市名称，必填
  "district_code": "510104",            // 区县编码，必填
  "district_name": "锦江区",             // 区县名称，必填
  "region": "四川省成都市锦江区",         // 省市区组合，必填
  "address": "春熙路1号太古里购物中心2楼201室", // 详细地址，必填
  "longitude": 104.0800000,             // 经度，可选
  "latitude": 30.6500000,               // 纬度，可选
  "is_default": 1,                      // 是否默认地址：0-否，1-是，默认0
  "sort": 1,                            // 排序，可选
  "remark": "备注信息"                   // 备注，可选
}
```

**响应数据：**
```json
{
  "code": 0,
  "msg": "新增地址成功",
  "data": {
    "id": 1
  }
}
```

### 4. 更新地址

**接口地址：** `POST /publicbiz/employer/address/update/{id}`

**路径参数：**
- `id`: 地址ID

**请求参数：**
```json
{
  "label": "家庭地址",                    // 地址标签，必填
  "receiver_name": "李四",               // 收货人姓名，必填
  "receiver_phone": "13900139001",      // 收货人手机号，必填
  "province_code": "510000",            // 省份编码，必填
  "province_name": "四川省",             // 省份名称，必填
  "city_code": "510100",                // 城市编码，必填
  "city_name": "成都市",                 // 城市名称，必填
  "district_code": "510104",            // 区县编码，必填
  "district_name": "锦江区",             // 区县名称，必填
  "region": "四川省成都市锦江区",         // 省市区组合，必填
  "address": "春熙路1号太古里购物中心2楼201室", // 详细地址，必填
  "longitude": 104.0800000,             // 经度，可选
  "latitude": 30.6500000,               // 纬度，可选
  "is_default": 1,                      // 是否默认地址：0-否，1-是
  "sort": 1,                            // 排序，可选
  "remark": "备注信息"                   // 备注，可选
}
```

**响应数据：**
```json
{
  "code": 0,
  "msg": "更新地址成功",
  "data": null
}
```

### 5. 删除地址

**接口地址：** `POST /publicbiz/employer/address/delete/{id}`

**路径参数：**
- `id`: 地址ID

**响应数据：**
```json
{
  "code": 0,
  "msg": "删除地址成功",
  "data": null
}
```

### 6. 设置默认地址

**接口地址：** `POST /publicbiz/employer/address/set-default/{id}`

**路径参数：**
- `id`: 地址ID

**响应数据：**
```json
{
  "code": 0,
  "msg": "设置默认地址成功",
  "data": null
}
```

### 7. 获取用户默认地址

**接口地址：** `GET /publicbiz/employer/address/default`

**响应数据：**
```json
{
  "code": 0,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "user_id": 1,
    "label": "家庭地址",
    "receiver_name": "李四",
    "receiver_phone": "13900139001",
    "province_code": "510000",
    "province_name": "四川省",
    "city_code": "510100",
    "city_name": "成都市",
    "district_code": "510104",
    "district_name": "锦江区",
    "region": "四川省成都市锦江区",
    "address": "春熙路1号太古里购物中心2楼201室",
    "full_address": "四川省成都市锦江区春熙路1号太古里购物中心2楼201室",
    "longitude": 104.0800000,
    "latitude": 30.6500000,
    "is_default": 1,
    "status": 1,
    "sort": 1,
    "remark": null,
    "create_time": "2024-01-01 10:00:00",
    "update_time": "2024-01-01 10:00:00"
  }
}
```

### 8. 批量删除地址

**接口地址：** `POST /publicbiz/employer/address/batch-delete`

**请求参数：**
```json
{
  "ids": [1, 2, 3]  // 地址ID数组
}
```

**响应数据：**
```json
{
  "code": 0,
  "msg": "批量删除地址成功",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 操作成功 |
| 1001 | 参数错误 |
| 1002 | 用户未登录 |
| 1003 | 地址不存在 |
| 1004 | 地址不属于当前用户 |
| 1005 | 地址标签不能为空 |
| 1006 | 收货人姓名不能为空 |
| 1007 | 收货人手机号格式错误 |
| 1008 | 省市区信息不能为空 |
| 1009 | 详细地址不能为空 |
| 1010 | 用户地址数量已达上限 |
| 1011 | 默认地址设置失败 |

## 业务规则

1. **用户权限验证**：所有接口都需要验证当前登录用户是否有权限操作该地址
2. **默认地址规则**：每个用户只能有一个默认地址，设置新的默认地址时会自动取消其他地址的默认状态
3. **地址数量限制**：建议每个用户最多保存20个地址
4. **数据完整性**：删除地址时进行逻辑删除，不物理删除数据
5. **地址验证**：新增和更新地址时需要验证地址信息的完整性