# 雇主端订单详情接口文档

## 1. 接口概述

本文档描述了雇主端查看订单详情相关的接口，包括订单基本信息、服务状态、服务人员信息、服务详情、价格明细等功能。

## 2. 接口列表

### 2.1 获取订单详情

**接口地址：** `/publicbiz/employer/order/detail`

**请求方式：** GET

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| orderId | string | 是 | 订单ID |

**请求示例：**
```
GET /publicbiz/employer/order/detail?orderId=123
```

**响应参数：**

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "orderId": 123,
    "orderNo": "DO202412010001",
    "orderType": "domestic",
    "businessLine": "家政服务",
    "orderStatus": "executing",
    "paymentStatus": "paid",
    "totalAmount": "150.00",
    "paidAmount": "140.00",
    "refundAmount": "0.00",
    "createTime": "2024-12-19 14:00:09",
    "updateTime": "2024-12-19 14:00:09",
    "remarks": "家里有狗",
    "serviceAddress": "杨浦区莲花小区",
    "serviceAddressDetail": "1号楼1单元101室",
    "domesticOrder": {
      "id": 1,
      "customerName": "张女士",
      "customerPhone": "138****8888",
      "serviceCategoryName": "日常保洁",
      "servicePackageName": "3次日常保洁 (3小时)",
      "servicePackageThumbnail": "https://example.com/image1.jpg",
      "servicePackagePrice": "150.00",
      "servicePackageOriginalPrice": "160.00",
      "servicePackageUnit": "次",
      "servicePackageDuration": "3小时",
      "servicePackageType": "count-card",
      "serviceTimes": 3,
      "unitPrice": "50.00",
      "actualAmount": "140.00",
      "practitionerName": "李阿姨",
      "practitionerPhone": "139****9999",
      "practitionerAvatar": "https://example.com/avatar.jpg",
      "practitionerBadge": "金牌阿姨",
      "practitionerExperience": "5年经验",
      "practitionerFamilies": "服务15个家庭",
      "agencyName": "金牌家政",
      "agencyIcon": "https://example.com/agency-icon.png",
      "taskCount": 3,
      "completedTaskCount": 1,
      "taskProgress": "33.33",
      "serviceStartDate": "2024-12-19",
      "serviceEndDate": "2024-12-31",
      "serviceSchedule": [
        {
          "taskIndex": 1,
          "taskLabel": "第1次",
          "scheduledTime": "2024-12-19 14:00-17:00",
          "actualTime": "2024-12-19 14:00-17:00",
          "status": "completed",
          "practitionerName": "李阿姨",
          "practitionerPhone": "139****9999"
        },
        {
          "taskIndex": 2,
          "taskLabel": "第2次",
          "scheduledTime": "2024-12-26 14:00-17:00",
          "actualTime": null,
          "status": "scheduled",
          "practitionerName": "李阿姨",
          "practitionerPhone": "139****9999"
        },
        {
          "taskIndex": 3,
          "taskLabel": "第3次",
          "scheduledTime": "2024-12-31 14:00-17:00",
          "actualTime": null,
          "status": "scheduled",
          "practitionerName": "李阿姨",
          "practitionerPhone": "139****9999"
        }
      ]
    },
    "progressSteps": [
      {
        "step": "payment",
        "stepName": "已支付",
        "status": "completed",
        "completedTime": "2024-12-19 14:00:09"
      },
      {
        "step": "accepted",
        "stepName": "已接单",
        "status": "completed",
        "completedTime": "2024-12-19 14:05:30"
      },
      {
        "step": "executing",
        "stepName": "服务中",
        "status": "current",
        "startedTime": "2024-12-19 14:00:00"
      },
      {
        "step": "evaluation",
        "stepName": "待评价",
        "status": "pending"
      }
    ]
  }
}
```

## 3. 数据结构说明

### 3.1 订单状态 (orderStatus)
- pending_payment: 待支付
- executing: 执行中（服务中）
- completed: 已完成
- cancelled: 已取消

### 3.2 支付状态 (paymentStatus)
- pending: 待支付
- paid: 已支付
- refunded: 已退款
- cancelled: 已取消

### 3.3 服务包类型 (servicePackageType)
- count-card: 次卡
- time-card: 时卡
- single: 单次服务

### 3.4 任务状态 (serviceSchedule.status)
- scheduled: 已安排
- in_progress: 进行中
- completed: 已完成
- cancelled: 已取消

### 3.5 进度步骤状态 (progressSteps.status)
- completed: 已完成
- current: 当前步骤
- pending: 待处理

## 4. 字段说明

### 4.1 基础订单信息
- `orderId`: 订单ID
- `orderNo`: 订单编号
- `orderType`: 订单类型（domestic-家政服务）
- `businessLine`: 业务线
- `orderStatus`: 订单状态
- `paymentStatus`: 支付状态
- `totalAmount`: 订单总金额
- `paidAmount`: 实付金额
- `refundAmount`: 退款金额
- `createTime`: 下单时间
- `updateTime`: 更新时间
- `remarks`: 备注信息
- `serviceAddress`: 服务地址
- `serviceAddressDetail`: 详细地址

### 4.2 服务人员信息
- `practitionerName`: 服务人员姓名
- `practitionerPhone`: 服务人员电话
- `practitionerAvatar`: 服务人员头像
- `practitionerBadge`: 服务人员标签（如：金牌阿姨）
- `practitionerExperience`: 服务人员经验
- `practitionerFamilies`: 服务家庭数

### 4.3 服务包信息
- `serviceCategoryName`: 服务分类名称
- `servicePackageName`: 服务包名称
- `servicePackageThumbnail`: 服务包缩略图
- `servicePackagePrice`: 服务包价格
- `servicePackageOriginalPrice`: 服务包原价
- `servicePackageUnit`: 服务包单位
- `servicePackageDuration`: 服务包时长
- `servicePackageType`: 服务包类型
- `serviceTimes`: 服务次数
- `unitPrice`: 单价
- `actualAmount`: 实际金额

### 4.4 机构信息
- `agencyName`: 机构名称
- `agencyIcon`: 机构图标

### 4.5 服务进度
- `taskCount`: 总任务数
- `completedTaskCount`: 已完成任务数
- `taskProgress`: 任务进度百分比
- `serviceStartDate`: 服务开始日期
- `serviceEndDate`: 服务结束日期

### 4.6 服务安排
- `taskIndex`: 任务序号
- `taskLabel`: 任务标签（如：第1次）
- `scheduledTime`: 计划服务时间
- `actualTime`: 实际服务时间
- `status`: 任务状态

## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 404 | 订单不存在 |
| 403 | 无权限查看该订单 |
| 500 | 服务器内部错误 |

## 6. 业务规则

1. **权限控制**：只能查看属于当前用户的订单
2. **状态更新**：订单状态会根据服务进度自动更新
3. **进度计算**：任务进度 = (已完成任务数 / 总任务数) × 100%
4. **时间格式**：所有时间字段使用 "YYYY-MM-DD HH:mm:ss" 格式
5. **金额格式**：所有金额字段使用字符串格式，保留2位小数
