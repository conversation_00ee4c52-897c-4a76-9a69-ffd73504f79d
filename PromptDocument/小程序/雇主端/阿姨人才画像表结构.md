CREATE TABLE `publicbiz_practitioner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `aunt_oneid` varchar(36) NOT NULL DEFAULT '' COMMENT '阿姨OneID GUID',
  `name` varchar(50) NOT NULL COMMENT '阿姨姓名',
  `phone` varchar(20) NOT NULL COMMENT '手机号，用于登录',
  `id_card` varchar(18) NOT NULL COMMENT '身份证号',
  `hometown` varchar(100) DEFAULT NULL COMMENT '籍贯，如：四川成都',
  `age` int(11) DEFAULT NULL COMMENT '年龄',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别：male-男/female-女',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `service_type` varchar(50) NOT NULL COMMENT '主要服务类型：月嫂/育儿嫂/保洁/护工',
  `experience_years` int(11) NOT NULL COMMENT '从业年限',
  `platform_status` varchar(20) NOT NULL DEFAULT 'cooperating' COMMENT '平台状态：cooperating-合作中/terminated-已解约',
  `rating` decimal(2,1) NOT NULL DEFAULT '4.5' COMMENT '评级，1.0-5.0',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `agency_name` varchar(200) DEFAULT NULL COMMENT '所属机构名称',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active-正常/inactive-停用/pending-待审核',
  `current_status` varchar(50) DEFAULT NULL COMMENT '当前状态：服务中/待岗/休假中',
  `current_order_id` varchar(50) DEFAULT NULL COMMENT '当前服务订单ID',
  `total_orders` int(11) NOT NULL DEFAULT '0' COMMENT '累计服务单数',
  `total_income` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '累计收入',
  `customer_satisfaction` decimal(3,1) DEFAULT NULL COMMENT '客户满意度评分',
  `profile` text COMMENT '阿姨简介，个人描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COMMENT='阿姨基本信息表';

CREATE TABLE `publicbiz_practitioner_qualification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `practitioner_oneId` varchar(36) NOT NULL COMMENT '阿姨oneID',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型：id_card-身份证/health_cert-健康证/skill_cert-专业技能证书/other-其他附件',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `file_url` varchar(500) NOT NULL COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阿姨资质文件表';

CREATE TABLE `talent_user_tag` (
  `user_tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '关联用户ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID，关联标签库',
  `tag_type_id` bigint(20) NOT NULL COMMENT '标签类型ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`user_tag_id`)
) ENGINE=InnoDB AUTO_INCREMENT=132 DEFAULT CHARSET=utf8mb4 COMMENT='用户标签关联表';


CREATE TABLE `publicbiz_practitioner_qualification` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `practitioner_oneId` varchar(36) NOT NULL COMMENT '阿姨oneID',
  `file_type` varchar(50) NOT NULL COMMENT '文件类型：id_card-身份证/health_cert-健康证/skill_cert-专业技能证书/other-其他附件',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `file_url` varchar(500) NOT NULL COMMENT '文件URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小（字节）',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序，数字越小越靠前',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-有效，0-无效',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='阿姨资质文件表';


CREATE TABLE `publicbiz_aunt_review` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tenant_id` bigint(20) DEFAULT '1' COMMENT '租户ID',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `aunt_id` bigint(20) NOT NULL COMMENT '阿姨OneID',
  `reviewer_id` bigint(20) NOT NULL COMMENT '评价人用户ID',
  `reviewer_name` varchar(50) NOT NULL COMMENT '评价人姓名',
  `reviewer_avatar` varchar(500) DEFAULT NULL COMMENT '评价人头像URL',
  `rating` decimal(2,1) NOT NULL COMMENT '评分：1.0-5.0',
  `review_tags` text COMMENT '评价标签（JSON格式，如：["专业","细心","守时"]）',
  `review_content` text COMMENT '评价内容',
  `review_images` text COMMENT '评价图片URL列表（JSON格式）',
  `review_type` varchar(20) DEFAULT 'service' COMMENT '评价类型：service-服务评价，attitude-态度评价，professional-专业评价',
  `is_anonymous` tinyint(1) DEFAULT '0' COMMENT '是否匿名评价：0-否，1-是',
  `is_recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐：0-否，1-是',
  `like_count` int(11) DEFAULT '0' COMMENT '点赞数',
  `reply_content` text COMMENT '回复内容',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-隐藏，1-显示',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `service_package_id` bigint(20) DEFAULT NULL COMMENT '服务套餐ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='阿姨评价表';




CREATE TABLE `talent_user` (
  `user_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `identity_id` varchar(18) NOT NULL COMMENT '身份证号',
  `birth_date` date NOT NULL COMMENT '出生日期',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别（可选值：男、女、其他）',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `status` varchar(30) DEFAULT NULL COMMENT 'OneID状态（可选值：正常、待合并、已禁用）',
  `register_source` varchar(50) DEFAULT NULL COMMENT '用户来源',
  `oneid` char(36) NOT NULL DEFAULT '' COMMENT 'OneID GUID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `completeness` tinyint(4) DEFAULT '0' COMMENT '档案完整度百分比',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  `org_id` bigint(20) DEFAULT NULL COMMENT '所属机构ID',
  `org_name` varchar(200) DEFAULT NULL COMMENT '所属机构名称',
  `talent_source` varchar(100) DEFAULT NULL COMMENT '人才来源',
  `is_self_support` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否为平台自营（0-否，1-是）',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=64 DEFAULT CHARSET=utf8mb4 COMMENT='平台用户主表';

CREATE TABLE `talent_user_tag` (
  `user_tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `user_id` bigint(20) NOT NULL COMMENT '关联用户ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID，关联标签库',
  `tag_type_id` bigint(20) NOT NULL COMMENT '标签类型ID',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`user_tag_id`)
) ENGINE=InnoDB AUTO_INCREMENT=132 DEFAULT CHARSET=utf8mb4 COMMENT='用户标签关联表';

CREATE TABLE `talent_tag_type` (
  `tag_type_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签类型ID',
  `type_code` varchar(50) NOT NULL COMMENT '标签类型编码',
  `type_name` varchar(100) NOT NULL COMMENT '标签类型名称',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`tag_type_id`),
  UNIQUE KEY `type_code` (`type_code`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='标签类型表';

CREATE TABLE `talent_tag_library` (
  `tag_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `tag_type_id` bigint(20) NOT NULL COMMENT '标签类型ID',
  `tag_code` varchar(50) NOT NULL COMMENT '标签编码',
  `tag_name` varchar(100) NOT NULL COMMENT '标签名称',
  `description` varchar(255) DEFAULT NULL COMMENT '标签描述',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',
  PRIMARY KEY (`tag_id`),
  UNIQUE KEY `tag_code` (`tag_code`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8mb4 COMMENT='标签库表';






CREATE TABLE `publicbiz_domestic_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键，自增',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '租户ID',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL COMMENT '订单号',
  `customer_oneid` varchar(50) NOT NULL DEFAULT '' COMMENT '客户OneID GUID',
  `customer_name` varchar(50) NOT NULL COMMENT '客户姓名',
  `customer_phone` varchar(20) NOT NULL COMMENT '客户电话',
  `customer_address` varchar(500) NOT NULL COMMENT '服务地址',
  `customer_remark` varchar(2000) DEFAULT NULL COMMENT '客户备注',
  `service_category_id` bigint(20) NOT NULL COMMENT '服务分类ID',
  `service_category_name` varchar(255) NOT NULL COMMENT '服务分类名称',
  `service_package_id` bigint(20) DEFAULT NULL COMMENT '服务套餐ID',
  `service_package_name` varchar(200) DEFAULT NULL COMMENT '服务套餐名称',
  `service_start_date` date DEFAULT NULL COMMENT '服务开始日期',
  `service_end_date` date DEFAULT NULL COMMENT '服务结束日期',
  `service_duration` varchar(50) DEFAULT NULL COMMENT '服务时长',
  `service_frequency` varchar(50) DEFAULT NULL COMMENT '服务频次',
  `service_package_thumbnail` varchar(500) DEFAULT NULL COMMENT '套餐主图URL',
  `service_package_price` decimal(10,2) NOT NULL COMMENT '套餐价格',
  `service_package_original_price` decimal(10,2) DEFAULT NULL COMMENT '套餐原价',
  `service_package_unit` varchar(20) NOT NULL COMMENT '价格单位：次/项/天/月',
  `service_package_duration` varchar(100) DEFAULT NULL COMMENT '服务时长，如：4小时、26天、90天',
  `service_package_type` varchar(20) NOT NULL COMMENT '套餐类型：long-term-长周期套餐/count-card-次数次卡套餐',
  `service_description` text COMMENT '服务描述',
  `service_details` longtext COMMENT '详细服务内容，富文本格式',
  `service_process` longtext COMMENT '服务流程，富文本格式',
  `purchase_notice` text COMMENT '购买须知',
  `service_times` int(11) DEFAULT '1' COMMENT '服务次数',
  `unit_price` decimal(10,2) NOT NULL COMMENT '单价',
  `total_amount` decimal(10,2) NOT NULL COMMENT '订单总金额',
  `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠金额',
  `actual_amount` decimal(10,2) NOT NULL COMMENT '实付金额',
  `service_address` varchar(500) NOT NULL COMMENT '服务地址',
  `service_address_detail` varchar(200) DEFAULT NULL COMMENT '详细地址',
  `service_latitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址纬度',
  `service_longitude` decimal(10,6) DEFAULT NULL COMMENT '服务地址经度',
  `service_schedule` json DEFAULT NULL COMMENT '服务时间安排(JSON格式)',
  `practitioner_oneid` varchar(36) DEFAULT NULL COMMENT '服务人员OneID',
  `practitioner_name` varchar(50) DEFAULT NULL COMMENT '服务人员姓名',
  `practitioner_phone` varchar(20) DEFAULT NULL COMMENT '服务人员电话',
  `agency_id` bigint(20) DEFAULT NULL COMMENT '服务机构ID',
  `agency_name` varchar(200) DEFAULT NULL COMMENT '服务机构名称',
  `task_count` int(11) DEFAULT '0' COMMENT '任务总数',
  `completed_task_count` int(11) DEFAULT '0' COMMENT '已完成任务数',
  `task_progress` decimal(5,2) DEFAULT '0.00' COMMENT '任务进度百分比',
  `service_fee` decimal(10,2) DEFAULT NULL COMMENT '服务费',
  `agency_fee` decimal(10,2) DEFAULT NULL COMMENT '机构费',
  `platform_fee` decimal(10,2) DEFAULT NULL COMMENT '平台费',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_customer_name` (`customer_name`),
  KEY `idx_practitioner_oneId` (`practitioner_oneid`),
  KEY `idx_agency_id` (`agency_id`),
  KEY `idx_service_start_date` (`service_start_date`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_deleted` (`deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=2021 DEFAULT CHARSET=utf8mb4 COMMENT='家政服务订单详情表';

复购率 = (有复购行为的雇主数量 / 总雇主数量) × 100%
复购率统计计算:查询家政服务订单详情表阿姨所有历史订单  (有复购行为的雇主数量 / 总雇主数量) × 100% 
`customer_oneid` 是雇主的oneID
`practitioner_oneid` 是阿姨的OneID