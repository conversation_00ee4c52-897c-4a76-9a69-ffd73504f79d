

每个表需要包含以下几个字段：
`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
`creator` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updater` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
`tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号';


1. 合作伙伴表（publicbiz_partner）
```sql

CREATE TABLE `publicbiz_partner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '机构名称',
  `short_name` varchar(64) DEFAULT NULL COMMENT '机构简称',
  `type` varchar(32) NOT NULL DEFAULT '' COMMENT '机构类型',
  `biz` varchar(32) DEFAULT NULL COMMENT '业务模块',
  `status` varchar(32) NOT NULL DEFAULT '' COMMENT '合作状态',
  `risk` varchar(16) DEFAULT NULL COMMENT '风险等级',
  `owner` bigint(20) DEFAULT NULL COMMENT '我方负责人ID',
  `owner_name` varchar(30) DEFAULT NULL COMMENT '我方负责人姓名',
  `legal_person` varchar(64) DEFAULT NULL COMMENT '法人代表',
  `foundation_date` date DEFAULT NULL COMMENT '成立日期',
  `credit_code` varchar(64) DEFAULT NULL COMMENT '统一社会信用代码',
  `register_address` varchar(256) DEFAULT NULL COMMENT '注册地址',
  `business_address` varchar(256) DEFAULT NULL COMMENT '经营地址',
  `main_business` varchar(512) DEFAULT NULL COMMENT '主营业务',
  `contact_name` varchar(64) DEFAULT NULL COMMENT '主要联系人',
  `contact_phone` varchar(32) DEFAULT NULL COMMENT '联系电话',
  `rating` int(11) DEFAULT NULL COMMENT '当前评级（星级）',
  `cooperation_mode` varchar(32) DEFAULT NULL COMMENT '合作模式',
  `contract_no` varchar(64) DEFAULT NULL COMMENT '合同编号',
  `contract_start` date DEFAULT NULL COMMENT '合同开始日期',
  `contract_end` date DEFAULT NULL COMMENT '合同结束日期',
  `deposit` decimal(18,2) DEFAULT NULL COMMENT '保证金',
  `renew_date` date DEFAULT NULL COMMENT '续约提醒日期',
  `account_name` varchar(128) DEFAULT NULL COMMENT '对公账户名',
  `settlement_cycle` varchar(16) DEFAULT NULL COMMENT '结算周期',
  `bank_name` varchar(128) DEFAULT NULL COMMENT '开户银行',
  `bank_account` varchar(64) DEFAULT NULL COMMENT '银行账号',
  `qualification_file` varchar(512) DEFAULT NULL COMMENT '资质文件（URL或ID）',
  `invoice_type` varchar(16) DEFAULT NULL COMMENT '开票类型',
  `invoice_name` varchar(128) DEFAULT NULL COMMENT '开票名称',
  `tax_id` varchar(64) DEFAULT NULL COMMENT '纳税人识别号',
  `org_code` varchar(64) DEFAULT NULL COMMENT '社会组织代码',
  `invoice_address` varchar(256) DEFAULT NULL COMMENT '开票地址',
  `invoice_phone` varchar(32) DEFAULT NULL COMMENT '开票电话',
  `invoice_bank` varchar(128) DEFAULT NULL COMMENT '开票开户银行',
  `invoice_bank_account` varchar(64) DEFAULT NULL COMMENT '开票银行账号',
  `invoice_email` varchar(128) DEFAULT NULL COMMENT '开票邮箱',
  `invoice_contact` varchar(64) DEFAULT NULL COMMENT '开票联系人',
  `invoice_qualification_file` varchar(512) DEFAULT NULL COMMENT '开票资质文件（URL或ID）',
  `invoice_remark` varchar(512) DEFAULT NULL COMMENT '开票备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除，0-未删除，1-已删除',
  `creator` varchar(64) DEFAULT NULL COMMENT '创建者',
  `updater` varchar(64) DEFAULT NULL COMMENT '更新者',
  `tenant_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '租户编号',

  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合作伙伴';
```

```sql
-- 添加合作伙伴相关字段的 ALTER TABLE 语句
ALTER TABLE `publicbiz_partner`
    ADD COLUMN `agency_name` varchar(200) DEFAULT NULL COMMENT '关联机构名称' AFTER `agency_id`;
```



