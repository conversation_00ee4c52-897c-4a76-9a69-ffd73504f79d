# 机构管理模块API接口文档

## 1. 接口概述

### 1.1 基础信息
- **基础路径**: `/publicbiz/agency`
- **数据格式**: `application/json`
- **字符编码**: `UTF-8`

### 1.2 统一响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

 
### 1.3 响应状态码说明
- `200`: 操作成功
- `400`: 参数验证错误
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器错误

## 2. 接口列表

### 2.1 统计机构业务数据
- **接口地址**: `GET /publicbiz/agency/statistics`
- **接口描述**: 获取指定机构的业务统计数据
- **请求参数**: 

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| agencyId | Long | 是 | 机构ID | 1 |
| rangeType | String | 否 | 统计数量范围类型，可选值：30(30天)、90(90天)、year(年)、all(全部) |

**请求示例**：
```json
{
  "agencyId": 1,
  "rangeType": "30"
}
- **响应字段**: 

| 字段名 | 类型 | 说明 | 示例值 |
|--------|------|------|--------|
| serviceOrderCount | Long | 服务工单数 | 150 |
| interviewSuccessRate | BigDecimal | 面试成功率(%) | 85.5 |
| agencyRating | BigDecimal | 机构评分(1-5分) | 4.8 |
| complaintRate | BigDecimal | 客户投诉率(%) | 2.3 |
| totalPractitioners | Long | 在职阿姨总数 | 45 |
| newPractitioners | Long | 新增阿姨数 | 8 |
| flowPractitioners | Long | 流水阿姨数 | 12 |
| activeOrderPractitioners | Long | 上单阿姨数 | 25 |
| totalOrderAmount | BigDecimal | 订单总金额 | 125000.00 |
| ourIncome | BigDecimal | 我方收入 | 18750.00 |
| settledAmount | BigDecimal | 已结算金额 | 15000.00 |
| unsettledAmount | BigDecimal | 待结算金额 | 3750.00 |


#### 2.6.2 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data":{
  "serviceOrderCount": 150,
  "interviewSuccessRate": 85.5,
  "agencyRating": 4.8,
  "complaintRate": 2.3,
  "totalPractitioners": 45,
  "newPractitioners": 8,
  "flowPractitioners": 12,
  "activeOrderPractitioners": 25,
  "totalOrderAmount": 125000.00,
  "ourIncome": 18750.00,
  "settledAmount": 15000.00,
  "unsettledAmount": 3750.00
  }
}
```

### 2.2 获取机构业务趋势数据
- **接口地址**: `GET /publicbiz/agency/trend/{id}`
- **接口描述**: 获取指定机构的业务趋势数据
- **请求参数**:

| 参数名 | 类型 | 位置 | 必填 | 说明 | 示例值 |
|--------|------|------|------|------|--------|
| id | Long | Path | 是 | 机构ID | 1 |

- **响应字段**:

- **orderTrends**: 订单趋势数组
  - month: 月份，例如 `2024-01`
  - count: 数量，例如 `150`
- **serviceCategories**: 服务分类占比数组
  - serviceType: 服务类型，例如 `家政服务`
  - percentage: 占比(%)，BigDecimal，例如 `45.5`
- **serviceQualities**: 服务质量趋势数组
  - month: 月份，例如 `2024-01`
  - score: 评分，BigDecimal，例如 `4.8`

**响应示例**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "orderTrends": [
      { "month": "2024-01", "count": 150 },
      { "month": "2024-02", "count": 162 }
    ],
    "serviceCategories": [
      { "serviceType": "家政服务", "percentage": 45.5 },
      { "serviceType": "保洁服务", "percentage": 30.0 }
    ],
    "serviceQualities": [
      { "month": "2024-01", "score": 4.8 },
      { "month": "2024-02", "score": 4.7 }
    ]
  }
}
```
## 4. 错误码说明

### 4.1 通用错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 400 | 参数验证错误 | 检查请求参数格式和必填项 |
| 403 | 权限不足 | 确认用户具有相应操作权限 |
| 404 | 资源不存在 | 检查资讯ID是否正确 |
| 500 | 服务器错误 | 联系技术支持 |

### 4.2 业务错误码

| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| 1001 | 机构名称已存在 | 修改机构名称 |
| 1002 | 机构编号已存在 | 修改机构编号 |
| 1003 | 统一社会信用代码已存在 | 检查统一社会信用代码是否正确 |
| 1004 | 机构状态不允许操作 | 检查当前状态是否允许该操作 |
| 1005 | 机构不存在 | 检查机构ID是否正确 |
| 1006 | 统计时间范围无效 | 检查时间范围参数是否正确 |

## 5. 注意事项

1. **权限控制**: 所有接口都需要进行权限验证，确保用户具有相应操作权限
2. **数据验证**: 前端和后端都需要进行数据验证，确保数据完整性和正确性
3. **机构信息**: 机构名称、编号、统一社会信用代码等关键信息需要唯一性校验
4. **审核流程**: 机构审核需要严格按照审核流程执行，确保数据准确性
5. **统计性能**: 业务数据统计接口建议使用缓存优化，避免重复计算
6. **日志记录**: 重要操作需要记录操作日志，便于审计和问题排查
7. **数据安全**: 机构敏感信息需要进行脱敏处理，确保数据安全


## 6. 更新日志

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0.0 | 2024-01-20 | 初始版本，包含基础CRUD接口 |
| 1.1.0 | 2024-01-25 | 新增机构审核状态管理接口 |
| 1.2.0 | 2024-02-01 | 新增机构业务数据统计接口 |
| 1.3.0 | 2024-02-20 | 优化统计接口，支持多种时间范围统计 |
| 1.4.0 | 2024-02-25 | 完善机构管理功能，增加数据验证和权限控制 | 