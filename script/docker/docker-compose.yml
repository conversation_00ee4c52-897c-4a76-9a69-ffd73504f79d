version: '3'
services:
  bztmaster-gateway:
    image: bztmaster-gateway
    container_name: bztmaster-gateway
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=bztmaster-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/bztmaster-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host # 以主机网络环境运行
  bztmaster-system:
    image: bztmaster-module-system-biz
    container_name: bztmaster-system
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=bztmaster-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/bztmaster-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    healthcheck:
      test: [ "CMD","curl","-f","http://localhost:48081" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: always
    network_mode: host
  bztmaster-infra:
    image: bztmaster-module-infra-biz
    container_name: bztmaster-infra
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=bztmaster-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/bztmaster-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    healthcheck:
      test: [ "CMD","curl","-f","http://localhost:48082" ]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    depends_on:
      bztmaster-system:
        condition: service_healthy
  bztmaster-report:
    image: bztmaster-module-report-biz
    container_name: bztmaster-report
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=bztmaster-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/bztmaster-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      bztmaster-infra:
        condition: service_healthy
  bztmaster-bpm:
    image: bztmaster-module-bpm-biz
    container_name: bztmaster-bpm
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=bztmaster-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/bztmaster-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      bztmaster-infra:
        condition: service_healthy
  bztmaster-pay:
    image: bztmaster-module-pay-biz
    container_name: bztmaster-pay
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=bztmaster-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/bztmaster-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      bztmaster-infra:
        condition: service_healthy
  bztmaster-mp:
    image: bztmaster-module-mp-biz
    container_name: bztmaster-mp
    environment:
      - TZ=Asia/Shanghai # 配置程序默认时区为上海（中国标准时间）
      - JAVA_TOOL_OPTIONS=-javaagent:/data/skywalking/skywalking-agent/skywalking-agent.jar # 配置skywalking
      - SW_AGENT_NAME=bztmaster-gateway
      - SW_AGENT_TRACE_IGNORE_PATH=Redisson/PING,/actuator/**,/admin/**
      - SW_AGENT_COLLECTOR_BACKEND_SERVICES=[YOUR_SKYWALKING_ADDR] # 请替换 your.skywalking.addr 为你的 skywalking 地址
      - SPRING_PROFILES_ACTIVE=test # 指定程序运行环境
      - SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR=[YOUR_NACOS_ADDR] # 配置中心地址
      - SPRING_CLOUD_NACOS_CONFIG_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
      - SPRING_CLOUD_NACOS_SERVER_ADDR=[YOUR_NACOS_ADDR]  # 注册中心地址
      - SPRING_CLOUD_NACOS_DISCOVERY_NAMESPACE=[YOUR_NAMESPACE] # 命名空间
    volumes:
      - /docker/bztmaster-cloud/logs:/root/logs/
      - /data/skywalking/skywalking-agent:/data/skywalking/skywalking-agent
    restart: always
    network_mode: host
    depends_on:
      bztmaster-infra:
        condition: service_healthy