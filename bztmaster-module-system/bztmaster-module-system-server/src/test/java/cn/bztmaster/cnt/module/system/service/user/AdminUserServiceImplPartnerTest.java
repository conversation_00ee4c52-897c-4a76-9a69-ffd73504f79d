package cn.bztmaster.cnt.module.system.service.user;

import cn.bztmaster.cnt.framework.common.exception.ServiceException;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.test.core.ut.BaseDbUnitTest;
import cn.bztmaster.cnt.module.publicbiz.api.partner.PublicBizPartnerApi;
import cn.bztmaster.cnt.module.publicbiz.api.partner.dto.PartnerRespDTO;
import cn.bztmaster.cnt.module.system.controller.admin.user.vo.user.UserImportExcelVO;
import cn.bztmaster.cnt.module.system.controller.admin.user.vo.user.UserImportRespVO;
import cn.bztmaster.cnt.module.system.dal.dataobject.user.AdminUserDO;
import cn.bztmaster.cnt.module.system.dal.mysql.user.AdminUserMapper;
import cn.bztmaster.cnt.module.system.enums.user.AccountTypeEnum;
import cn.bztmaster.cnt.module.infra.api.config.ConfigApi;
import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

import static cn.bztmaster.cnt.module.system.enums.ErrorCodeConstants.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * {@link AdminUserServiceImpl} 合作伙伴相关功能的单元测试
 */
@Import(AdminUserServiceImpl.class)
public class AdminUserServiceImplPartnerTest extends BaseDbUnitTest {

    @Resource
    private AdminUserServiceImpl adminUserService;

    @Resource
    private AdminUserMapper adminUserMapper;

    @MockBean
    private PublicBizPartnerApi publicBizPartnerApi;

    @MockBean
    private ConfigApi configApi;

    @Test
    public void testImportUserList_EnterpriseUser_WithValidPartner_Success() {
        // 准备数据
        mockConfigApi();
        mockPartnerApi_ValidPartner();

        UserImportExcelVO importUser = new UserImportExcelVO();
        importUser.setUsername("testuser");
        importUser.setNickname("测试用户");
        importUser.setAccountType(AccountTypeEnum.ENTERPRISE_USER.getType());
        importUser.setPartnerName("测试合作伙伴");
        importUser.setMobile("***********");
        importUser.setEmail("<EMAIL>");
        importUser.setDeptId(1L);

        List<UserImportExcelVO> importUsers = Arrays.asList(importUser);

        // 执行导入
        UserImportRespVO result = adminUserService.importUserList(importUsers, false);

        // 验证结果
        assertEquals(1, result.getCreateUsernames().size());
        assertEquals("testuser", result.getCreateUsernames().get(0));
        assertTrue(result.getFailureUsernames().isEmpty());

        // 验证数据库中的用户信息
        AdminUserDO savedUser = adminUserMapper.selectByUsername("testuser");
        assertNotNull(savedUser);
        assertEquals(AccountTypeEnum.ENTERPRISE_USER.getType(), savedUser.getAccountType());
        assertEquals(1L, savedUser.getPartnerId());
        assertEquals("测试合作伙伴", savedUser.getPartnerName());
        assertEquals(100L, savedUser.getAgencyId());
        assertEquals("测试代理商", savedUser.getAgencyName());
    }

    @Test
    public void testImportUserList_EnterpriseUser_WithoutPartnerName_Failure() {
        // 准备数据
        mockConfigApi();

        UserImportExcelVO importUser = new UserImportExcelVO();
        importUser.setUsername("testuser");
        importUser.setNickname("测试用户");
        importUser.setAccountType(AccountTypeEnum.ENTERPRISE_USER.getType());
        // 不设置 partnerName
        importUser.setMobile("***********");
        importUser.setEmail("<EMAIL>");
        importUser.setDeptId(1L);

        List<UserImportExcelVO> importUsers = Arrays.asList(importUser);

        // 执行导入
        UserImportRespVO result = adminUserService.importUserList(importUsers, false);

        // 验证结果
        assertTrue(result.getCreateUsernames().isEmpty());
        assertEquals(1, result.getFailureUsernames().size());
        assertTrue(result.getFailureUsernames().containsKey("testuser"));
        assertEquals(USER_PARTNER_NAME_REQUIRED.getMsg(), result.getFailureUsernames().get("testuser"));
    }

    @Test
    public void testImportUserList_EnterpriseUser_WithInvalidPartner_Failure() {
        // 准备数据
        mockConfigApi();
        mockPartnerApi_InvalidPartner();

        UserImportExcelVO importUser = new UserImportExcelVO();
        importUser.setUsername("testuser");
        importUser.setNickname("测试用户");
        importUser.setAccountType(AccountTypeEnum.ENTERPRISE_USER.getType());
        importUser.setPartnerName("不存在的合作伙伴");
        importUser.setMobile("***********");
        importUser.setEmail("<EMAIL>");
        importUser.setDeptId(1L);

        List<UserImportExcelVO> importUsers = Arrays.asList(importUser);

        // 执行导入
        UserImportRespVO result = adminUserService.importUserList(importUsers, false);

        // 验证结果
        assertTrue(result.getCreateUsernames().isEmpty());
        assertEquals(1, result.getFailureUsernames().size());
        assertTrue(result.getFailureUsernames().containsKey("testuser"));
        assertTrue(result.getFailureUsernames().get("testuser").contains("不存在的合作伙伴"));
    }

    @Test
    public void testImportUserList_InternalEmployee_WithoutPartner_Success() {
        // 准备数据
        mockConfigApi();

        UserImportExcelVO importUser = new UserImportExcelVO();
        importUser.setUsername("testuser");
        importUser.setNickname("测试用户");
        importUser.setAccountType(AccountTypeEnum.INTERNAL_EMPLOYEE.getType());
        // 内部员工不需要设置合作伙伴
        importUser.setMobile("***********");
        importUser.setEmail("<EMAIL>");
        importUser.setDeptId(1L);

        List<UserImportExcelVO> importUsers = Arrays.asList(importUser);

        // 执行导入
        UserImportRespVO result = adminUserService.importUserList(importUsers, false);

        // 验证结果
        assertEquals(1, result.getCreateUsernames().size());
        assertEquals("testuser", result.getCreateUsernames().get(0));
        assertTrue(result.getFailureUsernames().isEmpty());

        // 验证数据库中的用户信息
        AdminUserDO savedUser = adminUserMapper.selectByUsername("testuser");
        assertNotNull(savedUser);
        assertEquals(AccountTypeEnum.INTERNAL_EMPLOYEE.getType(), savedUser.getAccountType());
        assertNull(savedUser.getPartnerId());
        assertNull(savedUser.getPartnerName());
        assertNull(savedUser.getAgencyId());
        assertNull(savedUser.getAgencyName());
    }

    private void mockConfigApi() {
        when(configApi.getConfigValueByKey("system.user.init-password"))
                .thenReturn(CommonResult.success("123456"));
    }

    private void mockPartnerApi_ValidPartner() {
        PartnerRespDTO partner = new PartnerRespDTO();
        partner.setId(1L);
        partner.setName("测试合作伙伴");
        partner.setAgencyId(100L);
        partner.setAgencyName("测试代理商");

        when(publicBizPartnerApi.getPartnerByName("测试合作伙伴"))
                .thenReturn(CommonResult.success(partner));
    }

    private void mockPartnerApi_InvalidPartner() {
        when(publicBizPartnerApi.getPartnerByName("不存在的合作伙伴"))
                .thenReturn(CommonResult.success(null));
    }
}
