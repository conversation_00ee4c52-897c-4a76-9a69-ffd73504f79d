package cn.bztmaster.cnt.module.system.controller.app.user.util;

import cn.hutool.core.util.StrUtil;

/**
 * 用户工具类
 *
 * <AUTHOR>
 */
public class UserUtils {

    /**
     * 手机号脱敏处理
     *
     * @param mobile 手机号
     * @return 脱敏后的手机号
     */
    public static String maskMobile(String mobile) {
        if (StrUtil.isEmpty(mobile) || mobile.length() != 11) {
            return mobile;
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }

    /**
     * 获取用户职位信息
     *
     * @param postIds 岗位ID集合
     * @return 职位名称
     */
    public static String getUserPosition(java.util.Set<Long> postIds) {
        // TODO: 根据岗位ID获取岗位名称，这里暂时返回默认值
        if (postIds != null && !postIds.isEmpty()) {
            return "店长"; // 这里应该根据实际岗位ID查询岗位名称
        }
        return null;
    }
}
