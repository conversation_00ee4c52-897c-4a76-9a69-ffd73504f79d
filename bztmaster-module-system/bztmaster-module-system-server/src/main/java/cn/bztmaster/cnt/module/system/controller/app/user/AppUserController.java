package cn.bztmaster.cnt.module.system.controller.app.user;

import cn.bztmaster.cnt.framework.common.pojo.CommonResult;
import cn.bztmaster.cnt.framework.common.util.validation.ValidationUtils;
import cn.bztmaster.cnt.module.system.controller.app.user.vo.AgencyUserInfoRespVO;
import cn.bztmaster.cnt.module.system.dal.dataobject.dept.DeptDO;
import cn.bztmaster.cnt.module.system.dal.dataobject.user.AdminUserDO;
import cn.bztmaster.cnt.module.system.enums.user.AccountTypeEnum;
import cn.bztmaster.cnt.module.system.service.dept.DeptService;
import cn.bztmaster.cnt.module.system.service.user.AdminUserService;
import cn.bztmaster.cnt.module.system.convert.app.AppUserConvert;
import cn.hutool.core.util.StrUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import static cn.bztmaster.cnt.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.bztmaster.cnt.framework.common.pojo.CommonResult.success;
import static cn.bztmaster.cnt.module.system.enums.ErrorCodeConstants.*;

/**
 * 机构端 - 用户管理
 *
 * <AUTHOR>
 */
@Tag(name = "机构端 - 用户管理")
@RestController
@RequestMapping("/system/app-user")
@Validated
@Slf4j
public class AppUserController {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;

    @GetMapping("/get-by-mobile")
    @Operation(summary = "根据手机号获取机构用户信息")
    @Parameter(name = "mobile", description = "手机号码", required = true, example = "13812345678")
    public CommonResult<AgencyUserInfoRespVO> getAgencyUserByMobile(@RequestParam("mobile") String mobile) {
        // 1. 参数校验
        if (StrUtil.isEmpty(mobile)) {
            throw exception(USER_MOBILE_NOT_EXISTS);
        }
        if (!ValidationUtils.isMobile(mobile)) {
            throw exception(USER_MOBILE_NOT_EXISTS);
        }

        // 2. 根据手机号查询用户
        AdminUserDO user = userService.getUserByMobile(mobile);
        if (user == null) {
            throw exception(USER_MOBILE_NOT_EXISTS);
        }

        // 3. 校验用户是否为机构用户
        if (user.getAccountType() == null || !AccountTypeEnum.ENTERPRISE_USER.getType().equals(user.getAccountType())) {
            throw exception(USER_NOT_EXISTS, "该账号不是机构用户，无法切换到机构端");
        }

        // 4. 校验用户状态
        if (user.getStatus() == null || user.getStatus() != 0) {
            throw exception(USER_IS_DISABLE, user.getNickname());
        }

        // 5. 校验机构信息
        if (user.getAgencyId() == null) {
            throw exception(USER_NOT_EXISTS, "用户未关联机构，请联系管理员");
        }

        // 6. 获取部门信息
        DeptDO dept = null;
        if (user.getDeptId() != null) {
            dept = deptService.getDept(user.getDeptId());
        }

        // 7. 构建响应数据
        AgencyUserInfoRespVO respVO = AppUserConvert.INSTANCE.convert(user, dept);

        return success(respVO);
    }
}
