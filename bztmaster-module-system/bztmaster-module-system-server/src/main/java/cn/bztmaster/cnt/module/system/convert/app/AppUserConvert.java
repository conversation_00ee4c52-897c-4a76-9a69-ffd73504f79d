package cn.bztmaster.cnt.module.system.convert.app;

import cn.bztmaster.cnt.framework.common.util.object.BeanUtils;
import cn.bztmaster.cnt.module.system.controller.app.user.vo.AgencyUserInfoRespVO;
import cn.bztmaster.cnt.module.system.dal.dataobject.dept.DeptDO;
import cn.bztmaster.cnt.module.system.dal.dataobject.user.AdminUserDO;
import cn.bztmaster.cnt.module.system.controller.app.user.util.UserUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.List;

/**
 * 机构端用户转换器
 *
 * <AUTHOR>
 */
@Mapper
public interface AppUserConvert {

    AppUserConvert INSTANCE = Mappers.getMapper(AppUserConvert.class);

    /**
     * 转换用户信息
     *
     * @param user 用户DO
     * @param dept 部门DO
     * @return 机构用户信息VO
     */
    default AgencyUserInfoRespVO convert(AdminUserDO user, DeptDO dept) {
        if (user == null) {
            return null;
        }
        
        AgencyUserInfoRespVO respVO = new AgencyUserInfoRespVO();
        respVO.setUserId(user.getId());
        respVO.setAccountType(user.getAccountType());
        respVO.setUsername(user.getUsername());
        respVO.setNickname(user.getNickname());
        respVO.setMobile(UserUtils.maskMobile(user.getMobile()));
        respVO.setAvatar(user.getAvatar());
        respVO.setPosition(UserUtils.getUserPosition(user.getPostIds()));
        respVO.setDepartment(dept != null ? dept.getName() : null);
        respVO.setAgencyId(user.getAgencyId());
        respVO.setAgencyName(user.getAgencyName());
        respVO.setPartnerId(user.getPartnerId());
        respVO.setPartnerName(user.getPartnerName());
        respVO.setStatus(user.getStatus());
        respVO.setWorkStatusType(1); // 默认全职，后续可以从数据库获取
        respVO.setPermissions(getUserPermissions(user.getId()));
        respVO.setLastLoginTime(user.getLoginDate());
        
        return respVO;
    }

    /**
     * 获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    default List<String> getUserPermissions(Long userId) {
        // TODO: 根据用户ID获取权限列表，这里暂时返回默认权限
        return Arrays.asList("order_manage", "staff_manage", "finance_view");
    }
}
