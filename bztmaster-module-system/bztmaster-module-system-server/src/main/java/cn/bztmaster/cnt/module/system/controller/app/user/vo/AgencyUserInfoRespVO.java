package cn.bztmaster.cnt.module.system.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 机构用户信息响应 VO
 *
 * <AUTHOR>
 */
@Schema(description = "机构用户信息响应 VO")
@Data
public class AgencyUserInfoRespVO {

    @Schema(description = "用户ID", example = "123")
    private Long userId;

    @Schema(description = "账户类型", example = "2")
    private Integer accountType;

    @Schema(description = "用户账号", example = "zhang_manager")
    private String username;

    @Schema(description = "用户昵称", example = "张经理")
    private String nickname;

    @Schema(description = "手机号（脱敏）", example = "138****5678")
    private String mobile;

    @Schema(description = "头像地址", example = "https://example.com/avatar.jpg")
    private String avatar;

    @Schema(description = "职位", example = "店长")
    private String position;

    @Schema(description = "部门", example = "运营部")
    private String department;

    @Schema(description = "机构ID", example = "456")
    private Long agencyId;

    @Schema(description = "机构名称", example = "XX家政服务有限公司")
    private String agencyName;

    @Schema(description = "合作伙伴ID", example = "789")
    private Long partnerId;

    @Schema(description = "合作伙伴名称", example = "XX合作伙伴")
    private String partnerName;

    @Schema(description = "账号状态", example = "0")
    private Integer status;

    @Schema(description = "工作状态类型", example = "1")
    private Integer workStatusType;

    @Schema(description = "权限列表", example = "[\"order_manage\", \"staff_manage\"]")
    private List<String> permissions;

    @Schema(description = "最后登录时间", example = "2024-01-15 10:30:00")
    private LocalDateTime lastLoginTime;
}
