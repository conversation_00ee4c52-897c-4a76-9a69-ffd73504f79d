<template>
  <div class="talent-detail-wrapper">
    <el-divider class="divider" />
    <div class="profile-row">
      <el-avatar size="80" class="avatar">张</el-avatar>
      <div class="profile-info">
        <div class="profile-name">
          张三
          <el-tag type="success" size="small" class="real-tag">已实名</el-tag>
        </div>
        <div class="profile-base-info">
          <span>身份证：101011990307123X</span>
          <span>手机号：13812345678</span>
          <span>邮箱：<EMAIL></span>
          <span>性别：女</span>
          <span>出生日期：1990-03-07</span>
        </div>
      </div>
    </div>
    <div class="section-title">人才标签云</div>
    <div class="tag-cloud">
      <el-tag class="tag-item" type="info">认证:高级母婴护理</el-tag>
      <el-tag class="tag-item" type="primary">技能:催乳</el-tag>
      <el-tag class="tag-item" type="info">技能:高级收纳</el-tag>
      <el-tag class="tag-item">评价:有耐心</el-tag>
      <el-tag class="tag-item">经验:服务过30个家庭</el-tag>
      <el-tag class="tag-item" type="info">学历:本科</el-tag>
    </div>
    <el-divider class="divider" />
    <div class="section-title" style="margin-top: 24px;">生命周期时间轴</div>
    <el-timeline class="timeline-box">
      <el-timeline-item timestamp="2024-06-10">
        <b>[就业]</b> 获得李女士五星好评
      </el-timeline-item>
      <el-timeline-item timestamp="2024-01-15">
        <b>[认证]</b> 考取金牌月嫂证书
      </el-timeline-item>
      <el-timeline-item timestamp="2023-12-20">
        <b>[培训]</b> 完成金牌月嫂培训课程
      </el-timeline-item>
      <el-timeline-item timestamp="2023-08-01">
        <b>[实习]</b> 在爱心家政完成3个月实习
      </el-timeline-item>
      <el-timeline-item timestamp="2022-07-15">
        <b>[实践]</b> 参与XX大学暑期社会实践
      </el-timeline-item>
      <el-timeline-item timestamp="2022-06-30">
        <b>[教育]</b> 毕业于XX大学
      </el-timeline-item>
    </el-timeline>
    <el-divider class="divider" />
    <div class="detail-archive-card">
      <div class="archive-title">详细档案</div>
      <el-tabs v-model="activeTab" class="archive-tabs">
        <el-tab-pane label="教育背景" name="education">
          <div class="edu-block">
            <div class="edu-school">XX大学</div>
            <div class="edu-info">学位类型: 本科　专业: 护理学　在校时间: 2018-09-01 ~ 2022-06-30　学业排名: Top 10%</div>
            <el-divider class="edu-divider" />
          </div>
          <div class="edu-block">
            <div class="edu-school">YY师范大学</div>
            <div class="edu-info">学位类型: 硕士　专业: 教育心理学　在校时间: 2022-09-01 ~ 2025-06-30　学业排名: Top 5%</div>
            <el-divider class="edu-divider" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="实践与项目" name="practice">
          <div class="practice-block">
            <div class="practice-title">[实习] 爱心家政</div>
            <div class="practice-info">岗位: 实习月嫂　时间: 2023-05-01 ~ 2023-08-01</div>
            <div class="practice-desc">职责: 负责新生儿日常护理，产妇饮食调理等。</div>
          </div>
          <div class="practice-block">
            <div class="practice-title">[校园实践] XX大学暑期社会实践</div>
            <div class="practice-info">组织方: XX大学团委　时间: 2021-07-01 ~ 2021-08-31</div>
            <div class="practice-desc">总结: 赴敬老院进行支教活动，锻炼了沟通和组织能力。</div>
          </div>
          <div class="practice-block">
            <div class="practice-title">[项目] 个人作品集网站</div>
            <div class="practice-desc">描述: 使用React和Node.js开发的个人项目，展示了前端开发技能。</div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="培训与技能" name="training">
          <div class="train-block">
            <div class="train-title">[培训] 金牌月嫂培训课程</div>
            <div class="train-info">培训机构: 汇成平台　完成日期: 2023-12-20</div>
          </div>
          <div class="train-block">
            <div class="train-title">[培训] 高级Office办公软件应用</div>
            <div class="train-info">培训机构: 某某在线教育　完成日期: 2022-03-15</div>
          </div>
          <div class="train-block">
            <div class="train-title">[技能] 催乳</div>
            <div class="train-info">掌握程度: 精通</div>
          </div>
          <div class="train-block">
            <div class="train-title">[技能] 高级收纳</div>
            <div class="train-info">掌握程度: 熟悉</div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="认证与资质" name="certificate">
          <div class="cert-block">
            <div class="cert-title">金牌月嫂证书</div>
            <div class="cert-info">发证机构: 汇成平台认证　颁发日期: 2024-01-15　记录来源: 平台录入</div>
            <div class="cert-status pass">审核状态: 已认证</div>
          </div>
          <div class="cert-block">
            <div class="cert-title">高级母婴护理师证</div>
            <div class="cert-info">发证机构: XX市人社局　颁发日期: 2023-05-10　记录来源: 个人申报</div>
            <div class="cert-status wait">审核状态: 待认证</div>
          </div>
          <div class="cert-block">
            <div class="cert-title">全国计算机等级考试一级</div>
            <div class="cert-info">发证机构: 教育部考试中心　颁发日期: 2019-09-25　记录来源: 个人申报</div>
            <div class="cert-status reject">审核状态: 已驳回</div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="求职与工作" name="job">
          <div class="job-block">
            <div class="job-title">[工作] 在爱心家政服务</div>
            <div class="job-info">职位: 金牌月嫂　任职时间: 2024-02-01 ~ 至今　薪资: 12000元/月</div>
          </div>
          <div class="job-block">
            <div class="job-title">[工作] 星光文具店</div>
            <div class="job-info">职位: 店员　任职时间: 2017-06-01 ~ 2018-08-30　薪资: 4000元/月</div>
          </div>
          <div class="job-block">
            <div class="job-title">[求职] XX家政公司</div>
            <div class="job-info">申请岗位: 高级育儿嫂　申请日期: 2024-01-20　状态: 面试中</div>
          </div>
          <div class="job-block">
            <div class="job-title">[求职] 阳光幼儿园</div>
            <div class="job-info">申请岗位: 助教　申请日期: 2023-12-15　状态: 不合适</div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="用户评价" name="comment">
          <div class="comment-block">
            <div class="comment-title">雇主: 李女士 (订单: 20240501A)</div>
            <div class="comment-info">服务项目: 金牌月嫂　评价时间: 2024-06-10　评分: <span class="star">★★★★★</span></div>
            <div class="comment-content">张姐非常专业，有耐心，把我和宝宝都照顾得很好，推荐！</div>
          </div>
          <div class="comment-block">
            <div class="comment-title">培训师: 王老师 (课程: 金牌月嫂培训)</div>
            <div class="comment-info">培训课程: 金牌月嫂认证　评价时间: 2023-12-25　评分: <span class="star">★★★★★</span></div>
            <div class="comment-content">学习态度认真，实践能力强，与同学相处融洽。</div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- 隐藏导出用div，平铺所有内容 -->
    <div ref="pdfAllTabsRef" class="pdf-export-wrapper" style="display:none">
      <div class="header-title">人才画像详情</div>
      <el-divider class="divider" />
      <div class="profile-row">
        <el-avatar size="80" class="avatar">张</el-avatar>
        <div class="profile-info">
          <div class="profile-name">
            张三
            <el-tag type="success" size="small" class="real-tag">已实名</el-tag>
          </div>
          <div class="profile-base-info">
            <span>身份证：101011990307123X</span>
            <span>手机号：13812345678</span>
            <span>邮箱：<EMAIL></span>
            <span>性别：女</span>
            <span>出生日期：1990-03-07</span>
          </div>
        </div>
      </div>
      <div class="section-title">人才标签云</div>
      <div class="tag-cloud">
        <el-tag class="tag-item" type="info">认证:高级母婴护理</el-tag>
        <el-tag class="tag-item" type="primary">技能:催乳</el-tag>
        <el-tag class="tag-item" type="info">技能:高级收纳</el-tag>
        <el-tag class="tag-item">评价:有耐心</el-tag>
        <el-tag class="tag-item">经验:服务过30个家庭</el-tag>
        <el-tag class="tag-item" type="info">学历:本科</el-tag>
      </div>
      <el-divider class="divider" />
      <div class="section-title" style="margin-top: 24px;">生命周期时间轴</div>
      <el-timeline class="timeline-box">
        <el-timeline-item timestamp="2024-06-10">
          <b>[就业]</b> 获得李女士五星好评
        </el-timeline-item>
        <el-timeline-item timestamp="2024-01-15">
          <b>[认证]</b> 考取金牌月嫂证书
        </el-timeline-item>
        <el-timeline-item timestamp="2023-12-20">
          <b>[培训]</b> 完成金牌月嫂培训课程
        </el-timeline-item>
        <el-timeline-item timestamp="2023-08-01">
          <b>[实习]</b> 在爱心家政完成3个月实习
        </el-timeline-item>
        <el-timeline-item timestamp="2022-07-15">
          <b>[实践]</b> 参与XX大学暑期社会实践
        </el-timeline-item>
        <el-timeline-item timestamp="2022-06-30">
          <b>[教育]</b> 毕业于XX大学
        </el-timeline-item>
      </el-timeline>
      <el-divider class="divider" />
      <div class="detail-archive-card" style="margin-left:0;margin-right:0;">
        <div class="archive-title">详细档案</div>
        <div class="tab-export-block">
          <!-- 教育背景 -->
          <div class="tab-title">教育背景</div>
          <div class="edu-block">
            <div class="edu-school">XX大学</div>
            <div class="edu-info">学位类型: 本科　专业: 护理学　在校时间: 2018-09-01 ~ 2022-06-30　学业排名: Top 10%</div>
          </div>
          <div class="edu-block">
            <div class="edu-school">YY师范大学</div>
            <div class="edu-info">学位类型: 硕士　专业: 教育心理学　在校时间: 2022-09-01 ~ 2025-06-30　学业排名: Top 5%</div>
          </div>
          <!-- 实践与项目 -->
          <div class="tab-title">实践与项目</div>
          <div class="practice-block">
            <div class="practice-title">[实习] 爱心家政</div>
            <div class="practice-info">岗位: 实习月嫂　时间: 2023-05-01 ~ 2023-08-01</div>
            <div class="practice-desc">职责: 负责新生儿日常护理，产妇饮食调理等。</div>
          </div>
          <div class="practice-block">
            <div class="practice-title">[校园实践] XX大学暑期社会实践</div>
            <div class="practice-info">组织方: XX大学团委　时间: 2021-07-01 ~ 2021-08-31</div>
            <div class="practice-desc">总结: 赴敬老院进行支教活动，锻炼了沟通和组织能力。</div>
          </div>
          <div class="practice-block">
            <div class="practice-title">[项目] 个人作品集网站</div>
            <div class="practice-desc">描述: 使用React和Node.js开发的个人项目，展示了前端开发技能。</div>
          </div>
          <!-- 培训与技能 -->
          <div class="tab-title">培训与技能</div>
          <div class="train-block">
            <div class="train-title">[培训] 金牌月嫂培训课程</div>
            <div class="train-info">培训机构: 汇成平台　完成日期: 2023-12-20</div>
          </div>
          <div class="train-block">
            <div class="train-title">[培训] 高级Office办公软件应用</div>
            <div class="train-info">培训机构: 某某在线教育　完成日期: 2022-03-15</div>
          </div>
          <div class="train-block">
            <div class="train-title">[技能] 催乳</div>
            <div class="train-info">掌握程度: 精通</div>
          </div>
          <div class="train-block">
            <div class="train-title">[技能] 高级收纳</div>
            <div class="train-info">掌握程度: 熟悉</div>
          </div>
          <!-- 认证与资质 -->
          <div class="tab-title">认证与资质</div>
          <div class="cert-block">
            <div class="cert-title">金牌月嫂证书</div>
            <div class="cert-info">发证机构: 汇成平台认证　颁发日期: 2024-01-15　记录来源: 平台录入</div>
            <div class="cert-status pass">审核状态: 已认证</div>
          </div>
          <div class="cert-block">
            <div class="cert-title">高级母婴护理师证</div>
            <div class="cert-info">发证机构: XX市人社局　颁发日期: 2023-05-10　记录来源: 个人申报</div>
            <div class="cert-status wait">审核状态: 待认证</div>
          </div>
          <div class="cert-block">
            <div class="cert-title">全国计算机等级考试一级</div>
            <div class="cert-info">发证机构: 教育部考试中心　颁发日期: 2019-09-25　记录来源: 个人申报</div>
            <div class="cert-status reject">审核状态: 已驳回</div>
          </div>
          <!-- 求职与工作 -->
          <div class="tab-title">求职与工作</div>
          <div class="job-block">
            <div class="job-title">[工作] 在爱心家政服务</div>
            <div class="job-info">职位: 金牌月嫂　任职时间: 2024-02-01 ~ 至今　薪资: 12000元/月</div>
          </div>
          <div class="job-block">
            <div class="job-title">[工作] 星光文具店</div>
            <div class="job-info">职位: 店员　任职时间: 2017-06-01 ~ 2018-08-30　薪资: 4000元/月</div>
          </div>
          <div class="job-block">
            <div class="job-title">[求职] XX家政公司</div>
            <div class="job-info">申请岗位: 高级育儿嫂　申请日期: 2024-01-20　状态: 面试中</div>
          </div>
          <div class="job-block">
            <div class="job-title">[求职] 阳光幼儿园</div>
            <div class="job-info">申请岗位: 助教　申请日期: 2023-12-15　状态: 不合适</div>
          </div>
          <!-- 用户评价 -->
          <div class="tab-title">用户评价</div>
          <div class="comment-block">
            <div class="comment-title">雇主: 李女士 (订单: 20240501A)</div>
            <div class="comment-info">服务项目: 金牌月嫂　评价时间: 2024-06-10　评分: <span class="star">★★★★★</span></div>
            <div class="comment-content">张姐非常专业，有耐心，把我和宝宝都照顾得很好，推荐！</div>
          </div>
          <div class="comment-block">
            <div class="comment-title">培训师: 王老师 (课程: 金牌月嫂培训)</div>
            <div class="comment-info">培训课程: 金牌月嫂认证　评价时间: 2023-12-25　评分: <span class="star">★★★★★</span></div>
            <div class="comment-content">学习态度认真，实践能力强，与同学相处融洽。</div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer-btn">
      <el-button type="primary" @click="exportPdf"><el-icon><Document /></el-icon>导出简历</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue'
import { Document } from '@element-plus/icons-vue'
import * as html2pdf from 'html2pdf.js'
const activeTab = ref('education')
const pdfDetailRef = ref()
const pdfAllTabsRef = ref()
const talentName = ref('张三')

const exportPdf = async () => {
  // 隐藏导出按钮，防止导出到PDF
  const footerBtn = document.querySelector('.footer-btn')
  if (footerBtn) footerBtn.style.display = 'none'
  // 显示所有tab内容的div
  pdfAllTabsRef.value.style.display = 'block'
  await nextTick()
  html2pdf.default()
    .set({
      margin: 0,
      filename: `${talentName.value}个人简历.pdf`,
      html2canvas: { scale: 2, useCORS: true },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    })
    .from(pdfAllTabsRef.value)
    .save()
    .then(() => {
      if (footerBtn) footerBtn.style.display = ''
      pdfAllTabsRef.value.style.display = 'none'
    })
}
</script>

<style scoped>
.talent-detail-wrapper {
  background: #fff;
  padding: 20px 40px 24px 40px;
  min-width: 480px;
  min-height: 0;
  box-sizing: border-box;
  overflow-y: auto;
  position: relative;
  z-index: 10;
}
.divider {
  margin: 12px 0;
  border-color: #f0f0f0;
}
.header-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 16px;
}
.profile-row {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}
.avatar {
  margin-right: 32px;
  font-size: 32px;
  background: #409EFF;
  color: #fff;
}
.profile-info {
  flex: 1;
}
.profile-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
}
.real-tag {
  margin-left: 8px;
}
.profile-base-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px 32px;
  color: #666;
  font-size: 14px;
}
.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 24px 0 12px 0;
}
.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 12px 16px;
  margin-bottom: 8px;
}
.tag-item {
  font-size: 13px;
  padding: 2px 12px;
  border-radius: 16px;
}
.timeline-box {
  margin-top: 8px;
  margin-bottom: 32px;
}
.footer-btn {
  display: flex;
  justify-content: flex-end;
  margin-top: 0;
  margin-bottom: 0;
}
.detail-archive-card {
  background: #fafbfc;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  padding: 16px 5px 8px 5px;
  margin: 16px 5px 24px 5px;
  border: none;
}
.archive-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 18px;
}
.archive-tabs {
  margin-bottom: 0;
}
.edu-block {
  margin-bottom: 18px;
}
.edu-school {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 4px;
}
.edu-info {
  color: #666;
  font-size: 13px;
}
.edu-divider {
  margin: 12px 0 0 0;
  border-color: #f0f0f0;
}
.practice-block, .train-block, .cert-block, .job-block, .comment-block {
  background: #fff;
  margin-bottom: 16px;
  padding: 0 0 8px 0;
}
.practice-title, .train-title, .cert-title, .job-title, .comment-title {
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 2px;
}
.practice-info, .train-info, .cert-info, .job-info, .comment-info {
  color: #666;
  font-size: 13px;
  margin-bottom: 2px;
}
.practice-desc, .comment-content {
  color: #444;
  font-size: 13px;
}
.cert-status {
  font-size: 13px;
  margin-top: 2px;
  display: inline-block;
  padding: 0 8px;
  border-radius: 10px;
}
.cert-status.pass {
  color: #1abc9c;
  background: #e8f8f5;
}
.cert-status.wait {
  color: #e6a23c;
  background: #fdf6ec;
}
.cert-status.reject {
  color: #f56c6c;
  background: #fef0f0;
}
.star {
  color: #f39c12;
  font-size: 15px;
  letter-spacing: 1px;
}
</style>
